----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    15:27:27 08/25/2023 
-- Design Name: 
-- Module Name:    FMG_MODE - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;

use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

--fgm_mode_set:33 33 33 aa bcde  aa:01表示轮询，00表示单探头；bcde:1表示工作，b表示探头4，e表示探头1
--fgm_para_set:EE xxxx yyyy zz   xxxx:表示继电器切换间隔s；yyyy:表示继电器的切换次数 zz：轮询结束需切换回的单探头；发完该指令后再确认调整。
--默认工作模式：轮询工作模式，切换间隔为1800s（半小时）, 切换次数为24*4=96次，工作两天

entity FMG_MODE is
port (
    reset   : in std_logic;
    clk     : in std_logic;
	
	init_fgm: in std_logic;
    fgm_data_in	: in std_logic_vector(47 downto 0);
	fgm_change  : in std_logic;
	s_edge      : in std_logic;
	
	fgm_chanel  : out std_logic_vector(7 downto 0); --探头0，1，2，3
	fgm_chanel_change:out std_logic;
    
    fgm_state:   out std_logic_vector(3 downto 0);
    fgm_state_change:	out std_logic
);

end FMG_MODE;

architecture Behavioral of FMG_MODE is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal init_fgm_reg:std_logic;
signal init_fgm_edge:std_logic;

signal fgm_mode:std_logic_vector(7 downto 0);--工作模式，01：轮询，00：单探头
signal fgm_mode_set:std_logic;
signal fgm_para_set: std_logic;
signal fgm1_state:std_logic_vector(3 downto 0);
signal fgm2_state:std_logic_vector(3 downto 0);
signal fgm3_state:std_logic_vector(3 downto 0);
signal fgm4_state:std_logic_vector(3 downto 0);

signal fgm_time:std_logic_vector(15 downto 0);
signal fgm_count:std_logic_vector(15 downto 0);
signal fgm_state_old:std_logic_vector(7 downto 0);

signal wait_time:std_logic_vector(15 downto 0);
signal wait_count:std_logic_vector(15 downto 0);
signal fgm_change_process:std_logic;

--signal init_pro:std_logic;

type state_t is (start_s,check_s,wait_s,d_mode_s,lx_mode_s1,wait_s1,lx_mode_s2,wait_s2,lx_mode_s3,wait_s3,lx_mode_s4,wait_s4,check_count,old_work_s);
signal state: state_t;

begin

process(reset,clk)
	begin
		if reset='0' then
            init_fgm_reg<='0';			
		else
		    if clk='1' and clk'event then
			    init_fgm_reg<=init_fgm;
			end if;
		end if;
	end process;

process(reset,clk)
	begin
		if reset='0' then
            init_fgm_edge<='0';			
		else
		    if clk='1' and clk'event then
			    if init_fgm='1' and init_fgm_reg='0' then 
				    init_fgm_edge<='1';
				else 
				    init_fgm_edge<='0';
				end if;
			end if;
		end if;
	end process;
	
    process(reset,clk)
	begin
		if reset ='0' then
			fgm_mode <=x"00";
			fgm_mode_set<='0';
			fgm1_state<=x"0";
			fgm2_state<=x"0";
			fgm3_state<=x"0";
			fgm4_state<=x"0";
            fgm_time<=(others=>'0');	
            fgm_count<=(others=>'0');
            fgm_state_old<=(others=>'0');	
            fgm_para_set<='0';			
	    else 
			if clk='1' and clk'event then
			--初始化
			    if  init_fgm_edge='1' then  
                    fgm_mode<=x"01";
			    	fgm1_state<=x"1";
			    	fgm2_state<=x"1";
			    	fgm3_state<=x"1";
			    	fgm4_state<=x"1";
			    	fgm_mode_set<='1';
                    fgm_time<=x"0384";
--                    fgm_time<=x"0001";					
				    fgm_count<=x"0030";
					fgm_state_old<=x"01";
		        else 
				    if fgm_change='1' then 
				    --磁场工作模式设置
			            if fgm_data_in(47 downto 24)=x"333333" then 
			    	        fgm_mode<=fgm_data_in(23 downto 16);
			    	    	fgm4_state<=fgm_data_in(15 downto 12);
			    	    	fgm3_state<=fgm_data_in(11 downto 8);
			    	    	fgm2_state<=fgm_data_in(7 downto 4);
			    	    	fgm1_state<=fgm_data_in(3 downto 0);
			    	    	fgm_mode_set<='1';
					    --磁场工作参数设置
			    	    elsif fgm_data_in(47 downto 40)=x"EE" then
                            fgm_time<=fgm_data_in(39 downto 24)-1;	--继电器切换间隔
                            fgm_count<=fgm_data_in(23 downto 8)-1;	--继电器切换次数
                            fgm_state_old<=fgm_data_in(7 downto 0);	    --回归状态
					    	fgm_para_set<='1';
                        else
			    	        fgm_mode_set<='0';fgm_para_set<='0';
			    	    end if;
					else 
					    fgm_mode_set<='0';fgm_para_set<='0'; 
					end if;
			    end if;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
        if reset ='0' then
        	fgm_chanel<=(others=>'0');fgm_chanel_change<='0';wait_time<=(others=>'0'); state<=start_s; wait_count<=(others=>'0');fgm_change_process<='0';
	    else 
			if clk='1' and clk'event then
			    if fgm_mode_set='1' then state<=start_s; fgm_change_process<='1';
				else
		            case state is
				        when start_s =>
							fgm_chanel_change<='0';wait_time<=(others=>'0');wait_count<=(others=>'0');
							if fgm_change_process='1' then 
							    state<=check_s; 
							end if;
				    	when check_s =>
				    	    fgm_change_process<='0';
--进入单探头工作模式							
				    		if fgm_mode=x"00" then 
				    		    state<=d_mode_s;
--进入轮询工作模式
				    		elsif fgm_mode=x"01" then 
				    		    state<=wait_s;
							else 
							    state<=start_s;
				    		end if;
				    	when d_mode_s=>
				    	    if fgm1_state=x"1" then fgm_chanel<=x"01";
				    		elsif fgm2_state=x"1" then fgm_chanel<=x"02";
				    		elsif fgm3_state=x"1" then fgm_chanel<=x"03";
				    		elsif fgm4_state=x"1" then fgm_chanel<=x"04";
				    		else  fgm_chanel<=x"01";
				    		end if;                        
				    		fgm_chanel_change<='1';state<=start_s;					
				    	when wait_s=>
						    if s_edge='1' then 
							    state<=lx_mode_s1;
							end if;
						when lx_mode_s1=>
				    	    if fgm1_state=x"1" then 
				    		    fgm_chanel<=x"01"; fgm_chanel_change<='1'; state<=wait_s1;
				    		else 
				    		    state<=lx_mode_s2;
				    		end if;
				    	when wait_s1=>
				    	    fgm_chanel_change<='0'; 
				    	    if s_edge='1' then 
				    	        if wait_time=fgm_time then  state<=lx_mode_s2; wait_time<=(others=>'0');  --wait_time 要不要减1
				    			else wait_time<=wait_time+1;
								end if;
				    		end if;
				    	when lx_mode_s2=>
				    	    if fgm2_state=x"1" then 
				    		    fgm_chanel<=x"02"; fgm_chanel_change<='1'; state<=wait_s2;
				    		else 
				    		    state<=lx_mode_s3;
				    		end if;
				    	when wait_s2=>
				    	    fgm_chanel_change<='0'; 
				    	    if s_edge='1' then 
				    	        if wait_time=fgm_time then  state<=lx_mode_s3;wait_time<=(others=>'0');
				    			else wait_time<=wait_time+1;
								end if;
				    		end if;	    
				    	when lx_mode_s3=>
				    	    if fgm3_state=x"1" then 
				    		    fgm_chanel<=x"03"; fgm_chanel_change<='1'; state<=wait_s3;
				    		else 
				    		    state<=lx_mode_s4;
				    		end if;	
				    	when wait_s3=>
				    	    fgm_chanel_change<='0'; 
				    	    if s_edge='1' then 
				    	        if wait_time=fgm_time then  state<=lx_mode_s4;wait_time<=(others=>'0');
				    			else wait_time<=wait_time+1;
								end if;
				    		end if;	    	
				    	when lx_mode_s4=>
				    	    if fgm4_state=x"1" then 
				    		    fgm_chanel<=x"04"; fgm_chanel_change<='1'; state<=wait_s4;
				    		else 
				    		    state<=check_count;
				    		end if;	
				    	when wait_s4=>
				    	    fgm_chanel_change<='0'; 
				    	    if s_edge='1' then 
				    	        if wait_time=fgm_time then  state<=check_count;wait_time<=(others=>'0');
				    			else wait_time<=wait_time+1;
								end if;
				    		end if;		   
	                    when check_count=>
				    	    if wait_count=fgm_count then 
				    		    wait_count<=(others=>'0');state<=old_work_s;
				    	    else 
				    		    wait_count<=wait_count+1;state<=lx_mode_s1;
				    		end if;
	                    when old_work_s=>
				    	    fgm_chanel<=fgm_state_old; fgm_chanel_change<='1'; state<=start_s;
                        when others => state<=start_s;
				    end case;
				end if;
			end if;
		end if;
	end process;
	
	process(clk,reset)
	begin
		if reset='0' then
			fgm_state<=x"0";
			fgm_state_change<='0';
		else
			if clk'event and clk='1' then
                if fgm_mode_set='1' then
                    if fgm1_state=x"1" then  fgm_state(0)<='1'; else fgm_state(0)<='0';end if;
					if fgm2_state=x"1" then  fgm_state(1)<='1'; else fgm_state(1)<='0';end if;
					if fgm3_state=x"1" then  fgm_state(2)<='1'; else fgm_state(2)<='0';end if;
					if fgm4_state=x"1" then  fgm_state(3)<='1'; else fgm_state(3)<='0';end if;
					fgm_state_change<='1';
				else 
				    if state=old_work_s then 
					    if fgm_state_old=x"01" then fgm_state<=x"1";fgm_state_change<='1';
						elsif fgm_state_old=x"02" then fgm_state<=x"2";fgm_state_change<='1';
					    elsif fgm_state_old=x"03" then fgm_state<=x"4"; fgm_state_change<='1';
				        elsif fgm_state_old=x"04" then fgm_state<=x"8"; fgm_state_change<='1';
						else fgm_state_change<='0';
						end if;
					else 
					    fgm_state_change<='0';
					end if;
				end if;				
			end if;
		end if;
	end process;	
	

end Behavioral;

