# Summary

Date : 2023-01-03 15:15:28

Directory c:\\other\\ise_14_7\\workshop\\ce7zlq\\zx\\CE7-X_DPUv20_updata_dcy _dm\\CE7-X_DPU

Total : 1961 files,  227428 codes, 53480 comments, 27653 blanks, all 308561 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Verilog | 378 | 98,061 | 11,758 | 9,137 | 118,956 |
| VHDL | 812 | 83,678 | 27,674 | 10,375 | 121,727 |
| Batch | 333 | 18,960 | 0 | 2,226 | 21,186 |
| HTML | 49 | 14,681 | 0 | 3,406 | 18,087 |
| System Verilog | 66 | 3,804 | 0 | 300 | 4,104 |
| XML | 4 | 3,399 | 16 | 4 | 3,419 |
| Shell Script | 237 | 2,644 | 10,563 | 1,638 | 14,845 |
| Log | 19 | 1,076 | 0 | 44 | 1,120 |
| vivado ucf | 45 | 508 | 1,777 | 140 | 2,425 |
| Markdown | 8 | 301 | 0 | 52 | 353 |
| Ini | 1 | 262 | 1,377 | 300 | 1,939 |
| XSL | 1 | 40 | 3 | 13 | 56 |
| Xilinx Design Constraints | 6 | 12 | 312 | 18 | 342 |
| JSON | 2 | 2 | 0 | 0 | 2 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 1,961 | 227,428 | 53,480 | 27,653 | 308,561 |
| HDL | 31 | 5,819 | 905 | 768 | 7,492 |
| HDL\\UART | 4 | 582 | 127 | 88 | 797 |
| HDL\\decoder | 5 | 647 | 117 | 108 | 872 |
| HDL\\sdram_rw_ss | 5 | 1,293 | 178 | 66 | 1,537 |
| _ngo | 12 | 248 | 160 | 33 | 441 |
| _ngo\\cs_icon_pro | 4 | 78 | 50 | 11 | 139 |
| _ngo\\cs_ila_pro_0 | 4 | 85 | 55 | 11 | 151 |
| _ngo\\cs_ila_pro_1 | 4 | 85 | 55 | 11 | 151 |
| dcyip | 207 | 16,098 | 7,622 | 3,912 | 27,632 |
| dcyip\\IP | 161 | 8,305 | 6,170 | 2,461 | 16,936 |
| dcyip\\IP\\CMD_RAM | 32 | 1,660 | 1,234 | 492 | 3,386 |
| dcyip\\IP\\CMD_RAM\\CMD_RAM | 29 | 1,521 | 1,139 | 475 | 3,135 |
| dcyip\\IP\\CMD_RAM\\CMD_RAM\\doc | 1 | 156 | 0 | 69 | 225 |
| dcyip\\IP\\CMD_RAM\\CMD_RAM\\example_design | 4 | 157 | 323 | 95 | 575 |
| dcyip\\IP\\CMD_RAM\\CMD_RAM\\implement | 4 | 107 | 54 | 49 | 210 |
| dcyip\\IP\\CMD_RAM\\CMD_RAM\\simulation | 20 | 1,101 | 762 | 262 | 2,125 |
| dcyip\\IP\\CMD_RAM\\CMD_RAM\\simulation\\functional | 6 | 109 | 95 | 37 | 241 |
| dcyip\\IP\\CMD_RAM\\CMD_RAM\\simulation\\timing | 6 | 112 | 95 | 41 | 248 |
| dcyip\\IP\\E_RAM | 33 | 1,665 | 1,234 | 493 | 3,392 |
| dcyip\\IP\\E_RAM\\E_RAM | 29 | 1,521 | 1,139 | 475 | 3,135 |
| dcyip\\IP\\E_RAM\\E_RAM\\doc | 1 | 156 | 0 | 69 | 225 |
| dcyip\\IP\\E_RAM\\E_RAM\\example_design | 4 | 157 | 323 | 95 | 575 |
| dcyip\\IP\\E_RAM\\E_RAM\\implement | 4 | 107 | 54 | 49 | 210 |
| dcyip\\IP\\E_RAM\\E_RAM\\simulation | 20 | 1,101 | 762 | 262 | 2,125 |
| dcyip\\IP\\E_RAM\\E_RAM\\simulation\\functional | 6 | 109 | 95 | 37 | 241 |
| dcyip\\IP\\E_RAM\\E_RAM\\simulation\\timing | 6 | 112 | 95 | 41 | 248 |
| dcyip\\IP\\FGM1 | 29 | 1,521 | 1,139 | 475 | 3,135 |
| dcyip\\IP\\FGM1\\doc | 1 | 156 | 0 | 69 | 225 |
| dcyip\\IP\\FGM1\\example_design | 4 | 157 | 323 | 95 | 575 |
| dcyip\\IP\\FGM1\\implement | 4 | 107 | 54 | 49 | 210 |
| dcyip\\IP\\FGM1\\simulation | 20 | 1,101 | 762 | 262 | 2,125 |
| dcyip\\IP\\FGM1\\simulation\\functional | 6 | 109 | 95 | 37 | 241 |
| dcyip\\IP\\FGM1\\simulation\\timing | 6 | 112 | 95 | 41 | 248 |
| dcyip\\IP\\FGM_RAM | 32 | 1,660 | 1,234 | 492 | 3,386 |
| dcyip\\IP\\FGM_RAM\\FGM_RAM | 29 | 1,521 | 1,139 | 475 | 3,135 |
| dcyip\\IP\\FGM_RAM\\FGM_RAM\\doc | 1 | 156 | 0 | 69 | 225 |
| dcyip\\IP\\FGM_RAM\\FGM_RAM\\example_design | 4 | 157 | 323 | 95 | 575 |
| dcyip\\IP\\FGM_RAM\\FGM_RAM\\implement | 4 | 107 | 54 | 49 | 210 |
| dcyip\\IP\\FGM_RAM\\FGM_RAM\\simulation | 20 | 1,101 | 762 | 262 | 2,125 |
| dcyip\\IP\\FGM_RAM\\FGM_RAM\\simulation\\functional | 6 | 109 | 95 | 37 | 241 |
| dcyip\\IP\\FGM_RAM\\FGM_RAM\\simulation\\timing | 6 | 112 | 95 | 41 | 248 |
| dcyip\\IP\\PACK_RAM | 32 | 1,660 | 1,234 | 492 | 3,386 |
| dcyip\\IP\\PACK_RAM\\RAM_A | 29 | 1,521 | 1,139 | 475 | 3,135 |
| dcyip\\IP\\PACK_RAM\\RAM_A\\doc | 1 | 156 | 0 | 69 | 225 |
| dcyip\\IP\\PACK_RAM\\RAM_A\\example_design | 4 | 157 | 323 | 95 | 575 |
| dcyip\\IP\\PACK_RAM\\RAM_A\\implement | 4 | 107 | 54 | 49 | 210 |
| dcyip\\IP\\PACK_RAM\\RAM_A\\simulation | 20 | 1,101 | 762 | 262 | 2,125 |
| dcyip\\IP\\PACK_RAM\\RAM_A\\simulation\\functional | 6 | 109 | 95 | 37 | 241 |
| dcyip\\IP\\PACK_RAM\\RAM_A\\simulation\\timing | 6 | 112 | 95 | 41 | 248 |
| dcyip\\SRC | 46 | 7,793 | 1,452 | 1,451 | 10,696 |
| dcyip\\SRC\\.VSCodeCounter | 5 | 89 | 0 | 26 | 115 |
| dcyip\\SRC\\.VSCodeCounter\\2023-01-03_15-13-50 | 5 | 89 | 0 | 26 | 115 |
| dcyip\\SRC\\CMD | 4 | 1,457 | 260 | 201 | 1,918 |
| dcyip\\SRC\\TIME_manger | 5 | 334 | 140 | 64 | 538 |
| ipcore_dir | 509 | 42,852 | 16,978 | 6,110 | 65,940 |
| ipcore_dir\\FIFO2048_8192 | 30 | 2,275 | 955 | 375 | 3,605 |
| ipcore_dir\\FIFO2048_8192\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\FIFO2048_8192\\example_design | 2 | 72 | 115 | 22 | 209 |
| ipcore_dir\\FIFO2048_8192\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\FIFO2048_8192\\simulation | 21 | 1,789 | 691 | 205 | 2,685 |
| ipcore_dir\\FIFO2048_8192\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\FIFO2048_8192\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\FIFO2048x8 | 30 | 2,248 | 955 | 373 | 3,576 |
| ipcore_dir\\FIFO2048x8\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\FIFO2048x8\\example_design | 2 | 69 | 115 | 22 | 206 |
| ipcore_dir\\FIFO2048x8\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\FIFO2048x8\\simulation | 21 | 1,765 | 691 | 203 | 2,659 |
| ipcore_dir\\FIFO2048x8\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\FIFO2048x8\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\FIFO8192_2048 | 30 | 2,248 | 955 | 373 | 3,576 |
| ipcore_dir\\FIFO8192_2048\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\FIFO8192_2048\\example_design | 2 | 69 | 115 | 22 | 206 |
| ipcore_dir\\FIFO8192_2048\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\FIFO8192_2048\\simulation | 21 | 1,765 | 691 | 203 | 2,659 |
| ipcore_dir\\FIFO8192_2048\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\FIFO8192_2048\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\FIFO8192x8 | 30 | 2,248 | 955 | 373 | 3,576 |
| ipcore_dir\\FIFO8192x8\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\FIFO8192x8\\example_design | 2 | 69 | 115 | 22 | 206 |
| ipcore_dir\\FIFO8192x8\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\FIFO8192x8\\simulation | 21 | 1,765 | 691 | 203 | 2,659 |
| ipcore_dir\\FIFO8192x8\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\FIFO8192x8\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\FIFO_sim_sdram | 30 | 2,097 | 952 | 364 | 3,413 |
| ipcore_dir\\FIFO_sim_sdram\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\FIFO_sim_sdram\\example_design | 2 | 52 | 115 | 21 | 188 |
| ipcore_dir\\FIFO_sim_sdram\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\FIFO_sim_sdram\\simulation | 21 | 1,631 | 688 | 195 | 2,514 |
| ipcore_dir\\FIFO_sim_sdram\\simulation\\functional | 7 | 310 | 94 | 28 | 432 |
| ipcore_dir\\FIFO_sim_sdram\\simulation\\timing | 7 | 312 | 94 | 28 | 434 |
| ipcore_dir\\RAM512X16 | 29 | 1,537 | 1,139 | 475 | 3,151 |
| ipcore_dir\\RAM512X16\\doc | 1 | 156 | 0 | 69 | 225 |
| ipcore_dir\\RAM512X16\\example_design | 4 | 162 | 323 | 95 | 580 |
| ipcore_dir\\RAM512X16\\implement | 4 | 107 | 54 | 49 | 210 |
| ipcore_dir\\RAM512X16\\simulation | 20 | 1,112 | 762 | 262 | 2,136 |
| ipcore_dir\\RAM512X16\\simulation\\functional | 6 | 110 | 95 | 37 | 242 |
| ipcore_dir\\RAM512X16\\simulation\\timing | 6 | 113 | 95 | 41 | 249 |
| ipcore_dir\\asyn_fifo | 30 | 2,236 | 955 | 373 | 3,564 |
| ipcore_dir\\asyn_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\asyn_fifo\\example_design | 2 | 63 | 115 | 22 | 200 |
| ipcore_dir\\asyn_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\asyn_fifo\\simulation | 21 | 1,759 | 691 | 203 | 2,653 |
| ipcore_dir\\asyn_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\asyn_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\blk_len_fifo | 30 | 2,230 | 955 | 373 | 3,558 |
| ipcore_dir\\blk_len_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\blk_len_fifo\\example_design | 2 | 60 | 115 | 22 | 197 |
| ipcore_dir\\blk_len_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\blk_len_fifo\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| ipcore_dir\\blk_len_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\blk_len_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\blkout_fifo | 30 | 2,242 | 955 | 373 | 3,570 |
| ipcore_dir\\blkout_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\blkout_fifo\\example_design | 2 | 66 | 115 | 22 | 203 |
| ipcore_dir\\blkout_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\blkout_fifo\\simulation | 21 | 1,762 | 691 | 203 | 2,656 |
| ipcore_dir\\blkout_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\blkout_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\config_fifo | 30 | 2,221 | 955 | 373 | 3,549 |
| ipcore_dir\\config_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\config_fifo\\example_design | 2 | 60 | 115 | 22 | 197 |
| ipcore_dir\\config_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\config_fifo\\simulation | 21 | 1,747 | 691 | 203 | 2,641 |
| ipcore_dir\\config_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\config_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\downstream_fifo | 30 | 2,233 | 955 | 373 | 3,561 |
| ipcore_dir\\downstream_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\downstream_fifo\\example_design | 2 | 66 | 115 | 22 | 203 |
| ipcore_dir\\downstream_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\downstream_fifo\\simulation | 21 | 1,753 | 691 | 203 | 2,647 |
| ipcore_dir\\downstream_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\downstream_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\fifo1024x16_to_2048x8 | 30 | 2,242 | 955 | 373 | 3,570 |
| ipcore_dir\\fifo1024x16_to_2048x8\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\fifo1024x16_to_2048x8\\example_design | 2 | 66 | 115 | 22 | 203 |
| ipcore_dir\\fifo1024x16_to_2048x8\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\fifo1024x16_to_2048x8\\simulation | 21 | 1,762 | 691 | 203 | 2,656 |
| ipcore_dir\\fifo1024x16_to_2048x8\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\fifo1024x16_to_2048x8\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\fifo_16width | 30 | 2,230 | 955 | 373 | 3,558 |
| ipcore_dir\\fifo_16width\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\fifo_16width\\example_design | 2 | 60 | 115 | 22 | 197 |
| ipcore_dir\\fifo_16width\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\fifo_16width\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| ipcore_dir\\fifo_16width\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\fifo_16width\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\fifo_26width | 30 | 2,230 | 955 | 373 | 3,558 |
| ipcore_dir\\fifo_26width\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\fifo_26width\\example_design | 2 | 60 | 115 | 22 | 197 |
| ipcore_dir\\fifo_26width\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\fifo_26width\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| ipcore_dir\\fifo_26width\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\fifo_26width\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| ipcore_dir\\img_type_fifo | 30 | 2,230 | 955 | 373 | 3,558 |
| ipcore_dir\\img_type_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| ipcore_dir\\img_type_fifo\\example_design | 2 | 60 | 115 | 22 | 197 |
| ipcore_dir\\img_type_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| ipcore_dir\\img_type_fifo\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| ipcore_dir\\img_type_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| ipcore_dir\\img_type_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| simulation | 43 | 5,317 | 1,710 | 785 | 7,812 |
| simulation\\8b10_tx | 38 | 3,879 | 1,429 | 581 | 5,889 |
| simulation\\8b10_tx\\kw_pkg_fifo | 30 | 2,285 | 958 | 376 | 3,619 |
| simulation\\8b10_tx\\kw_pkg_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| simulation\\8b10_tx\\kw_pkg_fifo\\example_design | 2 | 66 | 115 | 22 | 203 |
| simulation\\8b10_tx\\kw_pkg_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| simulation\\8b10_tx\\kw_pkg_fifo\\simulation | 21 | 1,805 | 694 | 206 | 2,705 |
| simulation\\8b10_tx\\kw_pkg_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| simulation\\8b10_tx\\kw_pkg_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| work | 170 | 5,226 | 0 | 170 | 5,396 |
| work\\@a@d@d@e@r16@b@i@t@c_new | 1 | 10 | 0 | 1 | 11 |
| work\\@buffer_@r@a@m0 | 1 | 20 | 0 | 1 | 21 |
| work\\@buffer_@r@a@m1 | 1 | 20 | 0 | 1 | 21 |
| work\\@c@l@k@manage | 1 | 14 | 0 | 1 | 15 |
| work\\@c@m@d_@r@e@v | 1 | 14 | 0 | 1 | 15 |
| work\\@c@o@d@e@r_@r@d@a@t_top | 1 | 44 | 0 | 1 | 45 |
| work\\@c@o@d@e@r_@w@d@a@t_top | 1 | 54 | 0 | 1 | 55 |
| work\\@c@o@d@e@r_top | 1 | 50 | 0 | 1 | 51 |
| work\\@c@o@m@p_@t@o@p | 1 | 93 | 0 | 1 | 94 |
| work\\@c@u@t_top | 1 | 41 | 0 | 1 | 42 |
| work\\@code | 1 | 34 | 0 | 1 | 35 |
| work\\@create_@buffer0 | 1 | 27 | 0 | 1 | 28 |
| work\\@create_@buffer1 | 1 | 24 | 0 | 1 | 25 |
| work\\@create_@layer | 1 | 42 | 0 | 1 | 43 |
| work\\@create_@layer_@controller | 1 | 27 | 0 | 1 | 28 |
| work\\@create_@tag_@tree | 1 | 43 | 0 | 1 | 44 |
| work\\@create_@value | 1 | 18 | 0 | 1 | 19 |
| work\\@d@e@c@o@d@e | 1 | 23 | 0 | 1 | 24 |
| work\\@d@m_rv | 1 | 16 | 0 | 1 | 17 |
| work\\@d@w@t_top | 1 | 33 | 0 | 1 | 34 |
| work\\@encode_@controller | 1 | 32 | 0 | 1 | 33 |
| work\\@encode_@tag_@tree | 1 | 31 | 0 | 1 | 32 |
| work\\@encoder | 1 | 27 | 0 | 1 | 28 |
| work\\@f@i@f@o | 1 | 21 | 0 | 1 | 22 |
| work\\@global_@control | 1 | 49 | 0 | 1 | 50 |
| work\\@k_scale | 1 | 40 | 0 | 1 | 41 |
| work\\@k_scale_ctrl | 1 | 44 | 0 | 1 | 45 |
| work\\@kcoef_select | 1 | 66 | 0 | 1 | 67 |
| work\\@l@l_adjust | 1 | 37 | 0 | 1 | 38 |
| work\\@l@l_ctrl | 1 | 42 | 0 | 1 | 43 |
| work\\@leaf_@address | 1 | 14 | 0 | 1 | 15 |
| work\\@leaf_@r@a@m0 | 1 | 20 | 0 | 1 | 21 |
| work\\@m@q_coder | 1 | 18 | 0 | 1 | 19 |
| work\\@output_@b@r@a@m | 1 | 20 | 0 | 1 | 21 |
| work\\@p2_refresh_new | 1 | 18 | 0 | 1 | 19 |
| work\\@p3_byteout_new | 1 | 16 | 0 | 1 | 17 |
| work\\@p@k_top | 1 | 33 | 0 | 1 | 34 |
| work\\@parent_@r@a@m0 | 1 | 20 | 0 | 1 | 21 |
| work\\@r@o@m_162x8 | 1 | 17 | 0 | 1 | 18 |
| work\\@r@s@t | 1 | 8 | 0 | 1 | 9 |
| work\\@result_@store | 1 | 35 | 0 | 1 | 36 |
| work\\@s@y@n_@b@c | 1 | 26 | 0 | 1 | 27 |
| work\\@state@update_and_@parameter@prepare | 1 | 51 | 0 | 1 | 52 |
| work\\@t1_top | 1 | 44 | 0 | 1 | 45 |
| work\\@t2_top | 1 | 31 | 0 | 1 | 32 |
| work\\@t@a@g_@t@r@e@e_top | 1 | 23 | 0 | 1 | 24 |
| work\\@t@o@p | 1 | 69 | 0 | 1 | 70 |
| work\\@tag_@tree_@r@a@m_@ctrl | 1 | 41 | 0 | 1 | 42 |
| work\\apart_data | 1 | 25 | 0 | 1 | 26 |
| work\\assembly_comp | 1 | 48 | 0 | 1 | 49 |
| work\\assembly_direct | 1 | 31 | 0 | 1 | 32 |
| work\\asyn_dpram_ip | 1 | 27 | 0 | 1 | 28 |
| work\\bit_add | 1 | 36 | 0 | 1 | 37 |
| work\\bit_extract | 1 | 11 | 0 | 1 | 12 |
| work\\blk_num_ram_ctrl | 1 | 18 | 0 | 1 | 19 |
| work\\blocking_c | 1 | 47 | 0 | 1 | 48 |
| work\\body_top | 1 | 31 | 0 | 1 | 32 |
| work\\bram_cntrl | 1 | 66 | 0 | 1 | 67 |
| work\\bram_coef_wr_cntrl | 1 | 39 | 0 | 1 | 40 |
| work\\bram_ip | 1 | 26 | 0 | 1 | 27 |
| work\\ce7_comp_top | 1 | 31 | 0 | 1 | 32 |
| work\\checksum | 1 | 13 | 0 | 1 | 14 |
| work\\cmd_check | 1 | 17 | 0 | 1 | 18 |
| work\\coder_rd_sdram | 1 | 37 | 0 | 1 | 38 |
| work\\coder_wr_sdram | 1 | 57 | 0 | 1 | 58 |
| work\\col_lift | 1 | 35 | 0 | 1 | 36 |
| work\\col_lift_ctrl_lev1 | 1 | 65 | 0 | 1 | 66 |
| work\\col_lift_ctrl_lev234 | 1 | 86 | 0 | 1 | 87 |
| work\\col_lift_pre | 1 | 28 | 0 | 1 | 29 |
| work\\col_ram_ctrl | 1 | 32 | 0 | 1 | 33 |
| work\\col_trans_lev1 | 1 | 38 | 0 | 1 | 39 |
| work\\col_trans_lev234 | 1 | 44 | 0 | 1 | 45 |
| work\\comp_rd_sdram | 1 | 48 | 0 | 1 | 49 |
| work\\compare | 1 | 30 | 0 | 1 | 31 |
| work\\cpu_top | 1 | 23 | 0 | 1 | 24 |
| work\\cuter | 1 | 39 | 0 | 1 | 40 |
| work\\dat_receive | 1 | 15 | 0 | 1 | 16 |
| work\\data_buffer_new | 1 | 29 | 0 | 1 | 30 |
| work\\dcshift | 1 | 16 | 0 | 1 | 17 |
| work\\direct_rd_sdram | 1 | 46 | 0 | 1 | 47 |
| work\\dpram32b | 1 | 25 | 0 | 1 | 26 |
| work\\dual_port_bram | 1 | 21 | 0 | 1 | 22 |
| work\\dwt_store | 1 | 40 | 0 | 1 | 41 |
| work\\dwt_trans | 1 | 38 | 0 | 1 | 39 |
| work\\encode_lenth | 1 | 15 | 0 | 1 | 16 |
| work\\fast_look_new_new | 1 | 17 | 0 | 1 | 18 |
| work\\frame_head_out | 1 | 15 | 0 | 1 | 16 |
| work\\framing_c | 1 | 59 | 0 | 1 | 60 |
| work\\gen_frame_syn | 1 | 51 | 0 | 1 | 52 |
| work\\glbl | 1 | 11 | 0 | 1 | 12 |
| work\\head_body_add | 1 | 21 | 0 | 1 | 22 |
| work\\head_len | 1 | 16 | 0 | 1 | 17 |
| work\\head_top | 1 | 32 | 0 | 1 | 33 |
| work\\info_data_separate | 1 | 36 | 0 | 1 | 37 |
| work\\interactive_contrl | 1 | 14 | 0 | 1 | 15 |
| work\\lev_1 | 1 | 27 | 0 | 1 | 28 |
| work\\lev_2_3_4 | 1 | 35 | 0 | 1 | 36 |
| work\\lev_sel | 1 | 20 | 0 | 1 | 21 |
| work\\lift_pro_element | 1 | 31 | 0 | 1 | 32 |
| work\\lvde_send | 1 | 4 | 0 | 1 | 5 |
| work\\lvds_8b10b_decoder | 1 | 12 | 0 | 1 | 13 |
| work\\mid_top | 1 | 35 | 0 | 1 | 36 |
| work\\mselen_input_buffer | 1 | 20 | 0 | 1 | 21 |
| work\\mul_cut | 1 | 22 | 0 | 1 | 23 |
| work\\mult_pepi | 1 | 18 | 0 | 1 | 19 |
| work\\multiply_ip | 1 | 14 | 0 | 1 | 15 |
| work\\mux_apart_4level | 1 | 42 | 0 | 1 | 43 |
| work\\pack_control | 1 | 70 | 0 | 1 | 71 |
| work\\pack_head_@i@n@p@l_mux | 1 | 36 | 0 | 1 | 37 |
| work\\pack_head_@i@n@p@l_top | 1 | 17 | 0 | 1 | 18 |
| work\\pack_head_char | 1 | 32 | 0 | 1 | 33 |
| work\\packing | 1 | 44 | 0 | 1 | 45 |
| work\\packing_sel | 1 | 32 | 0 | 1 | 33 |
| work\\packing_top | 1 | 30 | 0 | 1 | 31 |
| work\\param_pre | 1 | 24 | 0 | 1 | 25 |
| work\\pass_len_code | 1 | 36 | 0 | 1 | 37 |
| work\\pkt_info_analysis | 1 | 16 | 0 | 1 | 17 |
| work\\pkt_pre_top | 1 | 37 | 0 | 1 | 38 |
| work\\pkt_wr_fifo | 1 | 13 | 0 | 1 | 14 |
| work\\pre_rd_fifo | 1 | 15 | 0 | 1 | 16 |
| work\\pre_wr_config_fifo | 1 | 20 | 0 | 1 | 21 |
| work\\pre_wr_direct_dpram | 1 | 26 | 0 | 1 | 27 |
| work\\pre_wr_img_dpram | 1 | 26 | 0 | 1 | 27 |
| work\\pre_wr_info_dpram | 1 | 26 | 0 | 1 | 27 |
| work\\pre_wr_sdram | 1 | 45 | 0 | 1 | 46 |
| work\\pre_wr_sdram_top | 1 | 46 | 0 | 1 | 47 |
| work\\processing_analysis | 1 | 52 | 0 | 1 | 53 |
| work\\ram_@l@l_adjust | 1 | 26 | 0 | 1 | 27 |
| work\\ram_col_lift | 1 | 26 | 0 | 1 | 27 |
| work\\rd_@m@q_dpram | 1 | 21 | 0 | 1 | 22 |
| work\\rd_@m@q_sdram | 1 | 23 | 0 | 1 | 24 |
| work\\rd_dpram | 1 | 49 | 0 | 1 | 50 |
| work\\rd_one_pack_ctrl | 1 | 20 | 0 | 1 | 21 |
| work\\rdblk3_wrblkband | 1 | 74 | 0 | 1 | 75 |
| work\\reg_top_xs_cntrl | 1 | 22 | 0 | 1 | 23 |
| work\\row_lift | 1 | 26 | 0 | 1 | 27 |
| work\\row_lift_ctrl | 1 | 43 | 0 | 1 | 44 |
| work\\row_lift_ctrl_lev234 | 1 | 62 | 0 | 1 | 63 |
| work\\row_trans | 1 | 34 | 0 | 1 | 35 |
| work\\row_trans_lev234 | 1 | 45 | 0 | 1 | 46 |
| work\\rst_manage | 1 | 14 | 0 | 1 | 15 |
| work\\schedule | 1 | 54 | 0 | 1 | 55 |
| work\\sdram2dpram | 1 | 43 | 0 | 1 | 44 |
| work\\sdram_contrl_top_fifo | 1 | 28 | 0 | 1 | 29 |
| work\\serdes_5x | 1 | 12 | 0 | 1 | 13 |
| work\\slope_calc_m | 1 | 14 | 0 | 1 | 15 |
| work\\slope_control | 1 | 54 | 0 | 1 | 55 |
| work\\slope_rd_dpram | 1 | 21 | 0 | 1 | 22 |
| work\\slope_rd_dpram_ctrl | 1 | 17 | 0 | 1 | 18 |
| work\\slope_thrs_calc | 1 | 15 | 0 | 1 | 16 |
| work\\slope_top_new | 1 | 19 | 0 | 1 | 20 |
| work\\stack | 1 | 27 | 0 | 1 | 28 |
| work\\state_reg | 1 | 24 | 0 | 1 | 25 |
| work\\subband_apart | 1 | 35 | 0 | 1 | 36 |
| work\\syn_dpram_ip | 1 | 32 | 0 | 1 | 33 |
| work\\tb | 1 | 4 | 0 | 1 | 5 |
| work\\tb_v | 1 | 4 | 0 | 1 | 5 |
| work\\tbb | 1 | 4 | 0 | 1 | 5 |
| work\\time_guard | 1 | 12 | 0 | 1 | 13 |
| work\\truncate | 1 | 33 | 0 | 1 | 34 |
| work\\uart_tx | 1 | 15 | 0 | 1 | 16 |
| work\\wr_b_dpram | 1 | 16 | 0 | 1 | 17 |
| work\\wr_blen_bram_ctrl | 1 | 17 | 0 | 1 | 18 |
| work\\wr_blk3_dpram | 1 | 24 | 0 | 1 | 25 |
| work\\wr_blocks_c | 1 | 45 | 0 | 1 | 46 |
| work\\wr_dpram | 1 | 18 | 0 | 1 | 19 |
| work\\wr_slope_dpram | 1 | 16 | 0 | 1 | 17 |
| work\\write_to_ram | 1 | 37 | 0 | 1 | 38 |
| work\\write_to_sdram | 1 | 65 | 0 | 1 | 66 |
| work\\zerobit_out | 1 | 28 | 0 | 1 | 29 |
| xdip | 564 | 78,053 | 17,936 | 8,848 | 104,837 |
| xdip\\ipcore_dir | 398 | 35,212 | 13,063 | 4,757 | 53,032 |
| xdip\\ipcore_dir\\asyn_fifo | 30 | 2,236 | 955 | 373 | 3,564 |
| xdip\\ipcore_dir\\asyn_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\asyn_fifo\\example_design | 2 | 63 | 115 | 22 | 200 |
| xdip\\ipcore_dir\\asyn_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\asyn_fifo\\simulation | 21 | 1,759 | 691 | 203 | 2,653 |
| xdip\\ipcore_dir\\asyn_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\asyn_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\blk_len_fifo | 30 | 2,230 | 955 | 373 | 3,558 |
| xdip\\ipcore_dir\\blk_len_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\blk_len_fifo\\example_design | 2 | 60 | 115 | 22 | 197 |
| xdip\\ipcore_dir\\blk_len_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\blk_len_fifo\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| xdip\\ipcore_dir\\blk_len_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\blk_len_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\blkout_fifo | 30 | 2,242 | 955 | 373 | 3,570 |
| xdip\\ipcore_dir\\blkout_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\blkout_fifo\\example_design | 2 | 66 | 115 | 22 | 203 |
| xdip\\ipcore_dir\\blkout_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\blkout_fifo\\simulation | 21 | 1,762 | 691 | 203 | 2,656 |
| xdip\\ipcore_dir\\blkout_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\blkout_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\config_fifo | 30 | 2,221 | 955 | 373 | 3,549 |
| xdip\\ipcore_dir\\config_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\config_fifo\\example_design | 2 | 60 | 115 | 22 | 197 |
| xdip\\ipcore_dir\\config_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\config_fifo\\simulation | 21 | 1,747 | 691 | 203 | 2,641 |
| xdip\\ipcore_dir\\config_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\config_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\downstream_fifo | 30 | 2,233 | 955 | 373 | 3,561 |
| xdip\\ipcore_dir\\downstream_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\downstream_fifo\\example_design | 2 | 66 | 115 | 22 | 203 |
| xdip\\ipcore_dir\\downstream_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\downstream_fifo\\simulation | 21 | 1,753 | 691 | 203 | 2,647 |
| xdip\\ipcore_dir\\downstream_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\downstream_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\fifo_16width | 30 | 2,230 | 955 | 373 | 3,558 |
| xdip\\ipcore_dir\\fifo_16width\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\fifo_16width\\example_design | 2 | 60 | 115 | 22 | 197 |
| xdip\\ipcore_dir\\fifo_16width\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\fifo_16width\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| xdip\\ipcore_dir\\fifo_16width\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\fifo_16width\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\fifo_26width | 30 | 2,230 | 955 | 373 | 3,558 |
| xdip\\ipcore_dir\\fifo_26width\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\fifo_26width\\example_design | 2 | 60 | 115 | 22 | 197 |
| xdip\\ipcore_dir\\fifo_26width\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\fifo_26width\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| xdip\\ipcore_dir\\fifo_26width\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\fifo_26width\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\fifo_8width | 30 | 2,230 | 955 | 373 | 3,558 |
| xdip\\ipcore_dir\\fifo_8width\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\fifo_8width\\example_design | 2 | 60 | 115 | 22 | 197 |
| xdip\\ipcore_dir\\fifo_8width\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\fifo_8width\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| xdip\\ipcore_dir\\fifo_8width\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\fifo_8width\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\fifo_generator_v9_3 | 30 | 2,225 | 955 | 373 | 3,553 |
| xdip\\ipcore_dir\\fifo_generator_v9_3\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\fifo_generator_v9_3\\example_design | 2 | 60 | 115 | 22 | 197 |
| xdip\\ipcore_dir\\fifo_generator_v9_3\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\fifo_generator_v9_3\\simulation | 21 | 1,751 | 691 | 203 | 2,645 |
| xdip\\ipcore_dir\\fifo_generator_v9_3\\simulation\\functional | 7 | 315 | 94 | 28 | 437 |
| xdip\\ipcore_dir\\fifo_generator_v9_3\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\frame_len_fifo | 30 | 2,230 | 955 | 373 | 3,558 |
| xdip\\ipcore_dir\\frame_len_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\frame_len_fifo\\example_design | 2 | 60 | 115 | 22 | 197 |
| xdip\\ipcore_dir\\frame_len_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\frame_len_fifo\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| xdip\\ipcore_dir\\frame_len_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\frame_len_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\img_type_fifo | 30 | 2,230 | 955 | 373 | 3,558 |
| xdip\\ipcore_dir\\img_type_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\img_type_fifo\\example_design | 2 | 60 | 115 | 22 | 197 |
| xdip\\ipcore_dir\\img_type_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\img_type_fifo\\simulation | 21 | 1,756 | 691 | 203 | 2,650 |
| xdip\\ipcore_dir\\img_type_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\img_type_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\ipcore_dir\\pktout_fifo | 30 | 2,248 | 955 | 373 | 3,576 |
| xdip\\ipcore_dir\\pktout_fifo\\doc | 1 | 170 | 0 | 78 | 248 |
| xdip\\ipcore_dir\\pktout_fifo\\example_design | 2 | 69 | 115 | 22 | 206 |
| xdip\\ipcore_dir\\pktout_fifo\\implement | 6 | 244 | 149 | 70 | 463 |
| xdip\\ipcore_dir\\pktout_fifo\\simulation | 21 | 1,765 | 691 | 203 | 2,659 |
| xdip\\ipcore_dir\\pktout_fifo\\simulation\\functional | 7 | 311 | 94 | 28 | 433 |
| xdip\\ipcore_dir\\pktout_fifo\\simulation\\timing | 7 | 313 | 94 | 28 | 435 |
| xdip\\src | 166 | 42,841 | 4,873 | 4,091 | 51,805 |
| xdip\\src\\.VSCodeCounter | 5 | 214 | 0 | 26 | 240 |
| xdip\\src\\.VSCodeCounter\\2023-01-03_15-15-03 | 5 | 214 | 0 | 26 | 240 |
| xdip\\src\\back | 10 | 3,417 | 350 | 430 | 4,197 |
| xdip\\src\\comp | 115 | 30,682 | 3,378 | 2,752 | 36,812 |
| xdip\\src\\cpu | 2 | 455 | 44 | 42 | 541 |
| xdip\\src\\mid | 6 | 1,245 | 134 | 183 | 1,562 |
| xdip\\src\\pre | 16 | 3,233 | 512 | 376 | 4,121 |
| xdip\\src\\sdram_control_top | 5 | 2,375 | 234 | 82 | 2,691 |
| xdip\\src\\serdes_5x | 4 | 463 | 77 | 55 | 595 |
| xst | 175 | 7,537 | 0 | 27 | 7,564 |
| xst\\work | 175 | 7,537 | 0 | 27 | 7,564 |
| xst\\work\\sub00 | 100 | 5,160 | 0 | 20 | 5,180 |
| xst\\work\\sub01 | 75 | 2,377 | 0 | 7 | 2,384 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)