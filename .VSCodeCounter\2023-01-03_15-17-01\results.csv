"filename", "language", "Verilog", "VHDL", "HTML", "vivado ucf", "XSL", "Log", "XML", "Batch", "Shell Script", "System Verilog", "Markdown", "JSON", "Ini", "Xilinx Design Constraints", "comment", "blank", "total"
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Buffer_RAM0.v", "Verilog", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 11, 85
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Buffer_RAM1.v", "Verilog", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 9, 80
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CLK_1MHz.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 10, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CLK_1s.vhd", "VHDL", 0, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 13, 138
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CLK_2MHz.vhd", "VHDL", 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 13, 91
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CMD_ANALY.vhd", "VHDL", 0, 594, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 60, 699
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CMD_CHECK.v", "Verilog", 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 34, 392
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CMD_PROCESS.vhd", "VHDL", 0, 281, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 62, 378
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CMD_REV.v", "Verilog", 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 39, 414
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CODER_RDAT_top.v", "Verilog", 479, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 22, 526
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CODER_WDAT_top.v", "Verilog", 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 18, 216
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CODER_top.v", "Verilog", 174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 14, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\COMP_TOP.v", "Verilog", 418, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 335, 48, 801
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\COREUART_summary.html", "HTML", 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 79
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\CUT_top.v", "Verilog", 191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 33, 243
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Code.v", "Verilog", 520, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 570
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ComControl_summary.html", "HTML", 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 80
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Create_Buffer0.v", "Verilog", 260, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 273
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Create_Buffer1.v", "Verilog", 261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 274
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Create_Layer.v", "Verilog", 387, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 37, 430
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Create_Layer_Controller.v", "Verilog", 433, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 460
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Create_Tag_Tree.v", "Verilog", 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 126
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Create_Value.v", "Verilog", 75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5, 81
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DATA_PROCESS.vhd", "VHDL", 0, 442, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 81, 553
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DA_CHANGE.vhd", "VHDL", 0, 158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 27, 213
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DA_DRV.vhd", "VHDL", 0, 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 19, 243
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DA_NORMAL.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 13, 104
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DA_PROCESS.vhd", "VHDL", 0, 333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 68, 427
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DA_SCAN.vhd", "VHDL", 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 16, 135
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DA_SCAN_OUT.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 14, 105
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DA_SCAN_PROCESS.vhd", "VHDL", 0, 143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 39, 208
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DA_SCH.vhd", "VHDL", 0, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 12, 109
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DCM0.vhd", "VHDL", 0, 78, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 10, 110
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DCM0_arwz.ucf", "vivado ucf", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 20
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DCM1.vhd", "VHDL", 0, 77, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 10, 109
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DCM1_arwz.ucf", "vivado ucf", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 20
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DCM2.vhd", "VHDL", 0, 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 9, 103
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DCM2_arwz.ucf", "vivado ucf", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 20
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DECODE.v", "Verilog", 132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 16, 171
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DIV_TIME_EDGE.vhd", "VHDL", 0, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 20, 324
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DM_rv.v", "Verilog", 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 3, 54
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\DWT_top.v", "Verilog", 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 11, 136
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\D_C7_FPGA.vhd", "VHDL", 0, 661, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 166, 161, 988
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Encode_Controller.v", "Verilog", 289, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 41, 334
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Encode_Tag_Tree.v", "Verilog", 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 11, 99
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Encoder.v", "Verilog", 397, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 419
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\FIFO.v", "Verilog", 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 122
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\FIFO8192x8.vhd", "VHDL", 0, 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 293
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\FIFO8192x8.vho", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 102
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\FIFO8192x8_summary.html", "HTML", 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 79
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Fifo_Schedule.v", "Verilog", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 9, 61
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Format_Check_summary.html", "HTML", 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 80
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\GC_DATA_PROCESS.vhd", "VHDL", 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 32, 139
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Global_Control.v", "Verilog", 431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 480
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\AD_ctrl.vhd", "VHDL", 0, 343, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 30, 397
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\CCSDS_PACK.vhd", "VHDL", 0, 216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 17, 260
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\CLKManage.v", "Verilog", 60, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 16, 115
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\CPU_IF.vhd", "VHDL", 0, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 22, 187
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\ComControl.vhd", "VHDL", 0, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 41, 371
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\Format_Check.vhd", "VHDL", 0, 168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 20, 216
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\Receive_8b10b.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 17, 123
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\Receive_uart.vhd", "VHDL", 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 14, 94
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\SciData_Multi.vhd", "VHDL", 0, 334, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 53, 413
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\SciData_Receive.vhd", "VHDL", 0, 400, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 75, 515
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\SciData_Transmit.vhd", "VHDL", 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 23, 218
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\SciReceive_8b10b.vhd", "VHDL", 0, 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 19, 117
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\SciReceive_uart.vhd", "VHDL", 0, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 19, 142
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\SciTX.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 14, 139
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\TOP.v", "Verilog", 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 38, 275
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\UART\COREUART.vhd", "VHDL", 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 18, 141
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\UART\Clock_gen.vhd", "VHDL", 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 17, 90
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\UART\Rx_async.vhd", "VHDL", 0, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 32, 358
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\UART\Tx_async.vhd", "VHDL", 0, 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 21, 208
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\UARTtoRS01.vhd", "VHDL", 0, 227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 37, 296
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\decoder\comma_check_5x.v", "Verilog", 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 31, 230
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\decoder\decoder_8b10b_5x.v", "Verilog", 288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 37, 358
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\decoder\rx_module5x.v", "Verilog", 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 12, 86
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\decoder\serdes5x.v", "Verilog", 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 7, 74
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\decoder\x_cdr5x.v", "Verilog", 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 21, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\multiplex.vhd", "VHDL", 0, 328, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 51, 408
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\sdram_rw_ss\pkg_ce5.vhd", "VHDL", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 11, 35
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\sdram_rw_ss\rw_sdramcntrl.vhd", "VHDL", 0, 1036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 22, 1128
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\sdram_rw_ss\sdram_contrl_top.vhd", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 7, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\sdram_rw_ss\sdram_read.vhd", "VHDL", 0, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 13, 153
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\HDL\sdram_rw_ss\sdram_write.vhd", "VHDL", 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 13, 159
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\H_EFI_DATA_PROCESS1.vhd", "VHDL", 0, 79, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 36, 120
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\H_EFI_DATA_PROCESS2.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 36, 120
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\H_FGM_DATA_PROCESS.vhd", "VHDL", 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 35, 126
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\H_PACK_PROCESS.vhd", "VHDL", 0, 547, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63, 48, 658
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\K_scale.v", "Verilog", 251, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 26, 309
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\K_scale_ctrl.v", "Verilog", 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 20, 336
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Kcoef_select.v", "Verilog", 138, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 15, 176
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\LC_PROCESS.vhd", "VHDL", 0, 166, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 20, 212
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\LL_adjust.v", "Verilog", 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 21, 175
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\LL_ctrl.v", "Verilog", 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 37, 464
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\L_DATA_PROCESS.vhd", "VHDL", 0, 339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 81, 462
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\L_EFI_DATA_PROCESS.vhd", "VHDL", 0, 80, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 36, 122
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\L_FGM_DATA_PROCESS.vhd", "VHDL", 0, 79, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 36, 121
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\L_PACK_MODE.vhd", "VHDL", 0, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 54, 364
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\L_PACK_PROCESS.vhd", "VHDL", 0, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 25, 377
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\L_SEND_PACK.vhd", "VHDL", 0, 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 54, 306
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Leaf_Address.v", "Verilog", 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Leaf_RAM0.v", "Verilog", 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 11, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\MAIN_CTRL.vhd", "VHDL", 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 14, 107
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\MQ_coder.v", "Verilog", 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 17, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Output_BRAM.v", "Verilog", 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13, 38
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\P2_refresh_new.v", "Verilog", 190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 37, 396
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\P3_byteout_new.v", "Verilog", 428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 25, 476
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\PACK_OUTPUT.vhd", "VHDL", 0, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 17, 215
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\PACK_PROCESS.vhd", "VHDL", 0, 311, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 58, 401
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\PK_top.v", "Verilog", 218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 24, 266
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Parent_RAM0.v", "Verilog", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 12, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ROM_162x8.v", "Verilog", 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 11, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\RST.v", "Verilog", 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 6, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Receive_8b10b_summary.html", "HTML", 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 80
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Receive_uart_summary.html", "HTML", 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 80
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Result_Store.v", "Verilog", 743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 77, 838
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SC_SEND_CTRL.vhd", "VHDL", 0, 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 20, 260
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SEND_PACK.vhd", "VHDL", 0, 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 54, 306
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SYN_BC.v", "Verilog", 227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 19, 267
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SciData_Transmit_envsettings.html", "HTML", 0, 0, 400, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 400
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SciData_Transmit_summary.html", "HTML", 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 80
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SciReceive_8b10b_envsettings.html", "HTML", 0, 0, 400, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 400
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SciReceive_8b10b_summary.html", "HTML", 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 80
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SciReceive_ram.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 21, 149
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SciReceive_uart_envsettings.html", "HTML", 0, 0, 400, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 400
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\SciReceive_uart_summary.html", "HTML", 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 80
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Sdram_Schedule.vhd", "VHDL", 0, 217, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 26, 274
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\StateUpdate_and_ParameterPrepare.v", "Verilog", 527, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63, 590
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\T1_top.v", "Verilog", 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 319
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\T2_top.v", "Verilog", 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 27, 253
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\TAG_TREE_top.v", "Verilog", 227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 25, 261
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\TEST_PWR.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 8, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\TIME_COUNTER.vhd", "VHDL", 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 16, 122
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\TOP.ucf", "vivado ucf", 0, 0, 0, 240, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 40, 302
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\TOP_envsettings.html", "HTML", 0, 0, 550, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 550
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\TOP_summary.html", "HTML", 0, 0, 234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 245
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\TOP_summary.xml", "XML", 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 1, 11
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\TOP_usage.xml", "XML", 0, 0, 0, 0, 0, 0, 3279, 0, 0, 0, 0, 0, 0, 0, 5, 1, 3285
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\TOP_xpa.log", "Log", 0, 0, 0, 0, 0, 714, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 719
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\Tag_Tree_RAM_Ctrl.v", "Verilog", 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 199, 30, 356
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_icon_pro\coregen.log", "Log", 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_icon_pro\icon_pro.ucf", "vivado ucf", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 10
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_icon_pro\icon_pro.vhd", "VHDL", 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 4, 30
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_icon_pro\icon_pro.vho", "VHDL", 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 6, 37
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_ila_pro_0\coregen.log", "Log", 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_ila_pro_0\ila_pro_0.ucf", "vivado ucf", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 0, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_ila_pro_0\ila_pro_0.vhd", "VHDL", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 4, 32
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_ila_pro_0\ila_pro_0.vho", "VHDL", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 6, 41
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_ila_pro_1\coregen.log", "Log", 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_ila_pro_1\ila_pro_1.ucf", "vivado ucf", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 0, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_ila_pro_1\ila_pro_1.vhd", "VHDL", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 4, 32
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\_ngo\cs_ila_pro_1\ila_pro_1.vho", "VHDL", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 6, 41
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\apart_data.v", "Verilog", 95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 19, 137
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\assembly_comp.v", "Verilog", 271, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 32, 370
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\assembly_direct.v", "Verilog", 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 13, 120
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\asyn_dpram_ip.v", "Verilog", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\bit_add.v", "Verilog", 1196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 23, 1244
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\bit_extract.v", "Verilog", 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 11, 93
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\blk_num_ram_ctrl.v", "Verilog", 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 12, 90
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\blocking_c.v", "Verilog", 519, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 82, 628
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\body_top.v", "Verilog", 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 19, 141
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\bram_cntrl.v", "Verilog", 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 7, 174
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\bram_coef_wr_cntrl.v", "Verilog", 562, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 43, 638
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\bram_ip.v", "Verilog", 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 15, 84
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ce7_comp_top.v", "Verilog", 678, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 89, 115, 882
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\checksum.v", "Verilog", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 11, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\clk_manage.v", "Verilog", 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 7, 47
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\coder_rd_sdram.v", "Verilog", 735, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 27, 807
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\coder_wr_sdram.v", "Verilog", 638, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 52, 735
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\col_lift.v", "Verilog", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 10, 116
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\col_lift_ctrl_lev1.v", "Verilog", 615, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 44, 688
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\col_lift_ctrl_lev234.v", "Verilog", 919, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 57, 1006
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\col_lift_pre.v", "Verilog", 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 11, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\col_ram_ctrl.v", "Verilog", 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 15, 144
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\col_trans_lev1.v", "Verilog", 395, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 40, 466
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\col_trans_lev234.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 46, 519
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\comp_rd_sdram.v", "Verilog", 335, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 46, 407
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\compare.v", "Verilog", 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 13, 229
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\cpu_top.v", "Verilog", 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 34, 380
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\cuter.v", "Verilog", 244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 36, 306
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dat_receive.v", "Verilog", 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 19, 145
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\data_buffer_new.v", "Verilog", 168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 23, 197
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcshift.v", "Verilog", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 7, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcy_sci.v", "Verilog", 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 2, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 150
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\doc\blk_mem_gen_v7_3_vinfo.html", "HTML", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 69, 225
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\example_design\CMD_RAM_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 6, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\example_design\CMD_RAM_exdes.vhd", "VHDL", 0, 60, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 44, 180
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\example_design\CMD_RAM_exdes.xdc", "Xilinx Design Constraints", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 52, 3, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\example_design\CMD_RAM_prod.vhd", "VHDL", 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 42, 276
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 23, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 5, 22, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\CMD_RAM_synth.vhd", "VHDL", 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 55, 323
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\CMD_RAM_tb.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 16, 143
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\addr_gen.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 7, 118
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\bmg_stim_gen.vhd", "VHDL", 0, 309, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 55, 435
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\bmg_tb_pkg.vhd", "VHDL", 0, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 11, 201
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\checker.vhd", "VHDL", 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 16, 162
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\data_gen.vhd", "VHDL", 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 13, 141
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 9, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\functional\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\functional\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 47, 5, 70
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 12, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\random.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 11, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 9, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\timing\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 47, 10, 79
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\timing\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\CMD_RAM\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 11, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\CMD_RAM\summary.log", "Log", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 150
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\doc\blk_mem_gen_v7_3_vinfo.html", "HTML", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 69, 225
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\example_design\E_RAM_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 6, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\example_design\E_RAM_exdes.vhd", "VHDL", 0, 60, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 44, 180
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\example_design\E_RAM_exdes.xdc", "Xilinx Design Constraints", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 52, 3, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\example_design\E_RAM_prod.vhd", "VHDL", 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 42, 276
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 23, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 5, 22, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\E_RAM_synth.vhd", "VHDL", 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 55, 323
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\E_RAM_tb.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 16, 143
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\addr_gen.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 7, 118
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\bmg_stim_gen.vhd", "VHDL", 0, 309, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 55, 435
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\bmg_tb_pkg.vhd", "VHDL", 0, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 11, 201
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\checker.vhd", "VHDL", 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 16, 162
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\data_gen.vhd", "VHDL", 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 13, 141
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 9, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\functional\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\functional\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 47, 5, 70
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 12, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\random.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 11, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 9, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\timing\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 47, 10, 79
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\timing\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\E_RAM\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 11, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\coregen.log", "Log", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\E_RAM\summary.log", "Log", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 150
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\doc\blk_mem_gen_v7_3_vinfo.html", "HTML", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 69, 225
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\example_design\FGM1_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 6, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\example_design\FGM1_exdes.vhd", "VHDL", 0, 60, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 44, 180
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\example_design\FGM1_exdes.xdc", "Xilinx Design Constraints", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 52, 3, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\example_design\FGM1_prod.vhd", "VHDL", 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 42, 276
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 23, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 5, 22, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\FGM1_synth.vhd", "VHDL", 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 55, 323
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\FGM1_tb.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 16, 143
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\addr_gen.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 7, 118
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\bmg_stim_gen.vhd", "VHDL", 0, 309, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 55, 435
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\bmg_tb_pkg.vhd", "VHDL", 0, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 11, 201
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\checker.vhd", "VHDL", 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 16, 162
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\data_gen.vhd", "VHDL", 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 13, 141
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 9, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\functional\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\functional\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 47, 5, 70
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 12, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\random.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 11, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 9, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\timing\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 47, 10, 79
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\timing\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM1\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 11, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 150
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\doc\blk_mem_gen_v7_3_vinfo.html", "HTML", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 69, 225
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\example_design\FGM_RAM_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 6, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\example_design\FGM_RAM_exdes.vhd", "VHDL", 0, 60, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 44, 180
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\example_design\FGM_RAM_exdes.xdc", "Xilinx Design Constraints", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 52, 3, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\example_design\FGM_RAM_prod.vhd", "VHDL", 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 42, 276
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 23, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 5, 22, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\FGM_RAM_synth.vhd", "VHDL", 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 55, 323
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\FGM_RAM_tb.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 16, 143
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\addr_gen.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 7, 118
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\bmg_stim_gen.vhd", "VHDL", 0, 309, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 55, 435
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\bmg_tb_pkg.vhd", "VHDL", 0, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 11, 201
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\checker.vhd", "VHDL", 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 16, 162
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\data_gen.vhd", "VHDL", 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 13, 141
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 9, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\functional\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\functional\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 47, 5, 70
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 12, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\random.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 11, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 9, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\timing\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 47, 10, 79
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\timing\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\FGM_RAM\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 11, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\FGM_RAM\summary.log", "Log", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 150
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\doc\blk_mem_gen_v7_3_vinfo.html", "HTML", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 69, 225
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\example_design\RAM_A_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 6, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\example_design\RAM_A_exdes.vhd", "VHDL", 0, 60, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 44, 180
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\example_design\RAM_A_exdes.xdc", "Xilinx Design Constraints", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 52, 3, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\example_design\RAM_A_prod.vhd", "VHDL", 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 42, 276
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 23, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 5, 22, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\RAM_A_synth.vhd", "VHDL", 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 55, 323
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\RAM_A_tb.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 16, 143
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\addr_gen.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 7, 118
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\bmg_stim_gen.vhd", "VHDL", 0, 309, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 55, 435
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\bmg_tb_pkg.vhd", "VHDL", 0, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 11, 201
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\checker.vhd", "VHDL", 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 16, 162
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\data_gen.vhd", "VHDL", 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 13, 141
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 9, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\functional\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\functional\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 47, 5, 70
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 12, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\random.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 11, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 9, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\timing\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 47, 10, 79
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\timing\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\RAM_A\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 11, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\PACK_RAM\summary.log", "Log", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\IP\summary.log", "Log", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\.VSCodeCounter\2023-01-03_15-13-50\details.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0, 0, 0, 6, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\.VSCodeCounter\2023-01-03_15-13-50\diff-details.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 6, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\.VSCodeCounter\2023-01-03_15-13-50\diff.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 7, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\.VSCodeCounter\2023-01-03_15-13-50\results.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\.VSCodeCounter\2023-01-03_15-13-50\results.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 7, 24
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\CLK_1MHz.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 10, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\CMD\CMD_ANALY.vhd", "VHDL", 0, 583, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 79, 65, 727
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\CMD\CMD_CHECK.v", "Verilog", 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 34, 392
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\CMD\CMD_PROCESS.vhd", "VHDL", 0, 284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 63, 385
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\CMD\CMD_REV.v", "Verilog", 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 39, 414
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DATA_PROCESS.vhd", "VHDL", 0, 442, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 81, 553
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DA_CHANGE.vhd", "VHDL", 0, 165, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 28, 227
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DA_DRV.vhd", "VHDL", 0, 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 20, 243
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DA_NORMAL.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 13, 104
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DA_PROCESS.vhd", "VHDL", 0, 334, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 68, 428
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DA_SCAN.vhd", "VHDL", 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 16, 137
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DA_SCAN_OUT.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 14, 105
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DA_SCAN_PROCESS.vhd", "VHDL", 0, 143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 39, 208
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DA_SCH.vhd", "VHDL", 0, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 12, 116
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\DIV_TIME_EDGE.vhd", "VHDL", 0, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 20, 324
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\D_C7_FPGA.vhd", "VHDL", 0, 700, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 164, 949
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\GC_DATA_PROCESS.vhd", "VHDL", 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 33, 146
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\H_EFI_DATA_PROCESS1.vhd", "VHDL", 0, 79, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 36, 120
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\H_EFI_DATA_PROCESS2.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 36, 120
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\H_FGM_DATA_PROCESS.vhd", "VHDL", 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 35, 126
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\H_PACK_PROCESS.vhd", "VHDL", 0, 554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 50, 681
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\LC_PROCESS.vhd", "VHDL", 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 23, 222
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\L_DATA_PROCESS.vhd", "VHDL", 0, 339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 81, 462
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\L_EFI_DATA_PROCESS.vhd", "VHDL", 0, 80, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 36, 122
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\L_FGM_DATA_PROCESS.vhd", "VHDL", 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 43, 170
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\L_PACK_PROCESS.vhd", "VHDL", 0, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 25, 377
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\L_SEND_PACK.vhd", "VHDL", 0, 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 54, 306
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\MAIN_CTRL.vhd", "VHDL", 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 14, 107
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\PACK_OUTPUT.vhd", "VHDL", 0, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 17, 215
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\PACK_PROCESS.vhd", "VHDL", 0, 311, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 58, 401
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\RST.v", "Verilog", 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 6, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\SC_SEND_CTRL.vhd", "VHDL", 0, 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 20, 260
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\SEND_PACK.vhd", "VHDL", 0, 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 54, 306
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\TEST_PWR.vhd", "VHDL", 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 16, 115
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\TIME_COUNTER.vhd", "VHDL", 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 16, 122
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\TIME_manger\CLK_1MHz.vhd", "VHDL", 0, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 10, 91
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\TIME_manger\CLK_1s.vhd", "VHDL", 0, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 13, 138
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\TIME_manger\CLK_2MHz.vhd", "VHDL", 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 13, 91
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\TIME_manger\DIV_TIME_EDGE.vhd", "VHDL", 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 11, 84
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\TIME_manger\time_counter.vhd", "VHDL", 0, 79, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 17, 134
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dcyip\SRC\uart_tx.v", "Verilog", 150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 22, 215
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\direct_rd_sdram.v", "Verilog", 313, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 35, 385
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dpram32b.v", "Verilog", 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 19, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dual_port_bram.v", "Verilog", 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 12, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dwt_store.v", "Verilog", 288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 35, 354
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\dwt_trans.v", "Verilog", 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 49, 370
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\encode_lenth.v", "Verilog", 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 8, 153
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\fast_look_new_new.v", "Verilog", 1554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 93, 61, 1708
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\frame_head_out.v", "Verilog", 462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 21, 504
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\framing_c.v", "Verilog", 558, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 64, 642
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\gen_frame_syn.v", "Verilog", 349, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 33, 429
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\head_body_add.v", "Verilog", 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 8, 189
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\head_len.v", "Verilog", 164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 24, 204
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\head_top.v", "Verilog", 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 15, 173
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\impact.xsl", "XSL", 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\info_data_separate.v", "Verilog", 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 22, 257
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\interactive_contrl.v", "Verilog", 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 9, 118
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\CMD_RAM.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 150
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\CMD_RAM.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\DCM0.v", "Verilog", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 5, 95
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\DCM0_arwz.ucf", "vivado ucf", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 20
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\DCM1.v", "Verilog", 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 5, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\DCM1_arwz.ucf", "vivado ucf", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 20
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\DCM2.v", "Verilog", 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 5, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\DCM2_arwz.ucf", "vivado ucf", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 20
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\E_RAM.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 150
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\E_RAM.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FGM1.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 150
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FGM1.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192.vhd", "VHDL", 0, 248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 296
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192.vho", "VHDL", 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 104
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\example_design\FIFO2048_8192_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\example_design\FIFO2048_8192_exdes.vhd", "VHDL", 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 152
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\FIFO2048_8192_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\FIFO2048_8192_dverif.vhd", "VHDL", 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 16, 173
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\FIFO2048_8192_pctrl.vhd", "VHDL", 0, 410, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 48, 552
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\FIFO2048_8192_pkg.vhd", "VHDL", 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 353
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\FIFO2048_8192_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\FIFO2048_8192_synth.vhd", "VHDL", 0, 207, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 305
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\FIFO2048_8192_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048_8192\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8.vhd", "VHDL", 0, 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 293
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8.vho", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 102
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\example_design\FIFO2048x8_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\example_design\FIFO2048x8_exdes.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 149
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\FIFO2048x8_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\FIFO2048x8_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\FIFO2048x8_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\FIFO2048x8_pkg.vhd", "VHDL", 0, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 352
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\FIFO2048x8_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\FIFO2048x8_synth.vhd", "VHDL", 0, 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 303
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\FIFO2048x8_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO2048x8\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048.vhd", "VHDL", 0, 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 293
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048.vho", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 102
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\example_design\FIFO8192_2048_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\example_design\FIFO8192_2048_exdes.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 149
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\FIFO8192_2048_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\FIFO8192_2048_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\FIFO8192_2048_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\FIFO8192_2048_pkg.vhd", "VHDL", 0, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 352
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\FIFO8192_2048_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\FIFO8192_2048_synth.vhd", "VHDL", 0, 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 303
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\FIFO8192_2048_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192_2048\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8.vhd", "VHDL", 0, 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 293
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8.vho", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 102
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\example_design\FIFO8192x8_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\example_design\FIFO8192x8_exdes.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 149
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\FIFO8192x8_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\FIFO8192x8_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\FIFO8192x8_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\FIFO8192x8_pkg.vhd", "VHDL", 0, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 352
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\FIFO8192x8_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\FIFO8192x8_synth.vhd", "VHDL", 0, 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 303
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\FIFO8192x8_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO8192x8\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\example_design\FIFO_sim_sdram_exdes.ucf", "vivado ucf", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\example_design\FIFO_sim_sdram_exdes.vhd", "VHDL", 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 19, 133
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\FIFO_sim_sdram_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\FIFO_sim_sdram_dverif.vhd", "VHDL", 0, 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 151
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\FIFO_sim_sdram_pctrl.vhd", "VHDL", 0, 329, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 91, 42, 462
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\FIFO_sim_sdram_pkg.vhd", "VHDL", 0, 251, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 348
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\FIFO_sim_sdram_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\FIFO_sim_sdram_synth.vhd", "VHDL", 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 24, 266
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\FIFO_sim_sdram_tb.vhd", "VHDL", 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 24, 196
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 3, 70
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\FIFO_sim_sdram\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 3, 70
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16.v", "Verilog", 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 187
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16.vhd", "VHDL", 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 153
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16.vho", "VHDL", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 84
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\doc\blk_mem_gen_v7_3_vinfo.html", "HTML", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 69, 225
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\example_design\RAM512X16_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 6, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\example_design\RAM512X16_exdes.vhd", "VHDL", 0, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 44, 183
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\example_design\RAM512X16_exdes.xdc", "Xilinx Design Constraints", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 52, 3, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\example_design\RAM512X16_prod.vhd", "VHDL", 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 42, 278
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 23, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 5, 22, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\RAM512X16_synth.vhd", "VHDL", 0, 193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 55, 330
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\RAM512X16_tb.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 16, 143
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\addr_gen.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 7, 118
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\bmg_stim_gen.vhd", "VHDL", 0, 311, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 55, 437
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\bmg_tb_pkg.vhd", "VHDL", 0, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 11, 201
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\checker.vhd", "VHDL", 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 16, 162
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\data_gen.vhd", "VHDL", 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 13, 141
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 9, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\functional\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\functional\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 47, 5, 70
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 12, 24
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\random.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 11, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 9, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\timing\simulate_ncsim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 47, 10, 79
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\timing\simulate_vcs.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 47, 7, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM512X16\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 11, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM_A.vhd", "VHDL", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 150
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\RAM_A.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 8, 82
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo.v", "Verilog", 445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 490
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo.vhd", "VHDL", 0, 239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 287
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo.vho", "VHDL", 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 98
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\example_design\asyn_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\example_design\asyn_fifo_exdes.vhd", "VHDL", 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 143
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\asyn_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\asyn_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\asyn_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\asyn_fifo_pkg.vhd", "VHDL", 0, 253, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 350
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\asyn_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\asyn_fifo_synth.vhd", "VHDL", 0, 201, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 299
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\asyn_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\asyn_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\example_design\blk_len_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\example_design\blk_len_fifo_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blk_len_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo.v", "Verilog", 447, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 492
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo.vhd", "VHDL", 0, 242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 290
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo.vho", "VHDL", 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 100
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\example_design\blkout_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\example_design\blkout_fifo_exdes.vhd", "VHDL", 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 146
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\blkout_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\blkout_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\blkout_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\blkout_fifo_pkg.vhd", "VHDL", 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 351
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\blkout_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\blkout_fifo_synth.vhd", "VHDL", 0, 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 301
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\blkout_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\blkout_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\example_design\config_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\example_design\config_fifo_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\config_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\config_fifo_dverif.vhd", "VHDL", 0, 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 151
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\config_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\config_fifo_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\config_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\config_fifo_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\config_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\config_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\coregen.log", "Log", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo.v", "Verilog", 447, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 492
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo.vhd", "VHDL", 0, 242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 290
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo.vho", "VHDL", 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 100
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\example_design\downstream_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\example_design\downstream_fifo_exdes.vhd", "VHDL", 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 146
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\downstream_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\downstream_fifo_dverif.vhd", "VHDL", 0, 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 151
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\downstream_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\downstream_fifo_pkg.vhd", "VHDL", 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 351
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\downstream_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\downstream_fifo_synth.vhd", "VHDL", 0, 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 301
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\downstream_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\downstream_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8.vhd", "VHDL", 0, 242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 290
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8.vho", "VHDL", 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 100
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\example_design\fifo1024x16_to_2048x8_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\example_design\fifo1024x16_to_2048x8_exdes.vhd", "VHDL", 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 146
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\fifo1024x16_to_2048x8_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\fifo1024x16_to_2048x8_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\fifo1024x16_to_2048x8_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\fifo1024x16_to_2048x8_pkg.vhd", "VHDL", 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 351
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\fifo1024x16_to_2048x8_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\fifo1024x16_to_2048x8_synth.vhd", "VHDL", 0, 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 301
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\fifo1024x16_to_2048x8_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo1024x16_to_2048x8\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\example_design\fifo_16width_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\example_design\fifo_16width_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\fifo_16width_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\fifo_16width_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\fifo_16width_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\fifo_16width_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\fifo_16width_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\fifo_16width_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\fifo_16width_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_16width\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\example_design\fifo_26width_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\example_design\fifo_26width_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\fifo_26width_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\fifo_26width_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\fifo_26width_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\fifo_26width_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\fifo_26width_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\fifo_26width_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\fifo_26width_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_26width\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_8width.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\fifo_generator_v9_3.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\frame_len_fifo.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\example_design\img_type_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\example_design\img_type_fifo_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\img_type_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\img_type_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\img_type_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\img_type_fifo_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\img_type_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\img_type_fifo_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\img_type_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\img_type_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\pktout_fifo.v", "Verilog", 449, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 494
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\summary.log", "Log", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ipcore_dir\xaw2verilog.log", "Log", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\lev_1.v", "Verilog", 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 27, 183
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\lev_2_3_4.v", "Verilog", 241, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 27, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\lev_sel.v", "Verilog", 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 10, 142
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\lift_pro_element.v", "Verilog", 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 26, 227
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\lvde_send.v", "Verilog", 491, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63, 59, 613
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\lvds_8b10b_decoder.v", "Verilog", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\mid_top.v", "Verilog", 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 21, 177
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\modelsim.ini", "Ini", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 262, 0, 1377, 300, 1939
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\mselen_input_buffer.v", "Verilog", 168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 24, 217
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\mul_cut.v", "Verilog", 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 6, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\mult_pepi.v", "Verilog", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 7, 43
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\multiplex_envsettings.html", "HTML", 0, 0, 400, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 400
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\multiply_ip.v", "Verilog", 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 5, 34
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\mux_apart_4level.v", "Verilog", 268, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 26, 317
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pack_control.v", "Verilog", 399, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 22, 454
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pack_head_INPL_mux.v", "Verilog", 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 14, 187
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pack_head_INPL_top.v", "Verilog", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 7, 94
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pack_head_char.v", "Verilog", 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 7, 161
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\packing.v", "Verilog", 636, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 82, 741
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\packing_sel.v", "Verilog", 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 26, 203
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\packing_top.v", "Verilog", 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 14, 139
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\param_pre.v", "Verilog", 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 16, 93
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pass_len_code.v", "Verilog", 412, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 18, 449
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pkg_ce5.vhd", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 12, 39
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pkt_info_analysis.v", "Verilog", 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 15, 146
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pkt_pre_top.v", "Verilog", 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 28, 235
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pkt_wr_fifo.v", "Verilog", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 10, 72
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\planAhead.ngc2edif.log", "Log", 0, 0, 0, 0, 0, 52, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 58
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pre_rd_fifo.v", "Verilog", 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 16, 97
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pre_wr_config_fifo.v", "Verilog", 157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 20, 200
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pre_wr_direct_dpram.v", "Verilog", 230, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 26, 286
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pre_wr_img_dpram.v", "Verilog", 248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 22, 304
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pre_wr_info_dpram.v", "Verilog", 251, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 21, 302
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pre_wr_sdram.v", "Verilog", 744, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 48, 848
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\pre_wr_sdram_top.v", "Verilog", 216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 33, 272
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\processing_analysis.v", "Verilog", 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 44, 446
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ram_LL_adjust.v", "Verilog", 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 15, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\ram_col_lift.v", "Verilog", 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\rd_MQ_dpram.v", "Verilog", 331, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 15, 365
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\rd_MQ_sdram.v", "Verilog", 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 21, 215
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\rd_dpram.v", "Verilog", 533, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 62, 628
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\rd_one_pack_ctrl.v", "Verilog", 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 12, 206
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\rdblk3_wrblkband.v", "Verilog", 882, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 36, 956
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\reg_top_xs_cntrl.v", "Verilog", 52, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 6, 77
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\row_lift.v", "Verilog", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 10, 102
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\row_lift_ctrl.v", "Verilog", 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 38, 373
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\row_lift_ctrl_lev234.v", "Verilog", 544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 52, 637
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\row_trans.v", "Verilog", 235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 31, 293
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\row_trans_lev234.v", "Verilog", 316, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 40, 388
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\rst_manage.v", "Verilog", 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 8, 60
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\rw_sdramcntrl.vhd", "VHDL", 0, 1036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 21, 1133
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\schedule.v", "Verilog", 324, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 12, 395
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\sdram2dpram.v", "Verilog", 480, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 43, 550
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\sdram_contrl_top.vhd", "VHDL", 0, 1104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 24, 1206
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\sdram_contrl_top_fifo.v", "Verilog", 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 10, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\sdram_contrl_top_sim.v", "Verilog", 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 2, 52
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\sdram_read.vhd", "VHDL", 0, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 12, 154
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\sdram_schedule_v1_fifohuansdram.v", "Verilog", 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 6, 40
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\sdram_write.vhd", "VHDL", 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 13, 159
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\serdes_5x.v", "Verilog", 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 9, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\deserdes.v", "Verilog", 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 8, 72
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\encoder.v", "Verilog", 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 33, 348
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\encoeder_8b10b.v", "Verilog", 94, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 14, 130
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\k_encoder.v", "Verilog", 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 6, 112
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo.vhd", "VHDL", 0, 242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 290
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo.vho", "VHDL", 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 100
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\example_design\kw_pkg_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\example_design\kw_pkg_fifo_exdes.vhd", "VHDL", 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 146
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\kw_pkg_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\kw_pkg_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\kw_pkg_fifo_pctrl.vhd", "VHDL", 0, 438, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 48, 583
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\kw_pkg_fifo_pkg.vhd", "VHDL", 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 351
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\kw_pkg_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\kw_pkg_fifo_synth.vhd", "VHDL", 0, 204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 302
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\kw_pkg_fifo_tb.vhd", "VHDL", 0, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 27, 216
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\kw_pkg_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\lddata_pkg.v", "Verilog", 727, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 189, 88, 1004
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\8b10_tx\lvdstxmodule.v", "Verilog", 89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 84, 41, 214
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\MT48LC128M4A2.v", "Verilog", 761, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 167, 125, 1053
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\SDRAM32x16_model.v", "Verilog", 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 5, 59
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\Sci_RS422_TX.v", "Verilog", 287, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 11, 331
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\tb_AD_ctrl.v", "Verilog", 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 16, 111
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\simulation\tb_TOP.v", "Verilog", 288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 47, 369
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\slope_calc_m.v", "Verilog", 377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 24, 435
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\slope_control.v", "Verilog", 402, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 36, 490
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\slope_rd_dpram.v", "Verilog", 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 12, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\slope_rd_dpram_ctrl.v", "Verilog", 200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 20, 224
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\slope_thrs_calc.v", "Verilog", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 12, 99
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\slope_top_new.v", "Verilog", 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 39, 245
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\stack.v", "Verilog", 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 21, 152
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\state_reg.v", "Verilog", 212, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 27, 271
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\subband_apart.v", "Verilog", 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 21, 153
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\syn_dpram_ip.v", "Verilog", 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 18, 119
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\tb.v", "Verilog", 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 13, 195
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\tb_v.v", "Verilog", 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 18, 167
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\tbb.v", "Verilog", 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 12, 187
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\time_guard.v", "Verilog", 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 9, 156
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\time_ucf.ucf", "vivado ucf", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 3, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\truncate.v", "Verilog", 445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 40, 553
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\uart_tx.v", "Verilog", 150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 22, 215
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\usage_statistics_webtalk.html", "HTML", 0, 0, 6170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 758, 6928
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\webtalk.log", "Log", 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 17
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\webtalk_impact.xml", "XML", 0, 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 3, 1, 59
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\webtalk_pn.xml", "XML", 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 3, 1, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@a@d@d@e@r16@b@i@t@c_new\_primary.vhd", "VHDL", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 11
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@buffer_@r@a@m0\_primary.vhd", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@buffer_@r@a@m1\_primary.vhd", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@c@l@k@manage\_primary.vhd", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@c@m@d_@r@e@v\_primary.vhd", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@c@o@d@e@r_@r@d@a@t_top\_primary.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@c@o@d@e@r_@w@d@a@t_top\_primary.vhd", "VHDL", 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@c@o@d@e@r_top\_primary.vhd", "VHDL", 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 51
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@c@o@m@p_@t@o@p\_primary.vhd", "VHDL", 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 94
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@c@u@t_top\_primary.vhd", "VHDL", 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 42
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@code\_primary.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 35
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@create_@buffer0\_primary.vhd", "VHDL", 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 28
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@create_@buffer1\_primary.vhd", "VHDL", 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 25
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@create_@layer\_primary.vhd", "VHDL", 0, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 43
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@create_@layer_@controller\_primary.vhd", "VHDL", 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 28
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@create_@tag_@tree\_primary.vhd", "VHDL", 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 44
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@create_@value\_primary.vhd", "VHDL", 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@d@e@c@o@d@e\_primary.vhd", "VHDL", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@d@m_rv\_primary.vhd", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@d@w@t_top\_primary.vhd", "VHDL", 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 34
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@encode_@controller\_primary.vhd", "VHDL", 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 33
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@encode_@tag_@tree\_primary.vhd", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 32
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@encoder\_primary.vhd", "VHDL", 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 28
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@f@i@f@o\_primary.vhd", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@global_@control\_primary.vhd", "VHDL", 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@k_scale\_primary.vhd", "VHDL", 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 41
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@k_scale_ctrl\_primary.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@kcoef_select\_primary.vhd", "VHDL", 0, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@l@l_adjust\_primary.vhd", "VHDL", 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 38
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@l@l_ctrl\_primary.vhd", "VHDL", 0, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 43
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@leaf_@address\_primary.vhd", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@leaf_@r@a@m0\_primary.vhd", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@m@q_coder\_primary.vhd", "VHDL", 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@output_@b@r@a@m\_primary.vhd", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@p2_refresh_new\_primary.vhd", "VHDL", 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@p3_byteout_new\_primary.vhd", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@p@k_top\_primary.vhd", "VHDL", 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 34
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@parent_@r@a@m0\_primary.vhd", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@r@o@m_162x8\_primary.vhd", "VHDL", 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 18
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@r@s@t\_primary.vhd", "VHDL", 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@result_@store\_primary.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@s@y@n_@b@c\_primary.vhd", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@state@update_and_@parameter@prepare\_primary.vhd", "VHDL", 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 52
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@t1_top\_primary.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@t2_top\_primary.vhd", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 32
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@t@a@g_@t@r@e@e_top\_primary.vhd", "VHDL", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@t@o@p\_primary.vhd", "VHDL", 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 70
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\@tag_@tree_@r@a@m_@ctrl\_primary.vhd", "VHDL", 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 42
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\apart_data\_primary.vhd", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 26
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\assembly_comp\_primary.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\assembly_direct\_primary.vhd", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 32
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\asyn_dpram_ip\_primary.vhd", "VHDL", 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 28
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\bit_add\_primary.vhd", "VHDL", 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 37
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\bit_extract\_primary.vhd", "VHDL", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 12
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\blk_num_ram_ctrl\_primary.vhd", "VHDL", 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\blocking_c\_primary.vhd", "VHDL", 0, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\body_top\_primary.vhd", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 32
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\bram_cntrl\_primary.vhd", "VHDL", 0, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\bram_coef_wr_cntrl\_primary.vhd", "VHDL", 0, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 40
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\bram_ip\_primary.vhd", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\ce7_comp_top\_primary.vhd", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 32
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\checksum\_primary.vhd", "VHDL", 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\cmd_check\_primary.vhd", "VHDL", 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 18
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\coder_rd_sdram\_primary.vhd", "VHDL", 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 38
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\coder_wr_sdram\_primary.vhd", "VHDL", 0, 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 58
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\col_lift\_primary.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\col_lift_ctrl_lev1\_primary.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 66
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\col_lift_ctrl_lev234\_primary.vhd", "VHDL", 0, 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\col_lift_pre\_primary.vhd", "VHDL", 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 29
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\col_ram_ctrl\_primary.vhd", "VHDL", 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 33
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\col_trans_lev1\_primary.vhd", "VHDL", 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 39
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\col_trans_lev234\_primary.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\comp_rd_sdram\_primary.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\compare\_primary.vhd", "VHDL", 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 31
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\cpu_top\_primary.vhd", "VHDL", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\cuter\_primary.vhd", "VHDL", 0, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 40
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\dat_receive\_primary.vhd", "VHDL", 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\data_buffer_new\_primary.vhd", "VHDL", 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 30
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\dcshift\_primary.vhd", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\direct_rd_sdram\_primary.vhd", "VHDL", 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 47
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\dpram32b\_primary.vhd", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 26
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\dual_port_bram\_primary.vhd", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\dwt_store\_primary.vhd", "VHDL", 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 41
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\dwt_trans\_primary.vhd", "VHDL", 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 39
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\encode_lenth\_primary.vhd", "VHDL", 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\fast_look_new_new\_primary.vhd", "VHDL", 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 18
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\frame_head_out\_primary.vhd", "VHDL", 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\framing_c\_primary.vhd", "VHDL", 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 60
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\gen_frame_syn\_primary.vhd", "VHDL", 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 52
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\glbl\_primary.vhd", "VHDL", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 12
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\head_body_add\_primary.vhd", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\head_len\_primary.vhd", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\head_top\_primary.vhd", "VHDL", 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 33
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\info_data_separate\_primary.vhd", "VHDL", 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 37
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\interactive_contrl\_primary.vhd", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\lev_1\_primary.vhd", "VHDL", 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 28
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\lev_2_3_4\_primary.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\lev_sel\_primary.vhd", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\lift_pro_element\_primary.vhd", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 32
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\lvde_send\_primary.vhd", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\lvds_8b10b_decoder\_primary.vhd", "VHDL", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\mid_top\_primary.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\mselen_input_buffer\_primary.vhd", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\mul_cut\_primary.vhd", "VHDL", 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\mult_pepi\_primary.vhd", "VHDL", 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\multiply_ip\_primary.vhd", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\mux_apart_4level\_primary.vhd", "VHDL", 0, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 43
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pack_control\_primary.vhd", "VHDL", 0, 70, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pack_head_@i@n@p@l_mux\_primary.vhd", "VHDL", 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 37
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pack_head_@i@n@p@l_top\_primary.vhd", "VHDL", 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 18
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pack_head_char\_primary.vhd", "VHDL", 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 33
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\packing\_primary.vhd", "VHDL", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\packing_sel\_primary.vhd", "VHDL", 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 33
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\packing_top\_primary.vhd", "VHDL", 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 31
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\param_pre\_primary.vhd", "VHDL", 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 25
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pass_len_code\_primary.vhd", "VHDL", 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 37
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pkt_info_analysis\_primary.vhd", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pkt_pre_top\_primary.vhd", "VHDL", 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 38
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pkt_wr_fifo\_primary.vhd", "VHDL", 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pre_rd_fifo\_primary.vhd", "VHDL", 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pre_wr_config_fifo\_primary.vhd", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pre_wr_direct_dpram\_primary.vhd", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pre_wr_img_dpram\_primary.vhd", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pre_wr_info_dpram\_primary.vhd", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pre_wr_sdram\_primary.vhd", "VHDL", 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 46
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\pre_wr_sdram_top\_primary.vhd", "VHDL", 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 47
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\processing_analysis\_primary.vhd", "VHDL", 0, 52, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 53
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\ram_@l@l_adjust\_primary.vhd", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\ram_col_lift\_primary.vhd", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\rd_@m@q_dpram\_primary.vhd", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\rd_@m@q_sdram\_primary.vhd", "VHDL", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\rd_dpram\_primary.vhd", "VHDL", 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\rd_one_pack_ctrl\_primary.vhd", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\rdblk3_wrblkband\_primary.vhd", "VHDL", 0, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 75
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\reg_top_xs_cntrl\_primary.vhd", "VHDL", 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\row_lift\_primary.vhd", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\row_lift_ctrl\_primary.vhd", "VHDL", 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 44
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\row_lift_ctrl_lev234\_primary.vhd", "VHDL", 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\row_trans\_primary.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 35
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\row_trans_lev234\_primary.vhd", "VHDL", 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 46
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\rst_manage\_primary.vhd", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\schedule\_primary.vhd", "VHDL", 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\sdram2dpram\_primary.vhd", "VHDL", 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 44
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\sdram_contrl_top_fifo\_primary.vhd", "VHDL", 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 29
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\serdes_5x\_primary.vhd", "VHDL", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\slope_calc_m\_primary.vhd", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\slope_control\_primary.vhd", "VHDL", 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\slope_rd_dpram\_primary.vhd", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\slope_rd_dpram_ctrl\_primary.vhd", "VHDL", 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 18
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\slope_thrs_calc\_primary.vhd", "VHDL", 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\slope_top_new\_primary.vhd", "VHDL", 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 20
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\stack\_primary.vhd", "VHDL", 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 28
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\state_reg\_primary.vhd", "VHDL", 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 25
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\subband_apart\_primary.vhd", "VHDL", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\syn_dpram_ip\_primary.vhd", "VHDL", 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 33
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\tb\_primary.vhd", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\tb_v\_primary.vhd", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\tbb\_primary.vhd", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\time_guard\_primary.vhd", "VHDL", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\truncate\_primary.vhd", "VHDL", 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 34
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\uart_tx\_primary.vhd", "VHDL", 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\wr_b_dpram\_primary.vhd", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\wr_blen_bram_ctrl\_primary.vhd", "VHDL", 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 18
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\wr_blk3_dpram\_primary.vhd", "VHDL", 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 25
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\wr_blocks_c\_primary.vhd", "VHDL", 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 46
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\wr_dpram\_primary.vhd", "VHDL", 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\wr_slope_dpram\_primary.vhd", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\write_to_ram\_primary.vhd", "VHDL", 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 38
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\write_to_sdram\_primary.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 66
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\work\zerobit_out\_primary.vhd", "VHDL", 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 29
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\wr_b_dpram.v", "Verilog", 201, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 13, 274
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\wr_blen_bram_ctrl.v", "Verilog", 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 10, 83
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\wr_blk3_dpram.v", "Verilog", 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 15, 213
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\wr_blocks_c.v", "Verilog", 495, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 75, 598
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\wr_dpram.v", "Verilog", 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 22, 170
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\wr_slope_dpram.v", "Verilog", 88, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 9, 116
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\write_to_ram.v", "Verilog", 464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 39, 544
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\write_to_sdram.v", "Verilog", 1520, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 79, 1655
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xaw2verilog.log", "Log", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xaw2vhdl.log", "Log", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\DCM_CASCADE.v", "Verilog", 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 5, 175
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\DCM_CASCADE_arwz.ucf", "vivado ucf", 0, 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 1, 36
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo.v", "Verilog", 445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 490
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo.vhd", "VHDL", 0, 239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 287
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo.vho", "VHDL", 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 98
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\example_design\asyn_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\example_design\asyn_fifo_exdes.vhd", "VHDL", 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 143
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\asyn_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\asyn_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\asyn_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\asyn_fifo_pkg.vhd", "VHDL", 0, 253, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 350
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\asyn_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\asyn_fifo_synth.vhd", "VHDL", 0, 201, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 299
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\asyn_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\asyn_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\example_design\blk_len_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\example_design\blk_len_fifo_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\blk_len_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blk_len_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo.v", "Verilog", 447, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 492
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo.vhd", "VHDL", 0, 242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 290
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo.vho", "VHDL", 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 100
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\example_design\blkout_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\example_design\blkout_fifo_exdes.vhd", "VHDL", 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 146
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\blkout_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\blkout_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\blkout_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\blkout_fifo_pkg.vhd", "VHDL", 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 351
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\blkout_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\blkout_fifo_synth.vhd", "VHDL", 0, 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 301
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\blkout_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\blkout_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\example_design\config_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\example_design\config_fifo_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\config_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\config_fifo_dverif.vhd", "VHDL", 0, 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 151
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\config_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\config_fifo_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\config_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\config_fifo_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\config_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\config_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\coregen.log", "Log", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo.v", "Verilog", 447, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 492
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo.vhd", "VHDL", 0, 242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 290
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo.vho", "VHDL", 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 100
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\example_design\downstream_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\example_design\downstream_fifo_exdes.vhd", "VHDL", 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 146
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\downstream_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\downstream_fifo_dverif.vhd", "VHDL", 0, 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 151
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\downstream_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\downstream_fifo_pkg.vhd", "VHDL", 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 351
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\downstream_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\downstream_fifo_synth.vhd", "VHDL", 0, 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 301
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\downstream_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\downstream_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\example_design\fifo_16width_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\example_design\fifo_16width_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\fifo_16width_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\fifo_16width_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\fifo_16width_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\fifo_16width_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\fifo_16width_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\fifo_16width_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\fifo_16width_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_16width\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\example_design\fifo_26width_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\example_design\fifo_26width_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\fifo_26width_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\fifo_26width_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\fifo_26width_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\fifo_26width_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\fifo_26width_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\fifo_26width_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\fifo_26width_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_26width\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\example_design\fifo_8width_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\example_design\fifo_8width_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\fifo_8width_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\fifo_8width_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\fifo_8width_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\fifo_8width_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\fifo_8width_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\fifo_8width_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\fifo_8width_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_8width\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\example_design\fifo_generator_v9_3_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\example_design\fifo_generator_v9_3_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\fifo_generator_v9_3_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\fifo_generator_v9_3_dverif.vhd", "VHDL", 0, 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 151
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\fifo_generator_v9_3_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\fifo_generator_v9_3_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\fifo_generator_v9_3_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\fifo_generator_v9_3_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\fifo_generator_v9_3_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 14, 0, 0, 0, 0, 0, 47, 5, 66
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 6, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 64, 0, 0, 0, 0, 0, 0, 0, 5, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\fifo_generator_v9_3\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\example_design\frame_len_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\example_design\frame_len_fifo_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\frame_len_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\frame_len_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\frame_len_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\frame_len_fifo_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\frame_len_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\frame_len_fifo_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\frame_len_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\frame_len_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 488
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo.vhd", "VHDL", 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 284
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo.vho", "VHDL", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\example_design\img_type_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\example_design\img_type_fifo_exdes.vhd", "VHDL", 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\img_type_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\img_type_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\img_type_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\img_type_fifo_pkg.vhd", "VHDL", 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 349
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\img_type_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\img_type_fifo_synth.vhd", "VHDL", 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\img_type_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\img_type_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo.v", "Verilog", 449, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 9, 494
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo.vhd", "VHDL", 0, 245, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 6, 293
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo.vho", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 9, 102
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\doc\fifo_generator_v9_3_vinfo.html", "HTML", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 248
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\example_design\pktout_fifo_exdes.ucf", "vivado ucf", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 2, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\example_design\pktout_fifo_exdes.vhd", "VHDL", 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 20, 149
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\implement\implement.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\implement\implement.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 50, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\implement\implement_synplify.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 17, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\implement\implement_synplify.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 50, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\implement\planAhead_ise.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 2, 55
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\implement\planAhead_ise.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 49, 2, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\functional\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 5, 63
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\functional\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 47, 5, 65
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\functional\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\functional\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\functional\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 6, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\functional\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 5, 68
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\functional\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\pktout_fifo_dgen.vhd", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 12, 124
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\pktout_fifo_dverif.vhd", "VHDL", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 15, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\pktout_fifo_pctrl.vhd", "VHDL", 0, 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 47, 542
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\pktout_fifo_pkg.vhd", "VHDL", 0, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 16, 352
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\pktout_fifo_rng.vhd", "VHDL", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 6, 101
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\pktout_fifo_synth.vhd", "VHDL", 0, 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 26, 303
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\pktout_fifo_tb.vhd", "VHDL", 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 25, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\timing\simulate_isim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 5, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\timing\simulate_isim.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 47, 5, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\timing\simulate_mti.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\timing\simulate_mti.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 47, 2, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\timing\simulate_ncsim.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 6, 73
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\timing\simulate_vcs.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 5, 67
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\pktout_fifo\simulation\timing\wave_ncsim.sv", "System Verilog", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 3, 71
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\ipcore_dir\xaw2verilog.log", "Log", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\.VSCodeCounter\2023-01-03_15-15-03\details.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 170, 0, 0, 0, 0, 6, 176
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\.VSCodeCounter\2023-01-03_15-15-03\diff-details.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 6, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\.VSCodeCounter\2023-01-03_15-15-03\diff.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 7, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\.VSCodeCounter\2023-01-03_15-15-03\results.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\.VSCodeCounter\2023-01-03_15-15-03\results.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 7, 29
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\assembly_comp.v", "Verilog", 266, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 32, 368
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\assembly_direct.v", "Verilog", 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 13, 120
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\blocking_c.v", "Verilog", 519, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 82, 628
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\direct_rd_sdram.v", "Verilog", 318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 36, 392
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\framing_c.v", "Verilog", 506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 64, 610
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\packing.v", "Verilog", 627, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 44, 694
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\packing_sel.v", "Verilog", 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 26, 203
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\packing_top.v", "Verilog", 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 14, 139
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\processing_analysis.v", "Verilog", 386, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 44, 453
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\back\wr_blocks_c.v", "Verilog", 487, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 75, 590
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\ce7_comp_top.v", "Verilog", 662, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 103, 115, 880
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Buffer_RAM0.v", "Verilog", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 11, 85
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Buffer_RAM1.v", "Verilog", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 9, 80
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\CODER_RDAT_top.v", "Verilog", 479, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 22, 526
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\CODER_WDAT_top.v", "Verilog", 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 18, 216
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\CODER_top.v", "Verilog", 174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 14, 209
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\COMP_TOP.v", "Verilog", 418, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 335, 48, 801
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\CUT_top.v", "Verilog", 191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 33, 243
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Code.v", "Verilog", 520, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 570
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Create_Buffer0.v", "Verilog", 260, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 273
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Create_Buffer1.v", "Verilog", 261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 274
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Create_Layer.v", "Verilog", 387, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 37, 430
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Create_Layer_Controller.v", "Verilog", 433, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 460
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Create_Tag_Tree.v", "Verilog", 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 126
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Create_Value.v", "Verilog", 75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5, 81
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\DWT_top.v", "Verilog", 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 11, 136
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Encode_Controller.v", "Verilog", 289, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 41, 334
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Encode_Tag_Tree.v", "Verilog", 86, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 11, 99
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Encoder.v", "Verilog", 397, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 419
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\FIFO.v", "Verilog", 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 122
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Global_Control.v", "Verilog", 431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 480
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\K_scale.v", "Verilog", 251, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 26, 309
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\K_scale_ctrl.v", "Verilog", 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 20, 336
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Kcoef_select.v", "Verilog", 138, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 15, 176
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\LL_adjust.v", "Verilog", 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 21, 175
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\LL_ctrl.v", "Verilog", 401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 37, 464
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Leaf_Address.v", "Verilog", 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Leaf_RAM0.v", "Verilog", 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 11, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\MQ_coder.v", "Verilog", 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 17, 140
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Output_BRAM.v", "Verilog", 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13, 38
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\P2_refresh_new.v", "Verilog", 190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 37, 396
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\P3_byteout_new.v", "Verilog", 428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 25, 476
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\PK_top.v", "Verilog", 218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 24, 266
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Parent_RAM0.v", "Verilog", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 12, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\ROM_162x8.v", "Verilog", 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 11, 50
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Result_Store.v", "Verilog", 743, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 77, 838
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\StateUpdate_and_ParameterPrepare.v", "Verilog", 527, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63, 590
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\T1_top.v", "Verilog", 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 319
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\T2_top.v", "Verilog", 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 27, 253
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\TAG_TREE_top.v", "Verilog", 227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 25, 261
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\Tag_Tree_RAM_Ctrl.v", "Verilog", 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 199, 30, 356
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\apart_data.v", "Verilog", 95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 19, 137
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\bit_add.v", "Verilog", 1196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 23, 1244
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\blk_num_ram_ctrl.v", "Verilog", 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 12, 90
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\body_top.v", "Verilog", 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 19, 141
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\bram_cntrl.v", "Verilog", 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 7, 174
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\bram_coef_wr_cntrl.v", "Verilog", 562, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 43, 638
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\bram_ip.v", "Verilog", 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 15, 84
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\coder_rd_sdram.v", "Verilog", 735, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 27, 807
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\coder_wr_sdram.v", "Verilog", 638, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 52, 735
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\col_lift.v", "Verilog", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 10, 116
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\col_lift_ctrl_lev1.v", "Verilog", 615, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 44, 688
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\col_lift_ctrl_lev234.v", "Verilog", 919, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 57, 1006
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\col_lift_pre.v", "Verilog", 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 11, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\col_ram_ctrl.v", "Verilog", 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 15, 144
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\col_trans_lev1.v", "Verilog", 395, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 40, 466
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\col_trans_lev234.v", "Verilog", 443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 46, 519
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\compare.v", "Verilog", 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 13, 229
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\cuter.v", "Verilog", 244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 36, 306
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\data_buffer_new.v", "Verilog", 168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 23, 197
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\dcshift.v", "Verilog", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 7, 62
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\dual_port_bram.v", "Verilog", 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 12, 56
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\dwt_store.v", "Verilog", 288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 35, 354
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\dwt_trans.v", "Verilog", 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 49, 370
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\encode_lenth.v", "Verilog", 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 8, 153
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\fast_look_new_new.v", "Verilog", 1554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 93, 61, 1708
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\frame_head_out.v", "Verilog", 462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 21, 504
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\head_body_add.v", "Verilog", 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 8, 189
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\head_len.v", "Verilog", 164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 24, 204
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\head_top.v", "Verilog", 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 15, 173
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\interactive_contrl.v", "Verilog", 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 9, 118
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\lev_1.v", "Verilog", 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 27, 183
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\lev_2_3_4.v", "Verilog", 241, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 27, 297
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\lev_sel.v", "Verilog", 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 10, 142
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\lift_pro_element.v", "Verilog", 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 26, 227
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\mselen_input_buffer.v", "Verilog", 168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 24, 217
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\mul_cut.v", "Verilog", 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 6, 69
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\mult_pepi.v", "Verilog", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 7, 43
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\multiply_ip.v", "Verilog", 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 5, 34
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\mux_apart_4level.v", "Verilog", 268, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 26, 317
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\pack_control.v", "Verilog", 399, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 22, 454
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\pack_head_INPL_mux.v", "Verilog", 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 14, 187
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\pack_head_INPL_top.v", "Verilog", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 7, 94
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\pack_head_char.v", "Verilog", 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 7, 161
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\pass_len_code.v", "Verilog", 412, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 18, 449
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\ram_LL_adjust.v", "Verilog", 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 15, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\ram_col_lift.v", "Verilog", 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 16, 88
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\rd_MQ_dpram.v", "Verilog", 331, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 15, 365
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\rd_MQ_sdram.v", "Verilog", 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 21, 215
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\rd_one_pack_ctrl.v", "Verilog", 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 12, 206
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\rdblk3_wrblkband.v", "Verilog", 882, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 36, 956
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\reg_top_xs_cntrl.v", "Verilog", 52, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 6, 77
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\row_lift.v", "Verilog", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 10, 102
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\row_lift_ctrl.v", "Verilog", 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 38, 373
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\row_lift_ctrl_lev234.v", "Verilog", 544, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 52, 637
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\row_trans.v", "Verilog", 235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 31, 293
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\row_trans_lev234.v", "Verilog", 316, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 40, 388
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\schedule.v", "Verilog", 324, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 12, 395
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\sdram2dpram.v", "Verilog", 480, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 43, 550
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\slope_calc_m.v", "Verilog", 377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 24, 435
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\slope_control.v", "Verilog", 402, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 36, 490
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\slope_rd_dpram.v", "Verilog", 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 12, 57
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\slope_rd_dpram_ctrl.v", "Verilog", 200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 20, 224
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\slope_thrs_calc.v", "Verilog", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 12, 99
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\slope_top_new.v", "Verilog", 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 39, 245
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\stack.v", "Verilog", 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 21, 152
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\subband_apart.v", "Verilog", 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 21, 153
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\syn_dpram_ip.v", "Verilog", 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 18, 119
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\truncate.v", "Verilog", 445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 40, 553
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\wr_b_dpram.v", "Verilog", 201, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 13, 274
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\wr_blen_bram_ctrl.v", "Verilog", 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 10, 83
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\wr_blk3_dpram.v", "Verilog", 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 15, 213
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\wr_slope_dpram.v", "Verilog", 88, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 9, 116
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\write_to_ram.v", "Verilog", 464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 39, 544
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\write_to_sdram.v", "Verilog", 1520, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 79, 1655
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\comp\zerobit_out.v", "Verilog", 463, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 13, 495
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\cpu\cpu_top.v", "Verilog", 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 33, 386
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\cpu\time_guard.v", "Verilog", 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 9, 155
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\lvds_8b10b_decoder.v", "Verilog", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 17, 89
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\mid\asyn_dpram_ip.v", "Verilog", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 16, 87
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\mid\comp_rd_sdram.v", "Verilog", 335, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 46, 407
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\mid\mid_top.v", "Verilog", 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 21, 177
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\mid\param_pre.v", "Verilog", 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 16, 93
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\mid\rd_dpram.v", "Verilog", 533, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 62, 628
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\mid\wr_dpram.v", "Verilog", 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 22, 170
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\checksum.v", "Verilog", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 11, 96
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\dat_receive.v", "Verilog", 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 19, 145
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\dpram32b.v", "Verilog", 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 19, 113
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\gen_frame_syn.v", "Verilog", 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 38, 456
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\info_data_separate.v", "Verilog", 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 22, 257
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pkt_info_analysis.v", "Verilog", 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 15, 146
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pkt_pre_top.v", "Verilog", 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 28, 235
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pkt_wr_fifo.v", "Verilog", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 10, 72
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pre_rd_fifo.v", "Verilog", 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 16, 97
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pre_wr_config_fifo.v", "Verilog", 157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 20, 203
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pre_wr_direct_dpram.v", "Verilog", 230, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 26, 286
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pre_wr_img_dpram.v", "Verilog", 248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 22, 304
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pre_wr_info_dpram.v", "Verilog", 251, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 21, 302
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pre_wr_sdram.v", "Verilog", 733, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 79, 48, 860
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\pre_wr_sdram_top.v", "Verilog", 216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 33, 272
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\pre\state_reg.v", "Verilog", 218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 28, 277
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\rst_manage.v", "Verilog", 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 13, 77
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\sdram_control_top\pkg_ce5.vhd", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 12, 39
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\sdram_control_top\rw_sdramcntrl.vhd", "VHDL", 0, 1036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 21, 1133
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\sdram_control_top\sdram_contrl_top.vhd", "VHDL", 0, 1104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 24, 1206
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\sdram_control_top\sdram_read.vhd", "VHDL", 0, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 12, 154
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\sdram_control_top\sdram_write.vhd", "VHDL", 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 13, 159
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\serdes_5x\DECODE.v", "Verilog", 132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 16, 171
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\serdes_5x\SYN_BC.v", "Verilog", 227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 19, 267
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\serdes_5x\bit_extract.v", "Verilog", 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 11, 93
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xdip\src\serdes_5x\serdes_5x.v", "Verilog", 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 9, 64
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl00.vho", "VHDL", 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl01.vho", "VHDL", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl02.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl03.vho", "VHDL", 0, 157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 160
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl04.vho", "VHDL", 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl05.vho", "VHDL", 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl06.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl07.vho", "VHDL", 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl08.vho", "VHDL", 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl09.vho", "VHDL", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl10.vho", "VHDL", 0, 614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 616
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl11.vho", "VHDL", 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl12.vho", "VHDL", 0, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl13.vho", "VHDL", 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl14.vho", "VHDL", 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl15.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl16.vho", "VHDL", 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl17.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl18.vho", "VHDL", 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl19.vho", "VHDL", 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl20.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl21.vho", "VHDL", 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl22.vho", "VHDL", 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl23.vho", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl24.vho", "VHDL", 0, 471, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 473
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl25.vho", "VHDL", 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl26.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl27.vho", "VHDL", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl28.vho", "VHDL", 0, 264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 265
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl29.vho", "VHDL", 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl30.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl31.vho", "VHDL", 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl32.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl33.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl34.vho", "VHDL", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl35.vho", "VHDL", 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl36.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl37.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl38.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl39.vho", "VHDL", 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl40.vho", "VHDL", 0, 326, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 328
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl41.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl42.vho", "VHDL", 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl43.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl44.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl45.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl46.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl47.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl48.vho", "VHDL", 0, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 295
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl49.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl50.vho", "VHDL", 0, 304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 306
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl51.vho", "VHDL", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl52.vho", "VHDL", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl53.vho", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl54.vho", "VHDL", 0, 374, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 375
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl55.vho", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl56.vho", "VHDL", 0, 370, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 371
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl57.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl58.vho", "VHDL", 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl59.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl60.vho", "VHDL", 0, 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 178
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl61.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl62.vho", "VHDL", 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl63.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl64.vho", "VHDL", 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl65.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl66.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl67.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl68.vho", "VHDL", 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl69.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl70.vho", "VHDL", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl71.vho", "VHDL", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl72.vho", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl73.vho", "VHDL", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl74.vho", "VHDL", 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl75.vho", "VHDL", 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl76.vho", "VHDL", 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl77.vho", "VHDL", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl78.vho", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl79.vho", "VHDL", 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl80.vho", "VHDL", 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl81.vho", "VHDL", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl82.vho", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl83.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl84.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl85.vho", "VHDL", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl86.vho", "VHDL", 0, 390, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 392
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl87.vho", "VHDL", 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl88.vho", "VHDL", 0, 388, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 390
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl89.vho", "VHDL", 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl90.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl91.vho", "VHDL", 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl92.vho", "VHDL", 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl93.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl94.vho", "VHDL", 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl95.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl96.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl97.vho", "VHDL", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl98.vho", "VHDL", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub00\vhpl99.vho", "VHDL", 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl100.vho", "VHDL", 0, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl101.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl102.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl103.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl104.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl105.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl106.vho", "VHDL", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl107.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl108.vho", "VHDL", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl109.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl110.vho", "VHDL", 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl111.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl112.vho", "VHDL", 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl113.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl114.vho", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl115.vho", "VHDL", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl116.vho", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl117.vho", "VHDL", 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl118.vho", "VHDL", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl119.vho", "VHDL", 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl120.vho", "VHDL", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl121.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl122.vho", "VHDL", 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl123.vho", "VHDL", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl124.vho", "VHDL", 0, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl125.vho", "VHDL", 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl126.vho", "VHDL", 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl127.vho", "VHDL", 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl128.vho", "VHDL", 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl129.vho", "VHDL", 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl130.vho", "VHDL", 0, 334, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 337
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl131.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl132.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl133.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl134.vho", "VHDL", 0, 466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 468
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl135.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl136.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl137.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl138.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl139.vho", "VHDL", 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl140.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl141.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl142.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl143.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl144.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl145.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl146.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl147.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl148.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl149.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl150.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl151.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl152.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl153.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl154.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl155.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl156.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl157.vho", "VHDL", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl158.vho", "VHDL", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl159.vho", "VHDL", 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl160.vho", "VHDL", 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl161.vho", "VHDL", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl162.vho", "VHDL", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl163.vho", "VHDL", 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl164.vho", "VHDL", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl165.vho", "VHDL", 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl166.vho", "VHDL", 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl167.vho", "VHDL", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl168.vho", "VHDL", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl169.vho", "VHDL", 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl170.vho", "VHDL", 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl171.vho", "VHDL", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl172.vho", "VHDL", 0, 469, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 470
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl173.vho", "VHDL", 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\xst\work\sub01\vhpl174.vho", "VHDL", 0, 477, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 478
"c:\other\ise_14_7\workshop\ce7zlq\zx\CE7-X_DPUv20_updata_dcy _dm\CE7-X_DPU\zerobit_out.v", "Verilog", 463, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 13, 495
"Total", "-", 98061, 83678, 14681, 508, 40, 1076, 3399, 18960, 2644, 3804, 301, 2, 262, 12, 53480, 27653, 308561