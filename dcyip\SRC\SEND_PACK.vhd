----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    21:24:31 10/31/2022 
-- Design Name: 
-- Module Name:    SEND_PACK - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library ieee;
use IEEE.std_logic_1164.all;
USE IEEE.STD_LOGIC_ARITH.ALL;
USE IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity SEND_PACK is
port(
    reset          : in std_logic;
    clk            : in std_logic;
	pack_edge      : in std_logic;
	
	e_sample_end1  : in std_logic;
	e_sample_end2  : in std_logic;
	e_sample_end3  : in std_logic;
	e_sample_end4  : in std_logic;
	e_sample_end5  : in std_logic;
	fgm_sample_end : in std_logic;
	gc_sample_end  : in std_logic;
	
	e_data_in1     : in std_logic_vector(7 downto 0);
	e_data_in2     : in std_logic_vector(7 downto 0);
	e_data_in3     : in std_logic_vector(7 downto 0);
	e_data_in4     : in std_logic_vector(7 downto 0);
    e_data_in5     : in std_logic_vector(7 downto 0);	
    fgm_data_in    : in std_logic_vector(7 downto 0);
    gc_data_in     : in std_logic_vector(7 downto 0);
	
	
	e_addr1        : out std_logic_vector(7 downto 0);
	e_addr2        : out std_logic_vector(7 downto 0);
	e_addr3        : out std_logic_vector(7 downto 0);
	e_addr4        : out std_logic_vector(7 downto 0);
	e_addr5        : out std_logic_vector(7 downto 0);
	fgm_addr       : out std_logic_vector(9 downto 0);
	gc_addr        : out std_logic_vector(7 downto 0);
	
	cmd_count_error: in std_logic_vector(7 downto 0);
    cmd_error_type : in std_logic_vector(7 downto 0);
    cmd_count_lx   : in std_logic_vector(7 downto 0);
    cmd_count_time : in std_logic_vector(7 downto 0);
    cmd_count_sz   : in std_logic_vector(7 downto 0);
	cmd_sz_data    : in std_logic_vector(47 downto 0);
    da1_data   : in std_logic_vector(15 downto 0);
    da2_data   : in std_logic_vector(15 downto 0);
    da3_data   : in std_logic_vector(15 downto 0);
    da4_data   : in std_logic_vector(15 downto 0);	
	
	work_state     : in std_logic_vector(7 downto 0);
	
	s_data        : in std_logic_vector(31 downto 0);
	ms_data       : in std_logic_vector(15 downto 0);	
	
	SC_TXD        : out std_logic
	 
);
end SEND_PACK;

architecture Behavioral of SEND_PACK is

signal	wr        : std_logic;
signal	data_in   : std_logic_vector(7 downto 0);
signal	addr_wr   : std_logic_vector(10 downto 0);
signal  addr_rd	  : std_logic_vector(10 downto 0);
signal  data_out  : std_logic_vector(7 downto 0); 

signal pack_end   : std_logic;

signal  send_over  :std_logic;
signal  send_start :std_logic;
signal  sc_data    : std_logic_vector(7 downto 0);



signal rd        : std_logic;
signal ram_dout   : std_logic_vector(7 downto 0);     
signal pram_addr     : std_logic_vector(10 downto 0);

signal wr_ram:std_logic_vector(0 downto 0);


component PACK_PROCESS 
port(
    reset          : in std_logic;
    clk            : in std_logic;
	
	wr             : out std_logic;
	data_out       : out std_logic_vector(7 downto 0);
	addr           : out std_logic_vector(10 downto 0);
	
	pack_edge      : in std_logic;

	e_sample_end1  : in std_logic;
	e_sample_end2  : in std_logic;
	e_sample_end3  : in std_logic;
	e_sample_end4  : in std_logic;
	e_sample_end5  : in std_logic;
	fgm_sample_end : in std_logic;
	gc_sample_end  : in std_logic;

	e_addr1        : out std_logic_vector(7 downto 0);
	e_addr2        : out std_logic_vector(7 downto 0);
	e_addr3        : out std_logic_vector(7 downto 0);
	e_addr4        : out std_logic_vector(7 downto 0);
	e_addr5        : out std_logic_vector(7 downto 0);
	fgm_addr       : out std_logic_vector(9 downto 0);
	gc_addr        : out std_logic_vector(7 downto 0);
	
	e_data_in1     : in std_logic_vector(7 downto 0);
	e_data_in2     : in std_logic_vector(7 downto 0);
	e_data_in3     : in std_logic_vector(7 downto 0);
	e_data_in4     : in std_logic_vector(7 downto 0);
    e_data_in5     : in std_logic_vector(7 downto 0);	
    fgm_data_in    : in std_logic_vector(7 downto 0);
    gc_data_in     : in std_logic_vector(7 downto 0);	

--时间码
    s_data        : in std_logic_vector(31 downto 0);
	ms_data       : in std_logic_vector(15 downto 0);		
--指令类型及计数等	
    cmd_count_error: in std_logic_vector(7 downto 0);
    cmd_error_type : in std_logic_vector(7 downto 0);
    cmd_count_lx   : in std_logic_vector(7 downto 0);
    cmd_count_time : in std_logic_vector(7 downto 0);
    cmd_count_sz   : in std_logic_vector(7 downto 0);
	cmd_sz_data    : in std_logic_vector(47 downto 0);
	work_state     : in std_logic_vector(7 downto 0);
	
	da1_data   : in std_logic_vector(15 downto 0);
    da2_data   : in std_logic_vector(15 downto 0);
    da3_data   : in std_logic_vector(15 downto 0);
    da4_data   : in std_logic_vector(15 downto 0);	

	pack_end       : out std_logic
);	
end component;

component RAM_A 
	port (
	addra: IN std_logic_VECTOR(10 downto 0);
	addrb: IN std_logic_VECTOR(10 downto 0);
	clka: IN std_logic;
	clkb: IN std_logic;
	dina: IN std_logic_VECTOR(7 downto 0);
	doutb: OUT std_logic_VECTOR(7 downto 0);
	wea: IN std_logic_VECTOR(0 downto 0)
	);
end component;

component SC_SEND_CTRL 
port(
    reset       : in std_logic;
    clk         : in std_logic;
--	test        : in std_logic;
	
	sc_edge     : in std_logic;
	send_start  : out std_logic;
	send_over   : in std_logic;
	data_in     : in std_logic_vector(7 downto 0); 
	addr	    : out std_logic_vector(10 downto 0);
	            
	data_out    : buffer std_logic_vector(7 downto 0)
	
);
end component;

component uart_tx is
port(
   clk     : in  STD_LOGIC;
--   clk_us_edge: in std_logic;
   rst     : in  STD_LOGIC;
   start   : in  STD_LOGIC;
   data_i  : in std_logic_vector(7 downto 0); 
   txd     : out std_logic;
   over    : out std_logic;
   tx_state: in  std_logic;
   tx_on   : out std_logic;
   rx_on   : out std_logic
);
end component;

begin

wr_ram(0)<=wr ;

U_PACK_PROCESS:PACK_PROCESS
port map (
    reset         =>   reset           ,
    clk           =>   clk             ,       
    
    wr            =>   wr              ,
    data_out      =>   data_in        ,
    addr          =>   addr_wr         ,
				
    pack_edge     =>   pack_edge       ,
				
    e_sample_end1 =>   e_sample_end1   ,
    e_sample_end2 =>   e_sample_end2   ,
    e_sample_end3 =>   e_sample_end3   ,
    e_sample_end4 =>   e_sample_end4   ,
    e_sample_end5 =>   e_sample_end5   ,
    fgm_sample_end=>   fgm_sample_end   ,
    gc_sample_end =>   gc_sample_end   ,
				
    e_addr1       =>   e_addr1         ,
    e_addr2       =>   e_addr2         ,
    e_addr3       =>   e_addr3         ,
    e_addr4       =>   e_addr4         ,
    e_addr5       =>   e_addr5         ,
    fgm_addr      =>   fgm_addr        ,
    gc_addr       =>   gc_addr         ,
				  
    e_data_in1    =>   e_data_in1      ,
    e_data_in2    =>   e_data_in2      ,
    e_data_in3    =>   e_data_in3      ,
    e_data_in4    =>   e_data_in4      ,
    e_data_in5    =>   e_data_in5      ,
    fgm_data_in   =>   fgm_data_in     ,
    gc_data_in    =>   gc_data_in      ,
	
	cmd_count_error => cmd_count_error ,
    cmd_error_type  => cmd_error_type  ,
    cmd_count_lx    => cmd_count_lx    ,
	cmd_count_time  => cmd_count_time   ,
    cmd_count_sz    => cmd_count_sz     ,
	cmd_sz_data     => cmd_sz_data      ,
    work_state      => work_state       ,
	
	da1_data     =>  da1_data ,
	da2_data     =>  da2_data ,
	da3_data     =>  da3_data ,
	da4_data     =>  da4_data ,

    s_data        =>   s_data          ,
	ms_data       =>   ms_data         ,
    pack_end      =>   pack_end        
);

	U_PACK_RAM:RAM_A
	port map (
		addra => addr_wr,
		addrb => addr_rd,
		clka  => clk    ,
		clkb  => clk    ,
		dina  => data_in,
		doutb => ram_dout,
		wea   => wr_ram
	);	

    u_SC_SEND_CTRL:SC_SEND_CTRL
    port map(
        reset       =>  reset      ,
        clk         =>  clk        ,
--		test        =>  test_state    ,
    	              
    	sc_edge     =>  pack_end    ,
    	send_start  =>  send_start ,
    	send_over   =>  send_over  ,
    	data_in     =>  ram_dout    ,
    	addr	    =>  addr_rd	   ,
    	                   
    	data_out    =>  sc_data       	
    );

    u_uart_tx:uart_tx 
	PORT MAP (
       clk      => clk,
--	   clk_us_edge =>clk_us_edge,
       rst      => reset,
       start    => send_start,
       data_i   => sc_data,
       txd      => SC_TXD,
       over     => send_over,
	   
	   tx_state => '1'     
--	   tx_on    => JL1_ON   
     );
 
end Behavioral;