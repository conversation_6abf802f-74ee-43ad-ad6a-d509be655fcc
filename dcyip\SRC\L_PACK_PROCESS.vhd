----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    18:09:00 10/24/2022 
-- Design Name: 
-- Module Name:    PACK_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library ieee;
use IEEE.std_logic_1164.all;
USE IEEE.STD_LOGIC_ARITH.ALL;
USE IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity L_PACK_PROCESS is
port(
    reset          : in std_logic;
    clk            : in std_logic;
	
	wr         : out std_logic;
	data_out       : out std_logic_vector(7 downto 0);
	addr      : out std_logic_vector(10 downto 0);
	
	pack_edge      : in std_logic;

	e_sample_end1  : in std_logic;
	e_sample_end2  : in std_logic;
	e_sample_end3  : in std_logic;
	e_sample_end4  : in std_logic;
	e_sample_end5  : in std_logic;
	fgm_sample_end : in std_logic;
	gc_sample_end  : in std_logic;

	e_addr1        : out std_logic_vector(7 downto 0);
	e_addr2        : out std_logic_vector(7 downto 0);
	e_addr3        : out std_logic_vector(7 downto 0);
	e_addr4        : out std_logic_vector(7 downto 0);
	e_addr5        : out std_logic_vector(7 downto 0);
	fgm_addr       : out std_logic_vector(9 downto 0);
	gc_addr        : out std_logic_vector(7 downto 0);
	
	e_data_in1     : in std_logic_vector(7 downto 0);
	e_data_in2     : in std_logic_vector(7 downto 0);
	e_data_in3     : in std_logic_vector(7 downto 0);
	e_data_in4     : in std_logic_vector(7 downto 0);
    e_data_in5     : in std_logic_vector(7 downto 0);	
    fgm_data_in    : in std_logic_vector(7 downto 0);
    gc_data_in     : in std_logic_vector(7 downto 0);	
	
	cmd_count_error: in std_logic_vector(7 downto 0);
    cmd_error_type : in std_logic_vector(7 downto 0);
    cmd_count_lx   : in std_logic_vector(7 downto 0);
    cmd_count_time : in std_logic_vector(7 downto 0);
    cmd_count_sz   : in std_logic_vector(7 downto 0);
	cmd_sz_data    : in std_logic_vector(47 downto 0);
	work_state     : in std_logic_vector(7 downto 0);
    da1_data   : in std_logic_vector(15 downto 0);
    da2_data   : in std_logic_vector(15 downto 0);
    da3_data   : in std_logic_vector(15 downto 0);
    da4_data   : in std_logic_vector(15 downto 0);	

--时间码
    s_data        : in std_logic_vector(31 downto 0);
	ms_data       : in std_logic_vector(15 downto 0);		
--指令类型及计数等	

	pack_end       : out std_logic
);	
end L_PACK_PROCESS;

architecture Behavioral of L_PACK_PROCESS is


signal e_rd1   :std_logic;
signal e_rd2   :std_logic;
signal e_rd3   :std_logic;
signal e_rd4   :std_logic;
signal e_rd5   :std_logic;
signal gc_rd   :std_logic;
signal fgm_rd  :std_logic;
signal time_rd :std_logic;
signal  rd_cmd       : std_logic;
signal blank_rd:std_logic;

signal reg_e_rd1   :std_logic;
signal reg_e_rd2   :std_logic;
signal reg_e_rd3   :std_logic;
signal reg_e_rd4   :std_logic;
signal reg_e_rd5   :std_logic;
signal reg_gc_rd   :std_logic;
signal reg_fgm_rd  :std_logic;
signal reg_time_rd :std_logic;
signal  reg_rd_cmd   : std_logic;
signal reg_blank_rd :std_logic;

signal time_reg : std_logic_vector(47 downto 0);

signal	wr_cnt : std_logic_vector(10 downto 0);
signal  cnt :std_logic_vector(9 downto 0);
type state_t is (start_s,check_s,rev_time,rev_eif1,rev_eif2,rev_eif3,rev_eif4,rev_eif5,rev_fgm,rev_gc,rev_cmd,rev_blank,end_s);
signal state: state_t;

begin

process(reset,clk)
	begin
		if reset='0' then
			time_reg<=(others=>'0');
		else
            if clk ='1' and clk'event then
                if pack_edge='1' then 
				    time_reg(47 downto 16)<=s_data;
                    time_reg(15 downto 0)<=ms_data;
				end if;
			end if;
		end if;
	end process;



process(reset,clk)
	begin
		if reset='0' then
		    state<=start_s;	cnt<= (others=>'0');pack_end<='0';
			e_addr1<=(others=>'0');e_addr2<=(others=>'0');e_addr3<=(others=>'0');e_addr4<=(others=>'0');e_addr5<=(others=>'0');fgm_addr<=(others=>'0');gc_addr<=(others=>'0');
			e_rd1<='0';e_rd2<='0';e_rd3<='0';e_rd4<='0';e_rd5<='0';gc_rd<='0';fgm_rd<='0';time_rd<='0';blank_rd<='0';rd_cmd<='0';
		else
            if clk ='1' and clk'event then
                case state is
                    when start_s =>
					    cnt<= (others=>'0');state<=check_s; pack_end<='0';
					when check_s =>
					    if pack_edge= '1' then
						   cnt <= (others=>'0'); state<=rev_time; 
						end if;
--接收时间码
					when rev_time=>
					    if(cnt<12) then 
						   time_rd<='1'; cnt <= cnt+1;
						else 
						   time_rd<='0'; cnt<=(others=>'0') ; state<=rev_eif1;						   
						end if;
--接收电场单探头电压数据						
					when rev_eif1=>
					    if(cnt<57) then 
						   e_rd1<='1'; e_addr1<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd1<='0'; e_addr1<=(others=>'0');state<=rev_eif2; 
						end if;		
                    when rev_eif2=>
					    if(cnt<57) then 
						   e_rd2<='1'; e_addr2<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd2<='0'; e_addr2<=(others=>'0');state<=rev_eif3; 
						end if;	
                    when rev_eif3=>
					    if(cnt<57) then 
						   e_rd3<='1'; e_addr3<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd3<='0'; e_addr3<=(others=>'0');state<=rev_eif4; 
						end if;	
                    when rev_eif4=>
					    if(cnt<57) then 
						   e_rd4<='1'; e_addr4<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd4<='0'; e_addr4<=(others=>'0');state<=rev_eif5; 
						end if;	
--接收电场差分电压						
                    when rev_eif5=>
					    if(cnt<57) then 
						   e_rd5<='1'; e_addr5<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd5<='0'; e_addr5<=(others=>'0');state<=rev_fgm; 
						end if;							
--接收磁场数据
					when rev_fgm=>
					    if(cnt<684) then 
						   fgm_rd<='1'; fgm_addr<=cnt(9 downto 0);cnt <= cnt+1;
						else 
						   fgm_rd<='0'; cnt<=(others=>'0'); fgm_addr<=(others=>'0');state<=rev_gc;
						end if;
--接收电压温度数据 
                    when rev_gc=>
					    if(cnt<1) then 
						   gc_rd<='1'; gc_addr<=cnt(7 downto 0);cnt <= cnt+1;
						elsif(cnt<9) then
						   gc_rd<='1'; gc_addr<=cnt(7 downto 0)+x"01";cnt <= cnt+1;
						else 
						   gc_rd<='0'; cnt<=(others=>'0'); gc_addr<=(others=>'0');state<=rev_cmd;
						end if;
--指令计数和工作状态
					when rev_cmd=>
					    if (cnt<23) then
						    rd_cmd<='1'; cnt <= cnt+1;
						else 
						    rd_cmd<='0'; cnt<=(others=>'0'); state<=rev_blank;						   
						end if;								   
					when rev_blank=>
					    if (cnt<16) then
						    blank_rd<='1';cnt<=cnt+1;
						else
						    blank_rd<='0';cnt<=(others=>'0');state<=end_s;
						end if;					    
					when end_s =>
					    pack_end <='1';state<=start_s;
                     when others => state<=start_s;
				end case;
			end if;
		end if;
	end process;

	process(reset,clk)
	begin
		if reset='0' then
		    reg_e_rd1   <='0' ;
			reg_e_rd2   <='0' ;
			reg_e_rd3   <='0' ;
			reg_e_rd4   <='0' ;
			reg_e_rd5   <='0' ;
			reg_gc_rd   <='0' ;
			reg_fgm_rd  <='0' ;
			reg_time_rd <='0' ;
            reg_rd_cmd <='0';			
			reg_blank_rd<='0' ;
		else
		    if clk ='1' and clk'event then
	        reg_e_rd1   <= e_rd1   ;
			reg_e_rd2   <= e_rd2   ;
			reg_e_rd3   <= e_rd3   ;
			reg_e_rd4   <= e_rd4   ;
			reg_e_rd5   <= e_rd5   ;
			reg_gc_rd   <= gc_rd   ;
			reg_fgm_rd  <= fgm_rd  ;
			reg_time_rd <=time_rd ;
			reg_rd_cmd <= rd_cmd  ;
			reg_blank_rd <= blank_rd ;	
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
		    wr<='0'; 
			addr<=(others=>'0'); 
			data_out<=(others=>'0'); 
			wr_cnt<=(others=>'0');
		else
		    if clk ='1' and clk'event then
--电场交流			
			    if(reg_e_rd1='1')then
			        wr<='1'; addr<=60+wr_cnt; data_out<= e_data_in1;wr_cnt<=wr_cnt+1; 
				elsif(reg_e_rd2='1') then
				    wr<='1'; addr<=117+wr_cnt; data_out<= e_data_in2;wr_cnt<=wr_cnt+1; 
				elsif(reg_e_rd3='1') then
				    wr<='1'; addr<=174+wr_cnt; data_out<= e_data_in3;wr_cnt<=wr_cnt+1; 
				elsif(reg_e_rd4='1') then
				    wr<='1'; addr<=231+wr_cnt; data_out<= e_data_in4;wr_cnt<=wr_cnt+1; 
				elsif(reg_e_rd5='1') then
				    wr<='1'; addr<=288+wr_cnt; data_out<= e_data_in5;wr_cnt<=wr_cnt+1; 
				elsif(reg_fgm_rd='1') then
				    wr<='1'; addr<=345+wr_cnt; data_out<= fgm_data_in;wr_cnt<=wr_cnt+1; 
				elsif(reg_gc_rd='1') then
				    if(wr_cnt=0)then
				        wr<='1';  addr<=43+wr_cnt; data_out<= gc_data_in; wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt>=1 and wr_cnt<9)then					   
					    wr<='1'; addr<=51+wr_cnt; data_out<= gc_data_in;wr_cnt<=wr_cnt+1; 
					else
					    wr<='0'; addr<=(others=>'0');  wr_cnt<=(others=>'0');
					end if;
				elsif(reg_time_rd='1')then 
				    if(wr_cnt=0)then
				       wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(47 downto 40);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=1)then
				       wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(39 downto 32);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=2) then
					   wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(31 downto 24);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=3) then
					   wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(23 downto 16);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=4) then
					   wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(15 downto 8);wr_cnt<=wr_cnt+1;    
					elsif(wr_cnt=5) then
					   wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(7 downto 0);wr_cnt<=wr_cnt+1; 
				    elsif(wr_cnt=6) then
					   wr<='1';  addr<=17+wr_cnt; data_out<= time_reg(47 downto 40);wr_cnt<=wr_cnt+1;
					elsif(wr_cnt=7)then
				       wr<='1';  addr<=17+wr_cnt; data_out<= time_reg(39 downto 32);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=8) then
					   wr<='1';  addr<=17+wr_cnt; data_out<= time_reg(31 downto 24);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=9) then
					   wr<='1';  addr<=17+wr_cnt; data_out<= time_reg(23 downto 16);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=10) then
					   wr<='1';  addr<=17+wr_cnt; data_out<= time_reg(15 downto 8);wr_cnt<=wr_cnt+1;    
					elsif(wr_cnt=11) then
					   wr<='1';  addr<=17+wr_cnt; data_out<= time_reg(7 downto 0);wr_cnt<=wr_cnt+1;   
					else
					   wr<='0'; wr_cnt<=(others=>'0');addr<=(others=>'0'); 
					end if;
				elsif(reg_rd_cmd='1') then
				    if(wr_cnt=0)then
				       wr<='1';  addr<=29+wr_cnt; data_out<= cmd_count_lx;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=1) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= cmd_count_time;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=2) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= cmd_count_sz;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=3) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= cmd_count_error;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=4) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= cmd_error_type;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=5) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= cmd_count_error;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=6) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= cmd_error_type;wr_cnt<=wr_cnt+1; 			   
					elsif(wr_cnt=7) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= cmd_sz_data(47 downto 40);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=8)then
				       wr<='1';  addr<=29+wr_cnt; data_out<= cmd_sz_data(39 downto 32);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=9) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= cmd_sz_data(31 downto 24);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=10) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= cmd_sz_data(23 downto 16);wr_cnt<=wr_cnt+1;    
					elsif(wr_cnt=11) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= cmd_sz_data(15 downto 8);wr_cnt<=wr_cnt+1; 
				    elsif(wr_cnt=12) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= cmd_sz_data(7 downto 0);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=13) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= work_state;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=14) then
					   wr<='1';  addr<=4+wr_cnt; data_out<= x"DD";wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=15) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da1_data(15 downto 8);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=16) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da1_data(7 downto 0);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=17) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da2_data(15 downto 8);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=18) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da2_data(7 downto 0);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=19) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da3_data(15 downto 8);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=20) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da3_data(7 downto 0);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=21) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da4_data(15 downto 8);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=22) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da4_data(7 downto 0);wr_cnt<=wr_cnt+1; 
					else
					    wr<='0'; wr_cnt<=(others=>'0');addr<=(others=>'0'); 
					end if;				
				elsif (reg_blank_rd='1')then			    
				    wr<='1'; addr<=1029+wr_cnt; data_out<=x"aa";wr_cnt<=wr_cnt+1; 
                else
                    wr<='0'; addr<=(others=>'0');  wr_cnt<=(others=>'0'); 						
	            end if;
			end if;
		end if;
	end process;
	
end Behavioral;	