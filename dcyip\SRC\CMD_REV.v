//*************************************************************************************
// Company:                                                                            
// Engineer:     LSL                                                           
// Create Date:  2016-11-29                                                          
// Design Name:                                                                        
// Module Name:   uart_rx                                                          
// Project Name:                                                                       
// Target Devices:                                                                 
// Description:     rxd data Receiver                                      
//************************************************************************************/
`timescale 1 ns / 1 ns
(*keep_hierarchy="no"*)module CMD_REV
/* #(
	parameter clock_frequency = 32000000,
	parameter baud_rate = 115200,
	parameter baud_cntr_16x_width = 5,
	parameter parity_choice = 1,//1:odd 0:event
	parameter parity_enable = 1 //1:enable 0:unable
) */
(
  input  wire        clk        ,
  input  wire        rst        ,
  input  wire        rxd        ,
  output reg         data_valid ,
  output reg  [07:0] data_o     ,
  output reg         rev_flag   ,
//  output reg         parity_err ,
//  output reg         frame_err  ,
  output reg         trans_error_flag,
  output reg  [07:0] trans_error_type
);

//reg  parity_error_reg;
//reg  frame_error_reg;
reg  parity_err;
reg  trans_error;
reg  trans_error_reg;

//取16个点进行采样
wire baud_tick_16x;//16 times of baud rate
reg  [4:0] baud_cntr_16x;
//localparam integer baud_cntr_16x_max = clock_frequency/(baud_rate*16)-1;17.36
localparam integer baud_cntr_16x_max = 16;
reg  negedge_edge_detected;
reg  rxd_reg1,rxd_reg2;
reg  [03:0] sample_cntr;//0~15
wire baud_tick;

reg  point_a;
reg  point_b;
reg  point_c;
reg  point;
localparam [3:0] point_a_cntr_value = 5;
localparam [3:0] point_b_cntr_value = 6;
localparam [3:0] point_c_cntr_value = 7;
localparam [3:0] point_cntr_value = 8;

reg  bit_valid;
reg  [07:0] shift_buf;
reg  parity;

reg [15:00] uart_time_cnt;

reg  [13:0] state;
localparam s_idle   = 14'b00_0000_0000_0001;
localparam s_start  = 14'b00_0000_0000_0010;
localparam s_bit0   = 14'b00_0000_0000_0100;
localparam s_bit1   = 14'b00_0000_0000_1000;
localparam s_bit2   = 14'b00_0000_0001_0000;
localparam s_bit3   = 14'b00_0000_0010_0000;
localparam s_bit4   = 14'b00_0000_0100_0000;
localparam s_bit5   = 14'b00_0000_1000_0000;
localparam s_bit6   = 14'b00_0001_0000_0000;
localparam s_bit7   = 14'b00_0010_0000_0000;
localparam s_parity = 14'b00_0100_0000_0000;
localparam s_stop1  = 14'b00_1000_0000_0000;
localparam s_stop2  = 14'b01_0000_0000_0000;
localparam s_check  = 14'b10_0000_0000_0000;

localparam parity_base =1'b1;

reg  frame_err1;
reg  frame_err2;
//assign frame_err = frame_err1|| frame_err2;

//baud_tick
assign baud_tick = baud_tick_16x & (sample_cntr==4'hf);

always @(posedge clk or negedge rst)
begin
	if(rst==0)
		sample_cntr <= 0;
	else case(state)
		s_idle  : sample_cntr <= 0;
		default : if(baud_tick_16x) 
						 if(sample_cntr==4'hf) sample_cntr <= 0;
						 else sample_cntr <= sample_cntr + 4'h1;
	endcase
end

//generate 16x baud rate signal，16次抽样取点，就是为了找到下降沿，以及后面判断别数据。
assign baud_tick_16x = (baud_cntr_16x==baud_cntr_16x_max);

always @(posedge clk or negedge rst)
begin
	if(rst==0)
		baud_cntr_16x <= 0;
	else if(baud_tick_16x)
		baud_cntr_16x <= 0;
	else
		baud_cntr_16x <= baud_cntr_16x + 5'h1;
end

//negedge edge detecting
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		rxd_reg1 <= 1'b1;
	else if(baud_tick_16x)
		rxd_reg1 <= rxd;
end

always @(posedge clk or negedge rst)
begin
	if(rst==0)
		rxd_reg2 <= 1'b1;
	else if(baud_tick_16x)
		rxd_reg2 <= rxd_reg1;
end

always @(posedge clk or negedge rst)
begin
	if(rst==0)
		negedge_edge_detected <= 1'b0;
	else if(baud_tick_16x & rxd_reg2 & (~rxd_reg1))
		negedge_edge_detected <= 1'b1;
	else
		negedge_edge_detected <= 1'b0;
end

//state
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		state <= s_idle;
	else case(state)
		s_idle  : if(negedge_edge_detected) state <= s_start;
		s_start : if(baud_tick) state <= s_bit0;
		s_bit0  : if(baud_tick) state <= s_bit1;
		s_bit1  : if(baud_tick) state <= s_bit2;
		s_bit2  : if(baud_tick) state <= s_bit3;
		s_bit3  : if(baud_tick) state <= s_bit4;
		s_bit4  : if(baud_tick) state <= s_bit5;
		s_bit5  : if(baud_tick) state <= s_bit6;
		s_bit6  : if(baud_tick) state <= s_bit7;
		s_bit7  : if(baud_tick) state <= s_parity;
		s_parity: if(baud_tick) state <= s_stop1;
	    s_stop1: if(baud_tick) state <= s_stop2;
		s_stop2  : if(bit_valid) state <= s_check;//bit_valid
		s_check  : state <= s_idle;//bit_valid
		default : state <= s_idle;
	endcase
end

//sample 3 times
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		point_a <= 0;
	else if(baud_tick_16x && sample_cntr==point_a_cntr_value)
		point_a <= rxd_reg1;
end

always @(posedge clk or negedge rst)
begin
	if(rst==0)
		point_b <= 0;
	else if(baud_tick_16x && sample_cntr==point_b_cntr_value)
		point_b <= rxd_reg1;
end

always @(posedge clk or negedge rst)
begin
	if(rst==0)
		point_c <= 0;
	else if(baud_tick_16x && sample_cntr==point_c_cntr_value)
		point_c <= rxd_reg1;
end
//抽样取3个值，没有1或者只有1个1时，说明接收的该bit值为0.
always @(posedge clk or negedge rst)//choose bit that at least appears 2 times
begin
	if(rst==0)
		point <= 0;
	else if(baud_tick_16x && sample_cntr==point_cntr_value)
	begin
		case({point_a,point_b,point_c})
			3'b000,3'b001,3'b010,3'b100: point <= 0;
			default: point <= 1'b1;
		endcase
	end
end

//bit_valid
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		bit_valid <= 0;
	else if(baud_tick_16x && sample_cntr==point_cntr_value)
		bit_valid <= 1;
	else
		bit_valid <= 0;
end

//shift_buf
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		shift_buf <= 8'hff;
	else case(state)
		s_idle : shift_buf <= 8'hff;
		s_bit0,s_bit1,s_bit2,s_bit3,
		s_bit4,s_bit5,s_bit6,s_bit7:
			if(bit_valid) shift_buf <= {point,shift_buf[7:1]};
		default: begin shift_buf<=shift_buf;end
	endcase
end

//parity
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		parity <= parity_base;
	else case(state)
		s_idle : parity <= parity_base;
		s_bit0,s_bit1,s_bit2,s_bit3,
		s_bit4,s_bit5,s_bit6,s_bit7:
			if(bit_valid) parity <= parity + point;
		default: begin parity<=parity;end
	endcase
end

//parity_err
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		parity_err <= parity_base;
	else case(state)
		s_idle  : parity_err <= 1'b0;
		s_parity: if(bit_valid) parity_err <= (parity!=point);
		default : begin parity_err<=parity_err; end
	endcase
end

//frame_err
always @(posedge clk or negedge rst)
begin
	if(rst==0)
	    begin
		frame_err1 <= 1'b0;
		frame_err2 <= 1'b0;
		end 
	else case(state)
		s_idle : begin frame_err1 <= 1'b0; frame_err2 <= 1'b0; end 
		s_stop1: if(bit_valid) frame_err1 <= (point!=1'b1); 
		s_stop2: if(bit_valid) frame_err2 <= (point!=1'b1); 
		default:begin frame_err1 <= frame_err1;frame_err2<=frame_err2;end
	endcase
end

//data_valid
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		data_valid <= 1'b0;
	else case(state)
//		s_stop1 : data_valid <= bit_valid?1'b1:1'b0;
		s_check:  if(parity_err==0 && frame_err1==0 && frame_err2==0) data_valid <= 1'b1;
		default: data_valid <= 1'b0;
	endcase
end

//data_o
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		data_o <= 8'hff;
	else case(state)
		s_stop1 : if(bit_valid) data_o <= shift_buf;
		default: begin data_o<=data_o; end
	endcase
end


//error_flag
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		begin trans_error <= 1'b0;trans_error_type<=8'h00;end
	else case(state)
		s_check: if (parity_err==1) begin trans_error <= 1'b1;trans_error_type <= 8'h11; end
		         else if(frame_err1==1 || frame_err2==1)  begin trans_error <= 1'b1;trans_error_type <= 8'h12; end
		         else begin trans_error <= 1'b0;trans_error_type <= 8'h00; end
	    default: begin trans_error<=trans_error; trans_error_type<=trans_error_type;end
	endcase
end

/* //error_flag
always @(posedge clk or negedge rst)
begin
	if(rst==0)
	    begin
	    	frame_error_reg <= 1'b0;
	    	parity_error_reg <= 1'b0;
	    end 
	else 
	    begin
	        frame_error_reg <= frame_err;
	    	parity_error_reg<=parity_err;	
	    end 
end

always @(posedge clk or negedge rst)
begin
	if(rst==0)
	    begin
		trans_error_flag <= 1'b0;
		trans_error_type <= 8'h00;
		end
	else 
	    begin
		if(frame_error_reg==1'b0 && frame_err==1'b1)
		    begin
		    trans_error_flag <= 1'b1;
			trans_error_type <= 8'h12;
			end 
		else if(parity_error_reg==1'b0 && parity_err==1'b1)
		    begin
		    trans_error_flag <= 1'b1;
			trans_error_type <= 8'h11;
			end
		else 
		    begin
		    trans_error_flag <= 1'b0;
			trans_error_type<=trans_error_type;
			end
		end
end */

always @(posedge clk or negedge rst)
begin
	if(rst==0)
	    begin
	    	trans_error_reg <= 1'b0;
	    end 
	else 
	    begin
	        trans_error_reg <= trans_error;
	    end 
end

always @(posedge clk or negedge rst)
begin
	if(rst==0)
	    begin
		trans_error_flag <= 1'b0;
		end
	else 
	    begin
		if(trans_error_reg==1'b0 && trans_error==1'b1)
		    begin
		    trans_error_flag <= 1'b1;
			end 
		else 
		    begin
		    trans_error_flag <= 1'b0;
			end
		end
end 
//判断是否接收完成
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		uart_time_cnt <= 16'b0;
	else 
	begin
	    if (negedge_edge_detected==1)
		    uart_time_cnt<=16'b1;
		else 
		begin
		    if (uart_time_cnt>=40000)  begin uart_time_cnt<=16'b0; end 
			else if(uart_time_cnt>=1&& uart_time_cnt<40000)  begin uart_time_cnt<=uart_time_cnt +16'b1; end 
			else begin uart_time_cnt<=16'b0;end
		end
	end			 
end

always @(posedge clk or negedge rst)
begin
	if(rst==0)
	    rev_flag <= 1'b0;
	else 
	begin
        if(uart_time_cnt>=1&& uart_time_cnt<40000)
			begin  rev_flag <= 1'b1; end
		else 
			begin rev_flag <= 1'b0;end

	end			 
end





endmodule
