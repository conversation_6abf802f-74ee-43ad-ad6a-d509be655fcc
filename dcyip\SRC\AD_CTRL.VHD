--------------------------------------------------------------------------------
-- Company: <Name>
--
-- File: READ_AD976.vhd
-- File history:
--      <Revision number>: <Date>: <Comments>
--      <Revision number>: <Date>: <Comments>
--      <Revision number>: <Date>: <Comments>
--
-- Description: 
--
-- <Description here>
--
-- Targeted device: <Family::ProASIC3> <Die::A3P600> <Package::208 PQFP>
-- Author: <Name>
--
--------------------------------------------------------------------------------

library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

entity Read_AD976 is
port (
    clk : in std_logic;
    reset : in std_logic;
    
	read_start: in std_logic;
    
    CS1		:	out std_logic; 
    CS2		:	out std_logic;
    CS3		:	out std_logic;
    CS4		:	out std_logic;
    CS5		:	out std_logic; 
	CS6		:	out std_logic; 
	
	RC		:	out std_logic;                    
	BY		:	out std_logic;      

	data_in :  in std_logic_vector(7 downto 0);
	read_over: out std_logic;
--单探头	
	data_out1:  out std_logic_vector(15 downto 0);
	data_out2:  out std_logic_vector(15 downto 0);
	data_out3:  out std_logic_vector(15 downto 0);
	data_out4:  out std_logic_vector(15 downto 0);
--差分探头
	data_out5:  out std_logic_vector(15 downto 0);
--磁场数据及工参
	data_out6:  out std_logic_vector(15 downto 0)
);
end Read_AD976;

architecture Behavioral of Read_AD976 is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

type t_states is (s_start,s_conv,s_wait,s_sel,s_read_h,s_read_l,s_dout,s_check,s_stop);

signal cnt : integer range 0 to 135;
signal data_cnt : integer range 0 to 5;
signal state: t_states;
signal data_out: std_logic_vector(15 downto 0);

begin
	process(reset,clk)
	begin
		if reset='0' then
			RC<='0'; BY<='0';data_cnt<=0;data_out<=x"0000";
			data_out1<=x"0000"; data_out2<=x"0000"; data_out3<=x"0000"; data_out4<=x"0000"; data_out5<=x"0000"; data_out6<=x"0000"; 
			CS1<='0';CS2<='0';CS3<='0';CS4<='0';CS5<='0';CS6<='0'; cnt<=0;
			read_over<='0'; state<=s_start;	
		else
			if clk='1' and clk'event then
				case state is
				    when s_start=>
					    CS1<='1';CS2<='1';CS3<='1';CS4<='1';CS5<='1';CS6<='1';RC<='1';BY<='0';read_over<='0';
					    if read_start='1' then
				            state<=s_conv;  cnt<=0; data_cnt<=0;
						end if;
					when s_conv =>
						RC<='0'; 
						if cnt =4 then
							RC<='1';cnt<=0;state<=s_wait;
						else
						    if cnt=1 then  CS1<='0';CS2<='0';CS3<='0';CS4<='0';CS5<='0';CS6<='0';BY<='0'; end if;
							if cnt=3 then  CS1<='1';CS2<='1';CS3<='1';CS4<='1';CS5<='1';CS6<='1'; end if;
							cnt<= cnt+1;
						end if;
					when s_wait=>							
						 CS1<='1';CS2<='1';CS3<='1';CS4<='1';CS5<='1';CS6<='1';BY<='0';
						if cnt=128 then
						    state<=s_sel; cnt<=0;
						else
						    cnt<=cnt+1;
						end if;
--						state<=s_sel;

					when s_sel=>	
						if data_cnt=0 then CS1<='0'; 
						elsif data_cnt=1 then CS2<='0';
						-- if data_cnt=0 then CS2<='0'; 
						-- elsif data_cnt=1 then CS1<='0';
						elsif data_cnt=2 then CS3<='0';
						elsif data_cnt=3 then CS4<='0';
						elsif data_cnt=4 then CS5<='0';
						elsif data_cnt=5 then CS6<='0';
						else CS1<='1';CS2<='1';CS3<='1';CS4<='1';CS5<='1';CS6<='1'; 
						end if;
						state<=s_read_h;	
					when s_read_h=>	
				        if cnt =2 then							   
							data_out(15 downto 8)<=data_in;
                            cnt<=0;BY<='1';state<=s_read_l;
						else
							cnt<= cnt+1;
						end if;
					when s_read_l=>
						if cnt=2 then
							data_out(7 downto 0)<=data_in;
							cnt<=0;	state<=s_dout;
							CS1<='1';CS2<='1';CS3<='1';CS4<='1';CS5<='1';CS6<='1'; 
						else
							cnt<=cnt+1;
						end if;
					when s_dout=>	
					    if data_cnt=0 then data_out1<=data_out; 
						elsif data_cnt=1 then data_out2<=data_out;
	                    -- if data_cnt=0 then data_out2<=data_out; 
						-- elsif data_cnt=1 then data_out1<=data_out; 						
						elsif data_cnt=2 then data_out3<=data_out; 
						elsif data_cnt=3 then data_out4<=data_out; 
						elsif data_cnt=4 then data_out5<=data_out; 
						elsif data_cnt=5 then data_out6<=data_out; 
						else data_out1<=x"0000"; data_out2<=x"0000"; data_out3<=x"0000"; data_out4<=x"0000"; data_out5<=x"0000"; data_out6<=x"0000";
						end if;
						state<=s_check;
					when s_check=>
					    BY<='0';
					    if data_cnt>=5 then 
							data_cnt<=0;read_over<='1';state<=s_stop;
						else 
--							data_cnt<=data_cnt+1; state<=s_wait;
                            data_cnt<=data_cnt+1; state<=s_sel;
						end if;
					when s_stop=>
						RC<='1'; BY<='0'; read_over<='0';state<=s_start;
					when others =>state<=s_start;
				end case;
			end if;
		end if;
	end process;
							
   -- architecture body
end Behavioral;