----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    10:13:33 13/02/2017 
-- Design Name:    zds
-- Module Name:    sdram_write - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;
use work.pkg_ce5.all;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity sdram_write is
  generic(sdram_data_bus_width : integer);
  port(
    rst              : in std_logic;
    clk              : in std_logic;
    write_en         : in std_logic;
    start_addr_wr    : in std_logic_vector(24 downto 0);
    data_sdram_wr    : in std_logic_vector(sdram_data_bus_width - 1 downto 0);
    write_length_wr  : in std_logic_vector(10 downto 0);
    write_sdram_end  : out std_logic;
    sd_dat           : out std_logic_vector(sdram_data_bus_width - 1 downto 0);
    sd_addr          : out std_logic_vector(24 downto 0);
    sd_command       : out std_logic_vector(2 downto 0);
    sd_write_length  : out std_logic_vector(10 downto 0)
  );
end sdram_write;

architecture Behavioral of sdram_write is

constant start_end_add_time : integer := 12;
--*********************************************************************************************
--SIGNAL DECLARATION
--*********************************************************************************************
  signal start_addr : std_logic_vector(24 downto 0);
  signal write_length : std_logic_vector(10 downto 0);
  signal counter_write_cntrl : std_logic_vector(10 downto 0);
  signal write_cycle_length : std_logic_vector(10 downto 0);
  signal addr_wr : std_logic_vector(9 downto 0);
--*********************************************************************************************
--LOGIC BODY
--*********************************************************************************************
begin

  sd_write_length <= write_length;
  
  process(clk,rst)
  begin
    if rst = '0' then
      write_length <= (others => '0');
      start_addr <= (others => '0');
    elsif rising_edge(clk) then
      if write_en = '1' then
        write_length <= write_length_wr;
        start_addr <= start_addr_wr;
      else
        write_length <= write_length;
        start_addr <= start_addr;
      end if;
    end if;
  end process;
  
  process(clk,rst)
  begin
    if rst = '0' then
      write_cycle_length <= (others => '0');
    elsif rising_edge(clk) then
      write_cycle_length <= write_length + start_end_add_time;----3 + 9 + write_length_wr + 12 - 1 = write_length_wr + 23
    end if;
  end process;
   
  process(clk,rst)
  begin
    if rst = '0' then
      counter_write_cntrl <= (others => '0');
    elsif rising_edge(clk) then
      if write_en = '1' then
        counter_write_cntrl <= counter_write_cntrl + '1';
      elsif counter_write_cntrl >= 1 and counter_write_cntrl < write_cycle_length then
        counter_write_cntrl <= counter_write_cntrl + '1';
      else
        counter_write_cntrl <= (others => '0');
      end if;
   end if;
  end process;
  
  process(clk,rst)
  begin 
    if rst = '0' then
     sd_dat <= (others => '0');
     sd_addr <= (others => '0');
     sd_command <= NOP_COMMAND;
     addr_wr <= (others => '0');
    elsif rising_edge(clk) then
      if counter_write_cntrl < 4 then
        sd_dat <= (others => '0');
        sd_addr<= (others => '0');
        sd_command <= NOP_COMMAND;
        addr_wr <= (others => '0');
      elsif counter_write_cntrl >= 4 and counter_write_cntrl < (write_length + 3) then
        sd_dat <= data_sdram_wr;
        addr_wr <= addr_wr + '1';
        sd_command <= WRITE_COMMAND;
--        if addr_wr(1 downto 0) = "00" then
          sd_addr <= start_addr + ("000" & X"000" & addr_wr);
--        end if;
      elsif counter_write_cntrl = (write_length + 3) then
        sd_dat <= data_sdram_wr;
        sd_command <= WRITE_COMMAND;
        addr_wr <= (others => '0');
		  sd_addr <= start_addr + ("000" & X"000" & addr_wr);
      elsif counter_write_cntrl >= (write_length + 4) and counter_write_cntrl <= (write_length + start_end_add_time) then
        sd_dat <= (others => '0');
        sd_command <= WRITE_COMMAND;
        addr_wr <= (others => '0');
      else
        sd_dat <= (others => '0');
        sd_addr <= (others => '0');
        sd_command <= NOP_COMMAND;
        addr_wr <= (others => '0');
      end if;
    end if;
  end process;
  
  process(clk,rst)
  begin
    if rst = '0' then
      write_sdram_end <= '0';
    elsif rising_edge(clk) then
      if counter_write_cntrl >= (write_length + start_end_add_time) then
        write_sdram_end <= '1';
      else
        write_sdram_end <= '0';
      end if;
    end if;
  end process;
      
end Behavioral;

