`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    21:22:54 10/23/2013 
// Design Name: 
// Module Name:    comma_check 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: comma is K28.5 (001111_1010,  110000_0101)
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module comma_check_5x(data_i,clk,rst,data_out,comma_flag,valid,valid_out,decoder_en);
input  [9:0]  data_i;
input  clk;
input  rst,valid;
output  [9:0]  data_out;
output decoder_en;
output comma_flag,valid_out;

 reg  [9:0]  data_in_o1;
 wire  [3:0]  sel_i;

 wire  [9:0]  data_out_i;
 reg   [9:0]  data_out;
 wire  detect_flag;
 reg   comma_flag;
 reg  valid_out1,valid_out2,valid_out3,valid_out4,valid_out5,valid_out;

 reg [9:0] data_in;
 
 always @(posedge clk or negedge rst)	begin
	   if(!rst) begin
			data_in <= 10'b0000000000;
			data_in_o1 <= 10'b0000000000;
      end
	   else if((valid_out2==1)&&(valid_out3==0)) begin  // else if(valid) begin
			data_in <= data_i;
			data_in_o1 <= data_in;
	   end
		else begin
			data_in <= data_in;
			data_in_o1 <= data_in_o1;
		end
end
 
 
always @(posedge clk or negedge rst)	begin
	    if(!rst) begin
			 valid_out <= 1'b0;
			 valid_out1 <= 1'b0;
			 valid_out2 <= 1'b0;
			 valid_out3 <= 1'b0;
			 valid_out4 <= 1'b0;
			 valid_out5 <= 1'b0;
           end
	    else begin
			 valid_out1 <= valid;
			 valid_out2 <= valid_out1;
			 valid_out3 <= valid_out2;
			 valid_out4 <= valid_out3;
			 valid_out5 <= valid_out4;
			 valid_out  <= valid_out5;
	    end
end



always @(posedge clk or negedge rst)	begin
	    if(!rst) begin
			data_out <= 10'b0000000000;
			comma_flag <= 1'b0;
       end
	    else begin
			data_out <= data_out_i;
			comma_flag <= detect_flag;
	    end
end

detect_5x detecter_5x (.data_in_a(data_in_o1),
                 .data_in_b(data_in),
					  .clk(clk),
					  .rst(rst),
					  .sel(sel_i),
					  .detect(detect_flag),
					  .decoder_en(decoder_en));

shift_5x  shifter_5x  (.data_in_a(data_in_o1),
					  .data_in_b(data_in),
					  .sel(sel_i),
					  .data_out(data_out_i));

endmodule



module detect_5x (data_in_a,data_in_b,clk,rst,sel,detect,decoder_en);
	input [9:0] data_in_a;
	input [9:0] data_in_b;
	input clk;
	input rst;
	output [3:0] sel;
	output detect;
	output decoder_en;
	reg decoder_en;

	 reg   [3:0] sel;
	 reg   detect;


	always @(posedge clk or negedge rst) begin
	if(!rst) begin
		sel <= 4'h1;
		detect <= 1'b0;
		decoder_en<= 1'b0;
	end
	else begin
		casex ({data_in_a[9:0],data_in_b[9:0]})//casex ({data_in_a[9:0],data_in_b[9:0]})
		20'b1100000101xxxxxxxxxx,		
        20'b0011111010xxxxxxxxxx:begin
		                            sel    <= 4'h1;
				                    detect <= 1'b1;
										  decoder_en<= 1'b1;
								end
		20'bx1100000101xxxxxxxxx,		
		20'bx0011111010xxxxxxxxx :begin
		                                   sel    <= 4'h2;
				                             detect <= 1'b1;
													  decoder_en<= 1'b1;
		
											  end
								  
		20'bxx1100000101xxxxxxxx,
		20'bxx0011111010xxxxxxxx	:begin
		                                   sel    <= 4'h3;
				                             detect <= 1'b1;
													  decoder_en<= 1'b1;
													  end											 

		20'bxxx1100000101xxxxxxx,
      	20'bxxx0011111010xxxxxxx:begin
		                                   sel    <= 4'h4;
				                             detect <= 1'b1;
													  decoder_en<= 1'b1;
													  end	
		20'bxxxx1100000101xxxxxx,
		20'bxxxx0011111010xxxxxx :begin
		                                   sel    <= 4'h5;
				                             detect <= 1'b1;
													  decoder_en<= 1'b1;
													  end
      20'bxxxxx1100000101xxxxx,
	  20'bxxxxx0011111010xxxxx :begin
		                                   sel    <= 4'h6;
				                             detect <= 1'b1;
													  decoder_en<= 1'b1;
	
												  end	
      20'bxxxxxx1100_000101xxxx,
	  20'bxxxxxx0011_111010xxxx :begin
		                                   sel    <= 4'h7;
				                             detect <= 1'b1;
													  decoder_en<= 1'b1;
													  end
      20'bxxxxxxx1100000101xxx,
	  20'bxxxxxxx0011111010xxx :begin
		                                   sel    <= 4'h8;
				                             detect <= 1'b1;
													  decoder_en<= 1'b1;
													  end
      20'bxxxxxxxx1100000101xx,
	  20'bxxxxxxxx0011111010xx :begin
		                                   sel    <= 4'h9;
				                             detect <= 1'b1;
													  decoder_en<= 1'b1;
													  end
      20'bxxxxxxxxx0011111010x,
	  20'bxxxxxxxxx1100000101x :begin
		                                   sel    <= 4'ha;
				                             detect <= 1'b1;
													  decoder_en<= 1'b1;
													  end
                                default :begin
										     sel    <= sel;
				                             detect <= 1'b0;
													  end
	   endcase
		end
		end		

endmodule



module shift_5x (data_in_a,data_in_b,sel,data_out);
	input [9:0] data_in_a;
	input [9:0] data_in_b;
	input [3:0] sel;
	output [9:0] data_out;

	 reg   [9:0]  data_out;

		
		always @(data_in_a,data_in_b,sel) begin
			case(sel)  
				4'h1 : data_out <= data_in_a;
				4'h2 : data_out <= {data_in_a[8:0],data_in_b[9]};
				4'h3 : data_out <= {data_in_a[7:0],data_in_b[9:8]};
				4'h4 : data_out <= {data_in_a[6:0],data_in_b[9:7]};
				4'h5 : data_out <= {data_in_a[5:0],data_in_b[9:6]};
				4'h6 : data_out <= {data_in_a[4:0],data_in_b[9:5]};
				4'h7 : data_out <= {data_in_a[3:0],data_in_b[9:4]};
				4'h8 : data_out <= {data_in_a[2:0],data_in_b[9:3]};
				4'h9 : data_out <= {data_in_a[1:0],data_in_b[9:2]};
				4'ha : data_out <= {data_in_a[0],data_in_b[9:1]};	
		default:
				data_out <= 10'b0;	
		   endcase
		end

endmodule
