----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    13:53:37 11/13/2022 
-- Design Name: 
-- Module Name:    DA_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity DA_PROCESS is
Port ( 	
			clk			: in std_logic;
			reset 		: in std_logic;
			da_clk     : in std_logic;
			da_data_in     : in std_logic_vector(47 downto 0);
			da_change   : in std_logic;
			s_edge      : in std_logic;
			mode        : in std_logic;
			init_da     : in std_logic;
			
			da1_data   : out std_logic_vector(15 downto 0);
            da2_data   : out std_logic_vector(15 downto 0);
            da3_data   : out std_logic_vector(15 downto 0);
            da4_data   : out std_logic_vector(15 downto 0);	
			
			SC_CS1		: out std_logic; 
            SC_CS2		: out std_logic; 
            SC_CS3		: out std_logic; 
            SC_CS4		: out std_logic; 			
			SC_DATA		: out std_logic;                  
			SC_CLK 		: out std_logic       
	);
end DA_PROCESS;

architecture Behavioral of DA_PROCESS is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";
signal scan_send_mark1  :std_logic;
signal scan_send_mark2  :std_logic;
signal scan_send_mark3  :std_logic;
signal scan_send_mark4  :std_logic;
signal scan_da_out   :std_logic_vector(15 downto 0);
signal normal_send_mark1  :std_logic;
signal normal_send_mark2  :std_logic;
signal normal_send_mark3  :std_logic;
signal normal_send_mark4  :std_logic;
signal normal_da_out   :std_logic_vector(15 downto 0);

signal normal_da_out1      :std_logic_vector(15 downto 0);
signal normal_da_out2      :std_logic_vector(15 downto 0);
signal normal_da_out3      :std_logic_vector(15 downto 0);
signal normal_da_out4      :std_logic_vector(15 downto 0);
signal scan_da_out1      :std_logic_vector(15 downto 0);
signal scan_da_out2      :std_logic_vector(15 downto 0);
signal scan_da_out3      :std_logic_vector(15 downto 0);
signal scan_da_out4      :std_logic_vector(15 downto 0);

signal da_scan_step1  : std_logic_vector(11 downto 0);
signal da_scan_time1  : std_logic_vector(3 downto 0);
signal da_scan_hdata1 : std_logic_vector(11 downto 0);
signal da_scan_ldata1 : std_logic_vector(11 downto 0);				
signal da_scan_step2  : std_logic_vector(11 downto 0);
signal da_scan_time2  : std_logic_vector(3 downto 0);
signal da_scan_hdata2 : std_logic_vector(11 downto 0);
signal da_scan_ldata2 : std_logic_vector(11 downto 0);					
signal da_scan_step3  : std_logic_vector(11 downto 0);
signal da_scan_time3  : std_logic_vector(3 downto 0);
signal da_scan_hdata3 : std_logic_vector(11 downto 0);
signal da_scan_ldata3 : std_logic_vector(11 downto 0);		
signal da_scan_step4  : std_logic_vector(11 downto 0);
signal da_scan_time4  : std_logic_vector(3 downto 0);
signal da_scan_hdata4 : std_logic_vector(11 downto 0);
signal da_scan_ldata4 : std_logic_vector(11 downto 0);	
signal da_num  : std_logic_vector(7 downto 0);
signal da_data : std_logic_vector(15 downto 0);
signal da_set  : std_logic;


signal send_over   :std_logic;
signal send_mark1  :std_logic;
signal send_mark2  :std_logic;
signal send_mark3  :std_logic;
signal send_mark4  :std_logic;
signal da_out      :std_logic_vector(15 downto 0);
signal busy_flag   :std_logic;

component DA_CHANGE_MODE is
Port ( 	
			clk			: in std_logic;
			reset 		: in std_logic;
			da_data_in  : in std_logic_vector(47 downto 0);
			da_change   : in std_logic;
			s_edge      : in std_logic;
			mode        : in std_logic;
			init_da     : in std_logic;
			
			da_scan_step1  : out std_logic_vector(11 downto 0);
			da_scan_time1  : out std_logic_vector(3 downto 0);
			da_scan_hdata1 : out std_logic_vector(11 downto 0);
			da_scan_ldata1 : out std_logic_vector(11 downto 0);	
							
			da_scan_step2  : out std_logic_vector(11 downto 0);
			da_scan_time2  : out std_logic_vector(3 downto 0);
			da_scan_hdata2 : out std_logic_vector(11 downto 0);
			da_scan_ldata2 : out std_logic_vector(11 downto 0);	
							
			da_scan_step3  : out std_logic_vector(11 downto 0);
			da_scan_time3  : out std_logic_vector(3 downto 0);
			da_scan_hdata3 : out std_logic_vector(11 downto 0);
			da_scan_ldata3 : out std_logic_vector(11 downto 0);
							
			da_scan_step4  : out std_logic_vector(11 downto 0);
			da_scan_time4  : out std_logic_vector(3 downto 0);
			da_scan_hdata4 : out std_logic_vector(11 downto 0);
			da_scan_ldata4 : out std_logic_vector(11 downto 0);	

            da_num  :out std_logic_vector(7 downto 0);
			da_data :out std_logic_vector(15 downto 0);
			da_set   :out std_logic
			
	);
end component;

component DA_SCAN_PROCESS is
Port ( 	
			clk			: in std_logic;
			reset 		: in std_logic;			
			start_scan  : in std_logic;
			s_edge      : in std_logic;
			send_over   : in std_logic;
						
			da_scan_step1  : in std_logic_vector(11 downto 0);
			da_scan_time1  : in std_logic_vector(3 downto 0);
			da_scan_hdata1 : in std_logic_vector(11 downto 0);
			da_scan_ldata1 : in std_logic_vector(11 downto 0);	
			
			da_scan_step2  : in std_logic_vector(11 downto 0);
			da_scan_time2  : in std_logic_vector(3 downto 0);
			da_scan_hdata2 : in std_logic_vector(11 downto 0);
			da_scan_ldata2 : in std_logic_vector(11 downto 0);	
			
			da_scan_step3  : in std_logic_vector(11 downto 0);
			da_scan_time3  : in std_logic_vector(3 downto 0);
			da_scan_hdata3 : in std_logic_vector(11 downto 0);
			da_scan_ldata3 : in std_logic_vector(11 downto 0);

			da_scan_step4  : in std_logic_vector(11 downto 0);
			da_scan_time4  : in std_logic_vector(3 downto 0);
			da_scan_hdata4 : in std_logic_vector(11 downto 0);
			da_scan_ldata4 : in std_logic_vector(11 downto 0);

           	da1_data   : out std_logic_vector(15 downto 0);
            da2_data   : out std_logic_vector(15 downto 0);
            da3_data   : out std_logic_vector(15 downto 0);
            da4_data   : out std_logic_vector(15 downto 0);				
			
	        send_mark1  : out std_logic;
	        send_mark2  : out std_logic;
	        send_mark3  : out std_logic;
	        send_mark4  : out std_logic;	
            da_out      : out std_logic_vector(15 downto 0)			
	);
end component;

component DA_NORMAL is
Port ( 	
        clk			: in std_logic;
		reset 		: in std_logic;
		mode        : in std_logic;
		send_over   : in std_logic;
		busy_flag     :  in std_logic;
		
		da_num  :in std_logic_vector(7 downto 0);
		da_data :in std_logic_vector(15 downto 0);
		da_set  :in std_logic;
		
		send_mark1  : out std_logic;
	    send_mark2  : out std_logic;
	    send_mark3  : out std_logic;
	    send_mark4  : out std_logic;	
        da_out      : out std_logic_vector(15 downto 0);	
		
		da_out1 :out std_logic_vector(15 downto 0);
		da_out2 :out std_logic_vector(15 downto 0);
		da_out3 :out std_logic_vector(15 downto 0);
		da_out4 :out std_logic_vector(15 downto 0)
	);
end component;

component DA_SCH is
Port ( 	
		clk			: in std_logic;
		reset 		: in std_logic;
		mode        : in std_logic;
		
		scan_send_mark1  : in std_logic;
	    scan_send_mark2  : in std_logic;
	    scan_send_mark3  : in std_logic;
	    scan_send_mark4  : in std_logic;	
        scan_da_out      : in std_logic_vector(15 downto 0);
        scan_da_out1      :in std_logic_vector(15 downto 0);
        scan_da_out2      :in std_logic_vector(15 downto 0);
        scan_da_out3      :in std_logic_vector(15 downto 0);
        scan_da_out4      :in std_logic_vector(15 downto 0);
		
		normal_send_mark1  : in std_logic;
	    normal_send_mark2  : in std_logic;
	    normal_send_mark3  : in std_logic;
	    normal_send_mark4  : in std_logic;	
        normal_da_out      : in std_logic_vector(15 downto 0);
		normal_da_out1      :in std_logic_vector(15 downto 0);
        normal_da_out2      :in std_logic_vector(15 downto 0);
        normal_da_out3      :in std_logic_vector(15 downto 0);
        normal_da_out4      :in std_logic_vector(15 downto 0);
		
        		
		send_mark1  : out std_logic;
	    send_mark2  : out std_logic;
	    send_mark3  : out std_logic;
	    send_mark4  : out std_logic;	
        da_out      : out std_logic_vector(15 downto 0);
		da_out1     : out std_logic_vector(15 downto 0);
        da_out2     : out std_logic_vector(15 downto 0);
        da_out3     : out std_logic_vector(15 downto 0);
        da_out4     : out std_logic_vector(15 downto 0)
);		
	
end component;


component DA_DRV is
Port ( 	
			clk			: in std_logic;
			reset 		: in std_logic;
			clk_128     : in std_logic;
			da_data_in  : in std_logic_vector(15 downto 0);
			send_mark1   : in std_logic;
			send_mark2   : in std_logic;
			send_mark3   : in std_logic;
			send_mark4   : in std_logic;
			send_over	: out std_logic; 
			busy_flag   :  out std_logic;
			
			SC_CS1		: out std_logic; 
            SC_CS2		: out std_logic; 
            SC_CS3		: out std_logic; 
            SC_CS4		: out std_logic; 			
			SC_DATA		: out std_logic;                  
			SC_CLK 		: out std_logic     
	);
end component;


begin
    u_DA_CHANGE_MODE:DA_CHANGE_MODE
   PORT MAP (
    clk         => clk        ,
    reset       => reset      ,
	
    da_data_in  =>da_data_in     , 
	da_change   =>da_change   ,
	s_edge   	=>s_edge   ,
	mode        =>mode   ,
	init_da     =>init_da,
	
	da_scan_step1  => da_scan_step1  ,
	da_scan_time1  => da_scan_time1  ,
	da_scan_hdata1 => da_scan_hdata1 ,
	da_scan_ldata1 => da_scan_ldata1 ,
									
	da_scan_step2  => da_scan_step2  ,
	da_scan_time2  => da_scan_time2  ,
	da_scan_hdata2 => da_scan_hdata2 ,
	da_scan_ldata2 => da_scan_ldata2 ,
									
	da_scan_step3  => da_scan_step3  ,
	da_scan_time3  => da_scan_time3  ,
	da_scan_hdata3 => da_scan_hdata3 ,
	da_scan_ldata3 => da_scan_ldata3 ,
									
	da_scan_step4  => da_scan_step4  ,
	da_scan_time4  => da_scan_time4  ,
	da_scan_hdata4 => da_scan_hdata4 ,
	da_scan_ldata4 => da_scan_ldata4 ,
	
	da_num   => da_num  ,
	da_data  => da_data ,
	da_set   => da_set
	   
    );  
	
	u_DA_SCAN_PROCESS:DA_SCAN_PROCESS
	PORT MAP (
	clk         => clk        ,
    reset       => reset      ,
	start_scan  => mode       ,
	s_edge      => s_edge     ,
	send_over   => send_over  ,
	
	da_scan_step1  => da_scan_step1  ,
	da_scan_time1  => da_scan_time1  ,
	da_scan_hdata1 => da_scan_hdata1 ,
	da_scan_ldata1 => da_scan_ldata1 ,
									
	da_scan_step2  => da_scan_step2  ,
	da_scan_time2  => da_scan_time2  ,
	da_scan_hdata2 => da_scan_hdata2 ,
	da_scan_ldata2 => da_scan_ldata2 ,
									
	da_scan_step3  => da_scan_step3  ,
	da_scan_time3  => da_scan_time3  ,
	da_scan_hdata3 => da_scan_hdata3 ,
	da_scan_ldata3 => da_scan_ldata3 ,
									
	da_scan_step4  => da_scan_step4  ,
	da_scan_time4  => da_scan_time4  ,
	da_scan_hdata4 => da_scan_hdata4 ,
	da_scan_ldata4 => da_scan_ldata4 ,
	
	da1_data  => scan_da_out1,
    da2_data  => scan_da_out2,
    da3_data  => scan_da_out3,
    da4_data  => scan_da_out4,
		
	send_mark1  => scan_send_mark1  ,
	send_mark2  => scan_send_mark2  ,
	send_mark3  => scan_send_mark3  ,
	send_mark4  => scan_send_mark4  ,
	da_out      => scan_da_out       
);

	u_DA_NORMAL:DA_NORMAL
	PORT MAP (
	clk         => clk        ,
    reset       => reset      ,
	send_over   => send_over  ,
	busy_flag     =>busy_flag,
	mode        => mode   ,
	
	da_num   => da_num  ,
	da_data  => da_data ,
	da_set   => da_set  ,	
		
	send_mark1  => normal_send_mark1  ,
	send_mark2  => normal_send_mark2  ,
	send_mark3  => normal_send_mark3  ,
	send_mark4  => normal_send_mark4  ,
	da_out      => normal_da_out      ,
   
    da_out1    =>  normal_da_out1    ,
    da_out2    =>  normal_da_out2    ,
    da_out3    =>  normal_da_out3    ,
    da_out4    =>  normal_da_out4    	
);

	u_DA_SCH:DA_SCH
	PORT MAP (
	clk         => clk        ,
    reset       => reset      ,
	mode        => mode       ,
	
	scan_send_mark1=> scan_send_mark1,
    scan_send_mark2=> scan_send_mark2,
    scan_send_mark3=> scan_send_mark3,
    scan_send_mark4=> scan_send_mark4,
	scan_da_out    => scan_da_out    ,
	scan_da_out1    =>  scan_da_out1    ,
    scan_da_out2    =>  scan_da_out2    ,
    scan_da_out3    =>  scan_da_out3    ,
    scan_da_out4    =>  scan_da_out4    ,
		
	normal_send_mark1=>normal_send_mark1,
    normal_send_mark2=>normal_send_mark2,
    normal_send_mark3=>normal_send_mark3,
    normal_send_mark4=>normal_send_mark4,
	normal_da_out    =>normal_da_out    ,
    normal_da_out1   =>  normal_da_out1    ,
    normal_da_out2   =>  normal_da_out2    ,
    normal_da_out3   =>  normal_da_out3    ,
    normal_da_out4   =>  normal_da_out4    ,	
   
 	send_mark1  => send_mark1  ,
	send_mark2  => send_mark2  ,
	send_mark3  => send_mark3  ,
	send_mark4  => send_mark4  ,
	da_out      => da_out      ,
	da_out1    =>  da1_data    ,
    da_out2    =>  da2_data    ,
    da_out3    =>  da3_data    ,
    da_out4    =>  da4_data    
);	
	
	
	
	u_DA_DRV:DA_DRV
   PORT MAP (
    clk         => clk        ,
    reset       => reset      ,
	clk_128     => da_clk     ,
	da_data_in  => da_out     ,
	
	send_mark1  => send_mark1  ,
	send_mark2  => send_mark2  ,
	send_mark3  => send_mark3  ,
	send_mark4  => send_mark4  ,
	
	send_over 	=>send_over   ,
	busy_flag     =>busy_flag,
	
	SC_CS1	  => SC_CS1	   ,
	SC_CS2	  => SC_CS2	   ,
	SC_CS3	  => SC_CS3	   ,
	SC_CS4	  => SC_CS4	   ,
	SC_DATA   => SC_DATA   ,
	SC_CLK    => SC_CLK    
    );  
end Behavioral;

