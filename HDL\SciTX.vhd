----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    09:56:55 11/28/2016 
-- Design Name: 
-- Module Name:    SciTX - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity SciTX is
port(clk:in std_logic;
	  reset:in std_logic;
	  wen:in std_logic;
	  din:in std_logic_vector(7 downto 0);
	  txrdy:out std_logic;
	  sci_clk:out std_logic;
	  sci_data:out std_logic;
	  sci_strob:out std_logic);
end SciTX;

architecture Behavioral of SciTX is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";
signal ren,txrdy_reg,flag:std_logic;
signal reg0,reg1,reg2:std_logic_vector(7 downto 0);
signal count:std_logic_vector(2 downto 0);
begin

	txrdy<=txrdy_reg;
	
	sci_clk<=clk;
	
	process(clk,reset)
	begin
		if reset='0' then
			txrdy_reg<='1';
		elsif rising_edge(clk) then
			if wen='0' then
				txrdy_reg<='0';
			elsif ren='0' then
				txrdy_reg<='1';
			end if;
		end if;
	end process;
	
	process(clk,reset)
	begin
		if reset='0' then
			reg0<=x"00";
			reg1<=x"00";
		elsif rising_edge(clk) then
			if wen='0' then
				reg0<=din;
			end if;
			if ren='0' then
				reg1<=reg0;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
			ren<='1';
			flag<='0';
		elsif rising_edge(clk) then
			if count="101" and txrdy_reg='0' then
				ren<='0';
			else
				ren<='1';
			end if;
			if count="101"  then
				if txrdy_reg='0' then
					flag<='1';
				else
					flag<='0';
				end if;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
			count<="000";
		elsif rising_edge(clk) then
			count<=count+1;
		end if;
	end process;
	
	process(clk,reset)
	begin
		if reset='0' then
			reg2<=(others=>'0');
		elsif falling_edge(clk) then
			if count="111" then
				reg2<=reg1;
			else
				reg2(7 downto 1)<=reg2(6 downto 0);
			end if;
		end if;
	end process;
	
	process(clk,reset)
	begin
		if reset='0' then
			sci_data<='0';
			sci_strob<='1';
		elsif falling_edge(clk) then
			if count="000" and flag='1' then
				sci_strob<='0';
			elsif count="000" and flag='0' then
				sci_strob<='1';
			end if;
			sci_data<=reg2(7);
		end if;
	end process;
		
end Behavioral;

