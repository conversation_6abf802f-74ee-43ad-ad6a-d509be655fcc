----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    13:02:20 10/09/2022 
-- Design Name: 
-- Module Name:    Receive_uart - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity Receive_uart is
	port(clk:in std_logic;
		  reset:in std_logic;
		  RX_line:in std_logic;
		  baud_val:in std_logic_vector(7 downto 0);
		  data_out_en:out std_logic;
		  data_out:out std_logic_vector(7 downto 0));
end Receive_uart;

architecture Behavioral of Receive_uart is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";
component coreuart
PORT (reset_n                 : IN std_logic;
      clk                     : IN std_logic;
      WEn                     : IN std_logic;
      OEn                     : IN std_logic;
      csn                     : IN std_logic;
      data_in                 : IN std_logic_vector(7 DOWNTO 0);
      rx                      : IN std_logic;
		stop_bit						: IN std_logic;
      baud_val                : IN std_logic_vector(7 DOWNTO 0);
      bit8                    : IN std_logic;   --   if set to one 8 data bits otherwise 7 data bits
      parity_en               : IN std_logic;   --   if set to one parity is enabled otherwise disabled
      odd_n_even              : IN std_logic;   --   if set to one odd parity otherwise even parity
      parity_err              : OUT std_logic;   --   parity error indicator on recieved data
      overflow                : OUT std_logic;   --   receiver overflow
      txrdy                   : OUT std_logic;   --   transmit ready for another byte
      rxrdy            			: OUT std_logic;   --   receiver has a byte ready
      data_out                : OUT std_logic_vector(7 DOWNTO 0);
      tx                      : OUT std_logic);
end component;
signal oen,oen_reg,rxrdy:std_logic;
begin

	data_out_en<=oen_reg and (not oen);
	
	process(clk,reset)
	begin
		if reset='0' then
			oen<='1';
			oen_reg<='1';
		elsif rising_edge(clk) then
			if rxrdy='1' then
				oen<='0';
			else
				oen<='1';
			end if;
			
			oen_reg<=oen;
		end if;
	end process;


	UART_RX:coreuart port map  --115200bps ---27.126-1=26
		(reset_n=>reset,clk=>clk,WEn=>'1',OEn=>oen,csn=>'0',data_in=>x"ff",rx=>rx_line,stop_bit=>'1',
		baud_val=>baud_val,bit8=>'1',parity_en=>'1',odd_n_even=>'1',parity_err=>open,overflow=>open, 
		txrdy=>open,rxrdy=>rxrdy, data_out=>data_out,tx=>open);


end Behavioral;

