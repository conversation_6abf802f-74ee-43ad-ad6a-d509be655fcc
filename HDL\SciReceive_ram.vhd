----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    14:11:14 10/09/2022 
-- Design Name: 
-- Module Name:    SciReceive_uart - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;
-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity SciReceive_ram is
port(clk:in std_logic;
	 reset:in std_logic;
	 soft_reset:in std_logic;

	 addr_rd	 : out std_logic_vector(10 downto 0);
	 ram_dout   : in  std_logic_vector(7 downto 0);
	 pack_end   : in  std_logic;
     RX_en : in std_logic;
	  
	  fifo_rd_en:in std_logic;
	  fifo_rd_data_count:out std_logic_vector(12 downto 0);
	  fifo_data:out std_logic_vector(7 downto 0);
	  fifo_empty:out std_logic;
	  fifo_prog_full:out std_logic;
	  
	  frame_num_o:out std_logic_vector(7 downto 0);
	  checksum_err_o:out std_logic_vector(7 downto 0));
end SciReceive_ram;

architecture Behavioral of SciReceive_ram is
component dcy_sci
	port(clk:in std_logic;
		  reset:in std_logic;

		  RX_en:in std_logic;
          addr_rd	 : out std_logic_vector(10 downto 0);
	      ram_dout   : in std_logic_vector(7 downto 0);
	      pack_end   : in std_logic;
		  
		  data_out_en:out std_logic;
		  data_out:out std_logic_vector(7 downto 0));
end component;

component format_check
	port(clk:in std_logic;
		  reset:in std_logic;
		  data_in_en:in std_logic;
		  data_in:in std_logic_vector(7 downto 0);
		  
		  data_out_en:out std_logic;
		  data_out:out std_logic_vector(7 downto 0);
		  frame_num_o:out std_logic_vector(7 downto 0);
		  checksum_err_o:out std_logic_vector(7 downto 0));
end component;

component FIFO8192x8
  PORT (
    rst : IN STD_LOGIC;
    wr_clk : IN STD_LOGIC;
    rd_clk : IN STD_LOGIC;
    din : IN STD_LOGIC_VECTOR(7 DOWNTO 0);
    wr_en : IN STD_LOGIC;
    rd_en : IN STD_LOGIC;
    dout : OUT STD_LOGIC_VECTOR(7 DOWNTO 0);
    full : OUT STD_LOGIC;
    empty : OUT STD_LOGIC;
    rd_data_count : OUT STD_LOGIC_VECTOR(12 DOWNTO 0);
    wr_data_count : OUT STD_LOGIC_VECTOR(12 DOWNTO 0);
    prog_full : OUT STD_LOGIC
  );
 end component;
 
signal data_out_en,fifo_wr_en,fifo_wr_en_reg,rst,reset1,soft_reset_reg0,soft_reset_reg1:std_logic;
signal data_out,fifo_din,fifo_din_reg:std_logic_vector(7 downto 0);
signal frame_num_reg0,frame_num_reg1,checksum_err_reg0,checksum_err_reg1:std_logic_vector(7 downto 0);
signal fifo_prog_full_reg0,fifo_prog_full_reg1:std_logic;
begin
	
	rst <= (not reset) or soft_reset;
	
	process(clk,reset)
	begin
		if reset='0' then
			frame_num_reg1<=x"00";
			frame_num_o<=x"00";
			checksum_err_reg1<=x"00";
			checksum_err_o<=x"00";
			fifo_prog_full_reg1<='0';
			fifo_prog_full<='0';
		elsif rising_edge(clk) then
			frame_num_reg1<=frame_num_reg0;
			frame_num_o<=frame_num_reg1;
			checksum_err_reg1<=checksum_err_reg0;
			checksum_err_o<=checksum_err_reg1;
			fifo_prog_full_reg1<=fifo_prog_full_reg0;
			fifo_prog_full<=fifo_prog_full_reg1;
		end if;
	end process;
	
	process(clk,reset)
	begin
		if reset='0' then
			soft_reset_reg0<='0';
			soft_reset_reg1<='0';
		elsif rising_edge(clk) then
			soft_reset_reg0<=soft_reset;
			soft_reset_reg1<=soft_reset_reg0;
		end if;
	end process;
	
	reset1<=reset and (not soft_reset_reg1);
	
	receive_ram_0:dcy_sci port map
	(clk=>clk,reset=>reset,RX_en=>RX_en,addr_rd=>addr_rd,ram_dout =>ram_dout,pack_end =>pack_end ,
	data_out_en=>data_out_en,data_out=>data_out);
	  
	 format_check_0:format_check port map
	 (clk=>clk,reset=>reset1,data_in_en=>data_out_en,data_in=>data_out,data_out_en=>fifo_wr_en, 
	 data_out=>fifo_din,frame_num_o=>frame_num_reg0,checksum_err_o=>checksum_err_reg0);
	 
	process(clk,reset1)
	begin
		if reset1='0' then
			fifo_wr_en_reg<='0';
			fifo_din_reg<=x"00";
		elsif rising_edge(clk) then
			fifo_din_reg<=fifo_din;
			fifo_wr_en_reg<=fifo_wr_en;
		end if;
	end process;
	 
	 FIFO_0:FIFO8192x8 port map
	(rst=>rst,wr_clk=>clk,rd_clk=>clk,din=>fifo_din_reg,wr_en=>fifo_wr_en_reg,rd_en=>fifo_rd_en,dout=>fifo_data,
	full=>open,empty=>fifo_empty,rd_data_count=>fifo_rd_data_count,wr_data_count=>open,prog_full=>fifo_prog_full_reg0);
end Behavioral;

