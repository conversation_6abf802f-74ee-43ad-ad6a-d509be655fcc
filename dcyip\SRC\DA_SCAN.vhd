----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    20:23:48 11/13/2022 
-- Design Name: 
-- Module Name:    DA_SCAN - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;
--扫描模式，DA以秒为单位进行更新

entity DA_SCAN is
Port ( 	
			clk			: in std_logic;
			reset 		: in std_logic;
			
			start_scan    : in std_logic;
			s_edge        : in std_logic;
			
			da_scan_step  : in std_logic_vector(11 downto 0);
			da_scan_time  : in std_logic_vector(3 downto 0);
			da_scan_hdata : in std_logic_vector(11 downto 0);
			da_scan_ldata : in std_logic_vector(11 downto 0);			
            da_out        : out std_logic_vector(15 downto 0)			
	);
end DA_SCAN;

architecture Behavioral of DA_SCAN is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";
signal scan_change: std_logic;
signal cnt:integer range 0 to 15;
signal da_state: std_logic;
signal data_out: std_logic_vector(11 downto 0);	



begin

da_out(15 downto 12)<=x"C";
da_out(11 downto 0)<=data_out;

	process(clk,reset)
	begin
		if reset='0' then
			cnt<=0;
		else
			if clk'event and clk='1' then
                if start_scan='1' then 
				    if s_edge='1' then 
					    if  cnt=da_scan_time then 
					        cnt<=0;
					    else
					        cnt<=cnt+1;
                        end if;	
                    end if;						
				else
			        cnt<=0;   
				end if;
			end if;
		end if;
	end process;
	
	process(clk,reset)
	begin
		if reset='0' then
			scan_change<='0';
		else
			if clk'event and clk='1' then
                if s_edge='1' and cnt=da_scan_time then 
					scan_change<='1';	
				else
			        scan_change<='0';	   
				end if;
			end if;
		end if;
	end process;

process(clk,reset)
	begin
		if reset='0' then
			data_out<=(others=>'0');
			da_state<='0';
		else
			if clk'event and clk='1' then
                if start_scan='1' then 
				    if scan_change='1' then 
				        if da_state='0' then 
				    	    if data_out<(da_scan_hdata-da_scan_step) then 							   
				    	       data_out<=(data_out+da_scan_step);
				    	    else
				    		    data_out<=da_scan_hdata;
				    			da_state<='1';
				    		end if;
				    	elsif da_state='1' then 
				    	    if  data_out>(da_scan_ldata+da_scan_step) then 
				    		    data_out<=(data_out-da_scan_step);
				    		else
--							    data_out(15 downto 12)<=x"C";
				    		    data_out<=da_scan_ldata;
				    			da_state<='0';
				    		end if;
						else
						    da_state<='0';
				    	end if;								           
				    end if;
				else
				   data_out<=x"800";
				end if;
			end if;
		end if;
	end process;	

end Behavioral;

