----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    11:02:22 09/10/2021 
-- Design Name: 
-- Module Name:    CMD_ANALY - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--根据校时指令和工参指令，输出应答帧
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--|MSB |bit6|bit5|bit4|bit3|bit2    |bit1          |bit0
--|FGM4|FGM3|FGM2|FGM1|预留|扫描模式|1-高速，0-低速|1-地面，0-在轨
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity CMD_ANALY is
port(
    reset: 		in std_logic;
    clk : 		in std_logic; 
	clk_us_edge:  in std_logic;
	
	rev_flag    : in std_logic;
	
	rev_ok_flag     : in std_logic ;       
    rev_error_flag  : in std_logic ;
    error_state     : in std_logic_vector(7 downto 0);
	cmd_type        : in std_logic_vector(7 downto 0);
	
	trans_error_flag : in std_logic ;
	trans_error_type : in std_logic_vector(7 downto 0);
--指令ram	
	ram_addr:  out std_logic_vector(7 downto 0);
    data_in:  in std_logic_vector(7 downto 0);

--校时指令输出	
    s_data:  out std_logic_vector(31 downto 0);
    ms_data: out std_logic_vector(15 downto 0);
	time_set: out std_logic;
	
	send_start : out std_logic;
	data_out  : inout std_logic_vector(7 downto 0);
	send_over : in std_logic;
	tx_state  : out std_logic;
--科学数据输入	
    s_data_in    :in std_logic_vector(31 downto 0);
    ms_data_in   :in std_logic_vector(15 downto 0);
    data_out_lx1  :in std_logic_vector(7 downto 0);
	data_out_lx2  :in std_logic_vector(7 downto 0);
	data_out_lx3  :in std_logic_vector(7 downto 0);
	data_out_lx4  :in std_logic_vector(7 downto 0);
	data_out_lx5  :in std_logic_vector(7 downto 0);
	tmp1          :in std_logic_vector(7 downto 0);
	tmp2          :in std_logic_vector(7 downto 0);
	tmp3          :in std_logic_vector(7 downto 0);
	tmp4          :in std_logic_vector(7 downto 0);
	dy            :in std_logic_vector(7 downto 0);
	cmd_addr_rd   :out  std_logic_vector (9 downto 0);
	cmd_fgm       :in std_logic_vector(7 downto 0);
--DA
     da1_data   :in std_logic_vector(15 downto 0);
     da2_data   :in std_logic_vector(15 downto 0);
     da3_data   :in std_logic_vector(15 downto 0);
     da4_data   :in std_logic_vector(15 downto 0);
--上注指令输出：
   mode_change : out std_logic;
   cmd_mode    : out std_logic_vector(7 downto 0);
   da_change   : out std_logic;
   da_data     : out std_logic_vector(47 downto 0);
   fgm_chanel_data  : out std_logic_vector(47 downto 0);
   fgm_change  : out std_logic;
--   lc_change   : out std_logic;
   
--指令计数
  
  --cmd_count_right:out std_logic_vector(7 downto 0);
  cmd_count_error:out std_logic_vector(7 downto 0);
  cmd_error_type :out std_logic_vector(7 downto 0);
  cmd_count_lx   :out std_logic_vector(7 downto 0);
  cmd_count_time   :out std_logic_vector(7 downto 0);
  cmd_count_sz   :out std_logic_vector(7 downto 0);
  cmd_sz_errcnt  :out std_logic_vector(7 downto 0);
  cmd_sz_errtype :out std_logic_vector(7 downto 0);
  
  --cmd_last       :out std_logic_vector(7 downto 0);
  --cmd_sz_code    :out std_logic_vector(7 downto 0);
  cmd_sz_data    :out std_logic_vector(47 downto 0);
  
--  off_state      : out std_logic;
  off_set       : out std_logic;
  power_state    : in std_logic_vector(7 downto 0)
);
end CMD_ANALY;

architecture Behavioral of CMD_ANALY is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

type sc_state_t is (sc_start_s,wait_s,rev_data_s,answer_wait,trans_wait,answer_start,send_s,send_end);
signal sc_state : sc_state_t;
signal cmd_code: std_logic_vector(7 downto 0);
signal ram_rd:std_logic;
signal ram_rd_reg:std_logic;
signal rev_cnt:std_logic_vector(7 downto 0);
signal rev_max:std_logic_vector(7 downto 0);
signal send_max:std_logic_vector(31 downto 0);

signal cmd_stat:std_logic_vector(7 downto 0);

signal checksum: std_logic_vector(15 downto 0);
signal send_cnt: integer range 0 to 898;
signal send_end_edge:std_logic;
signal send_edge:std_logic;
signal start:std_logic;
signal time_cnt:std_logic_vector(3 downto 0);

signal packet_cnt:std_logic_vector(15 downto 0);
signal cnt_ok   :std_logic_vector(7 downto 0);
signal cnt_error:std_logic_vector(7 downto 0);
signal cmd_lx_cnt   :std_logic_vector(7 downto 0);
signal cmd_sz_cnt   :std_logic_vector(7 downto 0);
signal cmd_time_cnt   :std_logic_vector(7 downto 0);
--signal cmd_last_type:std_logic_vector(7 downto 0);
signal cmd_error_state:std_logic_vector(7 downto 0);
signal cmd_sz_error_cnt:std_logic_vector(7 downto 0);
signal cmd_sz_error_type:std_logic_vector(7 downto 0);

signal error_state_reg:std_logic_vector(7 downto 0);

signal power_set:std_logic;
signal eng_set:std_logic;
--signal off_set:std_logic;

signal cmd_data:std_logic_vector(47 downto 0);
signal eng_data:std_logic_vector(15 downto 0);
--signal off_data:std_logic_vector(15 downto 0);

signal eng_cnt:std_logic_vector(2 downto 0);
signal power_cnt:std_logic_vector(3 downto 0);
--signal off_cnt:std_logic_vector(2 downto 0);
--signal off_state:std_logic;

signal send_over_reg:std_logic;
signal send_over_reg2:std_logic;
signal addr:std_logic_vector(9 downto 0);
signal time_reg_data:std_logic_vector(47 downto 0);
--signal cmd_mode_save:std_logic_vector(7 downto 0);

signal da1_reg_data:std_logic_vector(15 downto 0);
signal da2_reg_data:std_logic_vector(15 downto 0);
signal da3_reg_data:std_logic_vector(15 downto 0);
signal da4_reg_data:std_logic_vector(15 downto 0);
signal cmd_reg_data:std_logic_vector(47 downto 0);

signal us_cnt: integer range 0 to 60000;



begin
--指令状态

--cmd_count_right<=cnt_ok;
cmd_count_error<=cnt_error;
cmd_sz_data<=cmd_data;
--cmd_sz_code<=cmd_code;
--cmd_last<=cmd_last_type;
cmd_count_lx<=cmd_lx_cnt;
cmd_count_time<=cmd_time_cnt;
cmd_count_sz<=cmd_sz_cnt;
--cmd_mode<=cmd_mode_save;

cmd_error_type<=cmd_error_state;
cmd_sz_errcnt<=cmd_sz_error_cnt;
cmd_sz_errtype<=cmd_sz_error_type;


--tx_state<=send_edge;

process(reset,clk)
	begin
		if reset='0' then
            cmd_stat<=x"00";
			cnt_ok<=(others=>'0');
			cnt_error<=(others=>'0');
			cmd_lx_cnt<=(others=>'0');
			cmd_time_cnt<=(others=>'0');
			cmd_sz_cnt<=(others=>'0');
			cmd_error_state<=(others=>'0');
		else
		    if clk='1' and clk'event then
			    if trans_error_flag='1' then
				    cnt_error<=cnt_error+1;
					cmd_error_state<=trans_error_type;				
			    elsif rev_ok_flag ='1' then
                    cmd_stat<=x"F0";
					if cmd_type=x"25" then
					    cmd_lx_cnt <=cmd_lx_cnt +1 ;
					elsif cmd_type=x"13" then
					    cmd_time_cnt<=cmd_time_cnt+1;
					elsif cmd_type=x"87" or cmd_type=x"94" then
					    cmd_sz_cnt<=cmd_sz_cnt+1;
					else
					    cnt_ok<=cnt_ok+1;
					end if ;
				else 
				    if rev_error_flag='1' then
                        cmd_stat<=x"FF";
						cnt_error<=cnt_error+1;
						cmd_error_state<=error_state;
					end if;
				end if;
            end if;
        end if;
   	end process;

process(reset,clk)
	begin
		if reset='0' then
            error_state_reg<=(others=>'0');
		else
		    if clk='1' and clk'event then
                error_state_reg<=error_state;
			end if;
		end if;
	end process;

	
--rev_max、send_max	
	process(reset,clk)
	begin
		if reset='0' then
            rev_max<=(others=>'0');
			send_max<=(others=>'0');
		else
		    if clk='1' and clk'event then
			    if rev_ok_flag ='1'then
			        if cmd_type = x"13" then    --校时指令
				    	rev_max<=x"06";
				    	send_max<=x"00000003";--应答4
				    elsif cmd_type = x"25" then --工程参数轮询指令
				    	rev_max<=x"02";
				    	send_max<=x"00000053";--84
				    elsif cmd_type = x"87" then  --数据注入指令
				    	rev_max<=x"06";
				    	send_max<=x"00000003";
					elsif cmd_type = x"94" then  --载荷预关机指令
				    	rev_max<=x"02";
				    	send_max<=x"00000003";						
                    else						
				        rev_max<=x"00";
				        send_max<=x"00000003";
					end if;
--				    end if;
				else
				    if rev_error_flag='1' then
					    rev_max<=x"00";
				    	send_max<=x"00000003";
					end if;
				end if;
            end if;
        end if;
   	end process;		
	
	
process(reset,clk)
	begin
		if reset='0' then
            ram_rd_reg<='0';
		else
		    if clk='1' and clk'event then
			    ram_rd_reg<=ram_rd;
            end if;
        end if;
   	end process;
	
--校时指令	
	process(reset,clk)
	begin
		if reset='0' then
            s_data<=(others=>'0');
			ms_data<=(others=>'0');
			time_set<='0';
			time_cnt<=(others=>'0');
		else
		    if clk='1' and clk'event then
				if cmd_type=x"13" and ram_rd_reg ='1' then 
				    if time_cnt=0 then s_data(31 downto 24)<=data_in; time_cnt<=time_cnt+1;
			        elsif time_cnt=1 then s_data(23 downto 16)<=data_in;  time_cnt<=time_cnt+1;
					elsif time_cnt=2 then s_data(15 downto 8) <=data_in;  time_cnt<=time_cnt+1;  
					elsif time_cnt=3 then s_data(7 downto 0)  <=data_in;  time_cnt<=time_cnt+1;  
					elsif time_cnt=4 then ms_data(15 downto 8)<=data_in;  time_cnt<=time_cnt+1; 
					elsif time_cnt=5 then ms_data(7 downto 0) <=data_in;  time_set<='1';time_cnt<=(others=>'0');
					else time_set<='0';time_cnt<=(others=>'0');
					end if;
				else
				    time_set<='0';time_cnt<=(others=>'0');
				end if;	
            end if;
        end if;
   	end process;	
--工况指令	
	process(reset,clk)
	begin
		if reset='0' then
            cmd_code<=(others=>'0');
			cmd_data<=(others=>'0');
			power_set<='0';
			off_set<='0';
			power_cnt<=(others=>'0');
		else
		    if clk='1' and clk'event then
				if cmd_type=x"87" and ram_rd_reg ='1' then 
					if power_cnt=0 then cmd_data(47 downto 40)<=data_in;  power_cnt<=power_cnt+1;
			        elsif power_cnt=1 then cmd_data(39 downto 32)<=data_in;  power_cnt<=power_cnt+1;
					elsif power_cnt=2 then cmd_data(31 downto 24)<=data_in;  power_cnt<=power_cnt+1;
			        elsif power_cnt=3 then cmd_data(23 downto 16)<=data_in;  power_cnt<=power_cnt+1;
					elsif power_cnt=4 then cmd_data(15 downto 8) <=data_in;  power_cnt<=power_cnt+1;  
					elsif power_cnt=5 then cmd_data(7 downto 0)  <=data_in;  power_set<='1';power_cnt<=(others=>'0');
					else  power_set<='0'; power_cnt<=(others=>'0');
					end if;
				elsif cmd_type=x"94" and ram_rd_reg ='1' then 
				    if power_cnt=0 then cmd_data(47 downto 40)<=data_in;  power_cnt<=power_cnt+1;
			        elsif power_cnt=1 then cmd_data(39 downto 32)<=data_in;  cmd_data(31 downto 0)<=(others=>'0'); off_set<='1';power_cnt<=(others=>'0');
					else off_set<='0'; power_cnt<=(others=>'0');
					end if;
				else
				    power_set<='0'; power_cnt<=(others=>'0');off_set<='0';
				end if;	
            end if;
        end if;
   	end process;	
	
--工参轮询指令	
	process(reset,clk)
	begin
		if reset='0' then
			eng_data<=(others=>'0');
			eng_set<='0';
			eng_cnt<=(others=>'0');
		else
		    if clk='1' and clk'event then
				if cmd_type=x"25" and ram_rd_reg ='1' then 
					if eng_cnt=0 then eng_data(15 downto 8)<=data_in;  eng_cnt<=eng_cnt+1;
			        elsif eng_cnt=1 then eng_data(7 downto 0)<=data_in; eng_cnt<=(others=>'0');eng_set<='1';
                    else eng_set<='0';	
                    end if;
				else 
				    eng_set<='0';eng_cnt<=(others=>'0');
                end if;
            end if;
        end if;
    end process;	
	
--载荷预关机指令
	-- process(reset,clk)
	-- begin
		-- if reset='0' then
			-- off_data<=(others=>'0');
			-- off_set<='0';
			-- off_cnt<=(others=>'0');
		-- else
		    -- if clk='1' and clk'event then
				-- if cmd_type=x"94" and ram_rd_reg ='1' then 
					-- if off_cnt=0 then off_data(15 downto 8)<=data_in;  off_cnt<=off_cnt+1;
			        -- elsif off_cnt=1 then off_data(7 downto 0)<=data_in; off_cnt<=(others=>'0');off_set<='1';
                    -- else off_set<='0';	
                    -- end if;
				-- else 
				    -- off_set<='0';	off_cnt<=(others=>'0'); 
                -- end if;
            -- end if;
        -- end if;
    -- end process;
	
	-- process(reset,clk)
	-- begin
		-- if reset='0' then
            -- off_state<='0';
		-- else
		    -- if clk='1' and clk'event then
			    -- if off_set='1' then 
				    -- off_state<='1';
				-- end if;
			-- end if;
		-- end if;
    -- end process;
	
	

process(reset,clk)
	begin
		if reset='0' then
			cmd_mode<=x"00";
			mode_change<='0';
			da_change<='0';
			da_data<=(others=>'0');
			cmd_sz_error_cnt<=(others=>'0');
			cmd_sz_error_type<=(others=>'0');
			fgm_chanel_data<=(others=>'0');
			fgm_change<='0';
			
			
		else
		    if clk='1' and clk'event then
			    if power_set='1'then
				    --低速工作模式
				    if cmd_data=x"111111111111"  then cmd_mode<=x"11";mode_change<='1';
					--高速工作模式
				    elsif cmd_data=x"222222222222" then cmd_mode<=x"22";mode_change<='1';
					--扫描模式
					elsif cmd_data=x"444444444444" then cmd_mode<=x"44";mode_change<='1';
					--地面量程
					elsif cmd_data=x"AAAAAAAAAAAA" then cmd_mode<=x"AA";mode_change<='1';
					--在轨量程
					elsif cmd_data=x"BBBBBBBBBBBB" then cmd_mode<=x"BB";mode_change<='1';
					--探针驱动电流调整
					elsif cmd_data(47 downto 24)=x"CCCCCC" then cmd_mode<=x"CC";da_data<=cmd_data;da_change<='1';mode_change<='1';
					--探针驱动电流设置参数
					elsif cmd_data(47 downto 40)=x"DD" then da_data<=cmd_data;da_change<='1';
                    --磁场工作模式设置					
					elsif cmd_data(47 downto 40)=x"EE" then fgm_chanel_data<=cmd_data;fgm_change<='1';
					--磁场工作模式调整
					elsif cmd_data(47 downto 24)=x"333333" then fgm_chanel_data<=cmd_data;fgm_change<='1';
			        else  mode_change<='0';da_change<='0';cmd_sz_error_cnt<=cmd_sz_error_cnt+1;cmd_sz_error_type<=x"25";
					end if;
				else
					mode_change<='0';da_change<='0';fgm_change<='0';
				end if;
            end if;
        end if;
   	end process;
	
	process(reset,clk)
	begin
		if reset ='0' then
			send_start <='0';
		else
			if clk='1' and clk'event then
				send_start <=start;
			end if;
		end if;
	end process;
	
--	send_start <=start;
	
	packet_cnt_process:process(reset,clk)
	begin
		if reset ='0' then
			packet_cnt <=x"0000";
		else
			if clk='1' and clk'event then
				if send_over = '1' and send_cnt =15 then
						if packet_cnt=x"3FFF" then
							packet_cnt<=x"0000";
						else
							packet_cnt <=packet_cnt+1;
						end if;
				end if;
			end if;
		end if;
	end process;
	
    process(reset,clk)
	begin
		if reset ='0' then
			send_over_reg <='0';
			send_over_reg2 <='0';
		else
			if clk='1' and clk'event then
			    send_over_reg <=send_over;
			    send_over_reg2<=send_over_reg;
			end if;
		end if;
	end process;
	
					
     
 process(reset,clk)
--	variable cnt: integer range 0 to 2400;
	begin
		if reset='0' then
--			cnt :=0;
			sc_state	<=sc_start_s;
			ram_addr		<=(others=>'0');
			start	<='0';
			send_end_edge<='0';
			rev_cnt <=(others=>'0');
			send_edge<='0';
			ram_rd<='0';  
			tx_state<='0';
			 us_cnt<=0;
		else
			if clk='1' and clk'event then
				case sc_state is 
					when sc_start_s =>
					    send_end_edge<='0';
						us_cnt<=0;
						if rev_ok_flag ='1'then 
							rev_cnt <=(others=>'0');
							sc_state<=wait_s;
						else
						    if rev_error_flag='1' then
						        sc_state<=answer_wait;
							end if;
						end if;
					when wait_s =>
					    sc_state<=rev_data_s;
						
                    when rev_data_s =>                   
						if rev_cnt>=rev_max then 
						    ram_addr<=(others=>'0');
					        ram_rd<='0';     
                            rev_cnt<=(others=>'0');
							sc_state<=answer_wait;
							
                        else 								
                            ram_addr<=rev_cnt+5;
					        ram_rd<='1'; 
                            rev_cnt<=rev_cnt+1;
                            sc_state<=rev_data_s;
						end if;	
                      when answer_wait=>
					    if cmd_type=x"13" then
						   sc_state<=send_end;
						elsif error_state_reg=x"13" then 
						    sc_state<=send_end;
					    else 
						    if rev_flag ='0' then
						       tx_state<='1';
							   us_cnt<=0;
							   sc_state<=trans_wait;
							else 
							   if clk_us_edge ='1' then
							        if  us_cnt> 100 then
									    tx_state<='1';
									    us_cnt<=0;
										sc_state<=trans_wait;
						            else 
						                us_cnt<=us_cnt+1;
						    	        sc_state<=answer_wait;
									end if;
						    	end if;
						    end if;	
                        end if;		
                    when trans_wait=>
                        if clk_us_edge ='1' then
					        if  us_cnt>= 1000 then
							    us_cnt<=0;
						 	    sc_state<=answer_start;
					        else 
					             us_cnt<=us_cnt+1;
					            sc_state<=trans_wait;
						    end if;
					    end if;				
                    when answer_start =>
					    send_edge<='1';
						start<='1';
						sc_state<=send_s;
						
					 when send_s =>
					    start<='0';
						if send_over ='1' then
							if send_cnt >=send_max then
							   sc_state<=send_end;
							else 
							    sc_state<=answer_start;
							end if;
						end if;								
				    when send_end =>
					    sc_state<=sc_start_s;
						send_edge<='0';
						send_end_edge<='1';
						tx_state<='0';
					when others => sc_state<=sc_start_s;
				end case;	
			end if;
		end if;
	end process;
    
	
	send_cnt_process:process(reset,clk)
	begin
		if reset ='0' then
			send_cnt <=0;
		else
			if clk='1' and clk'event then
				if send_edge = '1'and send_over='1' then
					if send_cnt >=send_max then
						send_cnt <=0;
					else
					    send_cnt<=send_cnt+1;	
					end if;
				end if;
			end if;
		end if;
	end process;	
	
--组建工程数据源包	
cmd_addr_rd<=addr;
	pram_read_process:process(reset,clk)
	begin
		if reset ='0' then
			addr <=(others=>'0');
		else
			if clk='1' and clk'event then
				if send_edge = '1'and cmd_type = x"25" then
--磁场值 748 34-45  
                    if send_over='1' then  
                        if send_cnt=32 then 
					        addr<=conv_std_logic_vector(0,10);  
					    elsif send_cnt>=33 and send_cnt<=43 then
					        addr<=addr+conv_std_logic_vector(3,10);	
                        else
					        addr<=(others=>'0');
					    end if;
					end if;
				end if;
			end if;
		end if;
	end process;	

	
    checksum_process:process(reset,clk)
	begin
		if reset ='0' then
			checksum <=(others=>'0');
		else
			if clk='1' and clk'event then
			    if send_edge = '1'then
				    if send_over_reg2='1' then
				        if send_cnt >=8 and send_cnt<send_max-1 then	
						    checksum<=conv_std_logic_vector(conv_integer(checksum)+conv_integer(data_out),16);
 --                            checksum<=conv_std_logic_vector(conv_integer(checksum)+1,16);
						else
						    checksum<=checksum;	
						end if;
					else
					    checksum<=checksum;	
					end if;
				else
					checksum <=(others=>'0');
				end if;
			end if;
		end if;
	end process;	

	
	
	data_out_process:process(reset,clk)
	begin
		if reset ='0' then
			data_out<=x"00";
			time_reg_data<=(others=>'0');
	        da1_reg_data<=(others=>'0');
			da2_reg_data<=(others=>'0');
            da3_reg_data<=(others=>'0');
            da4_reg_data<=(others=>'0');
            cmd_reg_data<=(others=>'0');
		else
			if clk='1' and clk'event then
				if send_edge = '1'then
				    if cmd_stat=x"F0" then
					    if cmd_type = x"25"and eng_data=x"5a5a" then
                            if send_cnt = 0  then  				
                                data_out<= x"EB";	
--锁存其他数据									
								time_reg_data(47 downto 16)<=s_data_in;
				                time_reg_data(15 downto 0)<=ms_data_in;     
	                            da1_reg_data <= da1_data;
	                            da2_reg_data <= da2_data;
	                            da3_reg_data <= da3_data;
	                            da4_reg_data <= da4_data;
	                            cmd_reg_data <= cmd_data;
								-- power_reg_state <=power_state;
								-- reg_cmd_sz_cnt  <=cmd_sz_cnt;
								-- reg_dy          <=dy;
								-- reg_tmp1        <=tmp1;
								-- reg_tmp2        <=tmp2;
								-- reg_tmp3        <=tmp3;
								-- reg_tmp4        <=tmp4;
								-- reg_data_out_lx1<=data_out_lx1;
								-- reg_data_out_lx2<=data_out_lx2;
								-- reg_data_out_lx3<=data_out_lx3;
								-- reg_data_out_lx4<=data_out_lx4;
								-- reg_data_out_lx5<=data_out_lx5;
								-- reg_cmd_sz_cnt   <= cmd_sz_cnt   ;
								-- reg_dy           <= dy           ;
								-- reg_tmp1         <= tmp1         ;
								-- reg_tmp2         <= tmp2         ;
								-- reg_tmp3         <= tmp3         ;
								-- reg_tmp4         <= tmp4         ;
								-- reg_data_out_lx1 <= data_out_lx1 ;
								-- reg_data_out_lx2 <= data_out_lx2 ;
								-- reg_data_out_lx3 <= data_out_lx3 ;
								-- reg_data_out_lx4 <= data_out_lx4 ;
								-- reg_data_out_lx5 <= data_out_lx5 ;								
								
                                								
			                else 
							    if send_over_reg='1' then 							
				                        if send_cnt =1 then data_out<= x"90";     
					                    elsif send_cnt =2 then data_out<= x"01"; --包识别
					                    elsif send_cnt =3 then data_out<= x"73"; 
					                    elsif send_cnt =4 then data_out(7 downto 6)<="11";data_out(5 downto 0)<= packet_cnt(13 downto 8); --包计数
					                    elsif send_cnt =5 then data_out<= packet_cnt(7 downto 0); 
					                    elsif send_cnt =6 then data_out<=x"00"; 
					                    elsif send_cnt =7 then data_out<=x"4B"; 									
					                    elsif send_cnt =8 then data_out<=time_reg_data(47 downto 40);
							            elsif send_cnt =9 then data_out<=time_reg_data(39 downto 32);
							            elsif send_cnt =10 then data_out<=time_reg_data(31 downto 24);
							            elsif send_cnt =11 then data_out<=time_reg_data(23 downto 16);
							            elsif send_cnt =12 then data_out<=time_reg_data(15 downto 8);
							            elsif send_cnt =13 then data_out<=time_reg_data(7 downto 0);
							            elsif send_cnt =14 then data_out<=power_state;
					                    elsif send_cnt =15 then data_out<=cmd_sz_cnt;
							            elsif send_cnt =16 then data_out<=dy;
							            elsif send_cnt =17 then data_out<=tmp1;
							            elsif send_cnt =18 then data_out<=tmp2;
							            elsif send_cnt =19 then data_out<=tmp3;
							            elsif send_cnt =20 then data_out<=tmp4;
							            elsif send_cnt =21 then data_out<=data_out_lx1;
							            elsif send_cnt =22 then data_out<=data_out_lx2;
							            elsif send_cnt =23 then data_out<=data_out_lx3;
							            elsif send_cnt =24 then data_out<=data_out_lx4;
							            elsif send_cnt =25 then data_out<=data_out_lx5; 
                                        elsif send_cnt=26 then data_out<=da1_reg_data(15 downto 8);
                                        elsif send_cnt=27 then data_out<=da1_reg_data(7 downto 0);   							
							            elsif send_cnt=28 then data_out<=da2_reg_data(15 downto 8); 
							            elsif send_cnt=29 then data_out<=da2_reg_data(7 downto 0); 
							            elsif send_cnt=30 then data_out<=da3_reg_data(15 downto 8);
							            elsif send_cnt=31 then data_out<=da3_reg_data(7 downto 0); 
							            elsif send_cnt=32 then data_out<=da4_reg_data(15 downto 8);
                                        elsif send_cnt=33 then data_out<=da4_reg_data(7 downto 0);  							
							            elsif send_cnt >=34 and send_cnt <=45  then data_out<=cmd_fgm; 
							            elsif send_cnt =46 then data_out<=cmd_lx_cnt;
							            elsif send_cnt =47 then data_out<=cmd_time_cnt; 
							            elsif send_cnt =48 then data_out<=cmd_sz_error_cnt; 
							            elsif send_cnt =49 then data_out<=cmd_sz_error_type;
							            elsif send_cnt =50 then data_out<=cnt_error;
							            elsif send_cnt =51 then data_out<=cmd_error_state;
							            elsif send_cnt =52 then data_out<=cmd_reg_data(47 downto 40);
                                        elsif send_cnt =53 then data_out<=cmd_reg_data(39 downto 32);
                                        elsif send_cnt =54 then data_out<=cmd_reg_data(31 downto 24);
                                        elsif send_cnt =55 then data_out<=cmd_reg_data(23 downto 16);
                                        elsif send_cnt =56 then data_out<=cmd_reg_data(15 downto 8);
                                        elsif send_cnt =57 then data_out<=cmd_reg_data(7 downto 0);
							            elsif send_cnt >=58 and send_cnt <=81  then data_out<=x"AA";					
					                    elsif send_cnt =(send_max-1) then data_out<= checksum(15 downto 8); --校验  
					                    elsif send_cnt =send_max then data_out<= checksum(7 downto 0); --校验 
										else  data_out<= x"00";
										end if;
							    end if;
					        end if;	
					    else
					        if send_cnt = 0  then  data_out<= x"EB";
				            elsif send_cnt =1 then data_out<= x"90";    
				            elsif send_cnt =2 then data_out<= x"13"; --载荷地址        
				            elsif send_cnt =3 then data_out<= x"F0"; --指令状态
					        else  data_out<= x"00";
					        end if;		
                        end if;
                    else
                        if send_cnt = 0  then  data_out<= x"EB";
				        elsif send_cnt =1 then data_out<= x"90";    
				        elsif send_cnt =2 then data_out<= x"13"; --载荷地址        
				        elsif send_cnt =3 then data_out<= x"FF"; --指令状态
					    else  data_out<= x"00";
					    end if;							
					end if;     				
				end if;
			end if;
		end if;
	end process;	


end Behavioral;

