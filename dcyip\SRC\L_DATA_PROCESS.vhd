----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    11:24:37 10/24/2022 
-- Design Name: 
-- Module Name:    DATA_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity L_DATA_PROCESS is
port (
    clk : in std_logic;
    reset : in std_logic;
   
	read_start: in std_logic;
	
	start_edge:in std_logic;
	time_edge1 :in std_logic;
	time_edge2 :in std_logic;
	time_edge3 :in std_logic;
	
	fgm_chanel  : in std_logic_vector(7 downto 0); 
	
	
	read_over: in std_logic;
	fgm_rd_over: in std_logic;
	gc_rd_over:  in std_logic;	

	data_in1:  in std_logic_vector(15 downto 0);
	data_in2:  in std_logic_vector(15 downto 0);
	data_in3:  in std_logic_vector(15 downto 0);
	data_in4:  in std_logic_vector(15 downto 0);
	data_in5:  in std_logic_vector(15 downto 0);
	fgm_data:  in std_logic_vector(15 downto 0);
	gc_data:  in std_logic_vector(15 downto 0);
	
	sample_end1 : out std_logic;  	
	ram_dout1   : out std_logic_vector (7 downto 0);
	addr_rd1    : in  std_logic_vector(7 downto 0);
	
	sample_end2 : out std_logic;
	ram_dout2   : out std_logic_vector (7 downto 0);
	addr_rd2    : in  std_logic_vector(7 downto 0);
	
	sample_end3 : out std_logic;
	ram_dout3   : out std_logic_vector (7 downto 0);
	addr_rd3    : in  std_logic_vector(7 downto 0);
	
	sample_end4 : out std_logic;
	ram_dout4   : out std_logic_vector (7 downto 0);
	addr_rd4    : in  std_logic_vector(7 downto 0);
	
	sample_end5 : out std_logic;
	ram_dout5   : out std_logic_vector (7 downto 0);
	addr_rd5    : in  std_logic_vector(7 downto 0);
	
	sample_end6 : out std_logic;
	ram_dout6   : out std_logic_vector (7 downto 0);
	addr_rd6    : in  std_logic_vector(9 downto 0);
	
	sample_end7 : out std_logic;
	ram_dout7   : out std_logic_vector (7 downto 0);
	addr_rd7    : in  std_logic_vector(7 downto 0);

--CMD
    data_out_lx1  :out std_logic_vector(7 downto 0);
	data_out_lx2  :out std_logic_vector(7 downto 0);
	data_out_lx3  :out std_logic_vector(7 downto 0);
	data_out_lx4  :out std_logic_vector(7 downto 0);
	data_out_lx5  :out std_logic_vector(7 downto 0);
    tmp1          :out std_logic_vector(7 downto 0);
	tmp2          :out std_logic_vector(7 downto 0);
	tmp3          :out std_logic_vector(7 downto 0);
	tmp4          :out std_logic_vector(7 downto 0);
	dy            :out std_logic_vector(7 downto 0);
	cmd_addr_rd   :in  std_logic_vector (9 downto 0);
	cmd_fgm       :out std_logic_vector(7 downto 0)
		
);
end L_DATA_PROCESS;

architecture Behavioral of L_DATA_PROCESS is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal data_out1:std_logic_vector(7 downto 0);
signal addr_wr1 :std_logic_VECTOR(7 downto 0);
signal wr1      :std_logic; 
signal data_out2:std_logic_vector(7 downto 0);
signal addr_wr2 :std_logic_VECTOR(7 downto 0);
signal wr2      :std_logic; 
signal data_out3:std_logic_vector(7 downto 0);
signal addr_wr3 :std_logic_VECTOR(7 downto 0);
signal wr3      :std_logic; 
signal data_out4:std_logic_vector(7 downto 0);
signal addr_wr4 :std_logic_VECTOR(7 downto 0);
signal wr4      :std_logic; 
signal data_out5:std_logic_vector(7 downto 0);
signal addr_wr5 :std_logic_VECTOR(7 downto 0);
signal wr5      :std_logic; 
signal data_out6:std_logic_vector(7 downto 0);
signal addr_wr6 :std_logic_VECTOR(9 downto 0);
signal wr6      :std_logic; 
signal data_out7:std_logic_vector(7 downto 0);
signal addr_wr7 :std_logic_VECTOR(7 downto 0);
signal wr7      :std_logic; 

signal wr1_ram:std_logic_VECTOR(0 downto 0);
signal wr2_ram:std_logic_VECTOR(0 downto 0);
signal wr3_ram:std_logic_VECTOR(0 downto 0);
signal wr4_ram:std_logic_VECTOR(0 downto 0);
signal wr5_ram:std_logic_VECTOR(0 downto 0);
signal wr6_ram:std_logic_VECTOR(0 downto 0);
signal wr7_ram:std_logic_VECTOR(0 downto 0);


-- component H_EFI_Sample_sch1 
-- port (
    -- reset   : in std_logic;
    -- clk     : in std_logic;
	
    -- start_edge	: in std_logic;
	-- time_edge	: in std_logic;    
	-- data_in	: in std_logic_vector(15 downto 0);  
	-- rd_over : in std_logic;                        
                   	
	-- sample_end:out std_logic;
	
	-- data_out: out std_logic_vector(7 downto 0);
	-- wr:out std_logic;      
	-- addr:out std_logic_vector(7 downto 0);
	-- data_out_lx:out  std_logic_vector(7 downto 0)

-- );
-- end component;	
component L_EFI_Sample_sch is
port (
    reset   : in std_logic;
    clk     : in std_logic;
	
    start_edge	: in std_logic;
	time_edge	: in std_logic;    
	data_in	: in std_logic_vector(15 downto 0);  
	rd_over : in std_logic;                        
                   
	data_out: out std_logic_vector(7 downto 0);
	data_out_lx:out  std_logic_vector(7 downto 0);
	sample_end:out std_logic;
	
	wr:out std_logic;      
	addr:out std_logic_vector(7 downto 0)

);
end component;

component L_FGM_Sample_sch 
port (
    reset   : in std_logic;
    clk     : in std_logic;
	
    start_edge	: in std_logic;
	time_edge	: in std_logic;    
	data_in	: in std_logic_vector(15 downto 0);  
	rd_over : in std_logic;      
    fgm_chanel  : in std_logic_vector(7 downto 0); 		
                   
	data_out: out std_logic_vector(7 downto 0);
	sample_end:out std_logic;
	
	wr:out std_logic;      
	addr:out std_logic_vector(9 downto 0)

);
end component;	

component L_GC_Sample_sch 
port (
    reset   : in std_logic;
    clk     : in std_logic;
	
    start_edge	: in std_logic;
	time_edge	: in std_logic;    
	data_in	: in std_logic_vector(15 downto 0);  
	rd_over : in std_logic;                        
                   
	data_out: out std_logic_vector(7 downto 0);
	sample_end:out std_logic;
	
	wr:out std_logic;      
	addr:out std_logic_vector(7 downto 0);
	
	tmp1:out std_logic_vector(7 downto 0);
	tmp2:out std_logic_vector(7 downto 0);
	tmp3:out std_logic_vector(7 downto 0);
	tmp4:out std_logic_vector(7 downto 0);
	dy  :out std_logic_vector(7 downto 0)

);
end component;	

component E_RAM 
	port (
	addra: IN std_logic_VECTOR(7 downto 0);
	addrb: IN std_logic_VECTOR(7 downto 0);
	clka: IN std_logic;
	clkb: IN std_logic;
	dina: IN std_logic_VECTOR(7 downto 0);
	doutb: OUT std_logic_VECTOR(7 downto 0);
	wea: IN STD_LOGIC_VECTOR(0 DOWNTO 0));
END component;

component FGM1 
	port (
	addra: IN std_logic_VECTOR(9 downto 0);
	addrb: IN std_logic_VECTOR(9 downto 0);
	clka: IN std_logic;
	clkb: IN std_logic;
	dina: IN std_logic_VECTOR(7 downto 0);
	doutb: OUT std_logic_VECTOR(7 downto 0);
	wea: IN STD_LOGIC_VECTOR(0 DOWNTO 0));
END component;


begin

wr1_ram(0)<=wr1;
wr2_ram(0)<=wr2;
wr3_ram(0)<=wr3;
wr4_ram(0)<=wr4;
wr5_ram(0)<=wr5;
wr6_ram(0)<=wr6;
wr7_ram(0)<=wr7;

U_L_EFI1:L_EFI_Sample_sch
port map(
    clk         =>  clk         ,
    reset       =>  reset       ,  
	
    start_edge  =>  start_edge  ,
    time_edge   =>  time_edge1   ,	
    data_in     =>  data_in1    ,
    rd_over     =>  read_over     ,

    data_out    =>  data_out1    ,
    sample_end  =>  sample_end1  ,
	wr          =>  wr1          ,
	addr        =>  addr_wr1     ,
   data_out_lx	=> data_out_lx1  	
);	

U_L_EFI2:L_EFI_Sample_sch
port map(
    clk         =>  clk         ,
    reset       =>  reset       ,  
	
    start_edge  =>  start_edge  ,
    time_edge   =>  time_edge1   ,	
    data_in     =>  data_in2     ,
    rd_over     =>  read_over     ,
	

    data_out    =>  data_out2    ,
    sample_end  =>  sample_end2  ,
	wr          =>  wr2          ,
	addr        =>  addr_wr2     ,
    data_out_lx	=> data_out_lx2  	
);

U_L_EFI3:L_EFI_Sample_sch
port map(
    clk         =>  clk         ,
    reset       =>  reset       ,  
	
    start_edge  =>  start_edge  ,
    time_edge   =>  time_edge1   ,	
    data_in     =>  data_in3     ,
    rd_over     =>  read_over     ,

    data_out    =>  data_out3    ,
    sample_end  =>  sample_end3  ,
	wr          =>  wr3          ,
	addr        =>  addr_wr3      ,

   data_out_lx	=> data_out_lx3  	
);

U_L_EFI4:L_EFI_Sample_sch
port map(
    clk         =>  clk         ,
    reset       =>  reset       ,  
	
    start_edge  =>  start_edge  ,
    time_edge   =>  time_edge1   ,	
    data_in     =>  data_in4     ,
    rd_over     =>  read_over     ,

    data_out    =>  data_out4    ,
    sample_end  =>  sample_end4  ,
	wr          =>  wr4          ,
	addr        =>  addr_wr4     ,
   data_out_lx	=> data_out_lx4  	
);

U_L_EFI5:L_EFI_Sample_sch
port map(
    clk         =>  clk         ,
    reset       =>  reset       ,  
	
    start_edge  =>  start_edge  ,
    time_edge   =>  time_edge2   ,	
    data_in     =>  data_in5     ,
    rd_over     =>  read_over     ,

    data_out    =>  data_out5    ,
    sample_end  =>  sample_end5  ,
	wr          =>  wr5          ,
	addr        =>  addr_wr5     ,
	
    data_out_lx	=> data_out_lx5  
);

U_L_FGM:L_FGM_Sample_sch
port map(
    clk         =>  clk         ,
    reset       =>  reset       ,  
	
    start_edge  =>  start_edge  ,
    time_edge   =>  time_edge3   ,	
    data_in     =>  fgm_data     ,
    rd_over     =>  fgm_rd_over  ,
	fgm_chanel  =>  fgm_chanel   ,

    data_out    =>  data_out6    ,
    sample_end  =>  sample_end6  ,
	wr          =>  wr6          ,
	addr        =>  addr_wr6        
);
U_L_GC:L_GC_Sample_sch
port map(
    clk         =>  clk         ,
    reset       =>  reset       ,  
	
    start_edge  =>  start_edge  ,
    time_edge   =>  time_edge3   ,	
    data_in     =>  gc_data     ,
    rd_over     =>  gc_rd_over     ,

    data_out    =>  data_out7    ,
    sample_end  =>  sample_end7  ,
	wr          =>  wr7          ,
	addr        =>  addr_wr7     ,

	tmp1       =>   tmp1   ,
	tmp2       =>   tmp2   ,
	tmp3       =>   tmp3   ,
	tmp4       =>   tmp4   ,
	dy         =>   dy     
	
);

		EFI1_RAM : E_RAM
		port map (
			addra => addr_wr1,
			addrb => addr_rd1,
			clka  => clk,
			clkb  => clk,
			dina  => data_out1,
			doutb => ram_dout1,
			wea   => wr1_ram
			);
			
		EFI2_RAM : E_RAM
		port map (
			addra => addr_wr2,
			addrb => addr_rd2,
			clka  => clk,
			clkb  => clk,
			dina  => data_out2,
			doutb => ram_dout2,
			wea   => wr2_ram
			);
			
		EFI3_RAM: E_RAM
		port map (
			addra => addr_wr3,
			addrb => addr_rd3,
			clka => clk,
			clkb => clk,
			dina => data_out3,
			doutb => ram_dout3,
			wea   => wr3_ram);
			
		EFI4_RAM : E_RAM
		port map (
			addra => addr_wr4,
			addrb => addr_rd4,
			clka => clk,
			clkb => clk,
			dina => data_out4,
			doutb => ram_dout4,
			wea   => wr4_ram);
			
		EFI5_RAM : E_RAM
		port map (
			addra => addr_wr5,
			addrb => addr_rd5,
			clka => clk,
			clkb => clk,
			dina => data_out5,
			doutb => ram_dout5,
			wea   => wr5_ram);
			
		FGM1_RAM :FGM1
		port map (
			addra => addr_wr6,
			addrb => addr_rd6,
			clka => clk,
			clkb => clk,
			dina => data_out6,
			doutb => ram_dout6,
			wea   => wr6_ram);
			
		CMD_FGM_RAM :FGM1
		port map (
			addra => addr_wr6,
			addrb => cmd_addr_rd,
			clka => clk,
			clkb => clk,
			dina => data_out6,
			doutb => cmd_fgm,
			wea   => wr6_ram);
			
		GC_RAM : E_RAM
		port map (
			addra => addr_wr7,
			addrb => addr_rd7,
			clka => clk,
			clkb => clk,
			dina => data_out7,
			doutb => ram_dout7,
			wea   => wr7_ram);
end Behavioral;

