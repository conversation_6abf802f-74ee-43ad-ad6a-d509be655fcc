`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    09:58:27 10/29/2013 
// Design Name: 
// Module Name:    decoder 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module decoder_8b10b_5x(clk,data_in,rst,en,rk_flag,data_out,data_valid,data_out_valid,rk_false, data_3b_false, data_5b_false
    );
input  clk;
input  [9:0] data_in;
input  rst;
input  en;
input  data_valid;
output rk_flag;
output rk_false;
output data_3b_false;
output data_5b_false;
output [7:0] data_out;
output data_out_valid;

 reg   rk;
 reg   rk_flag;
 reg  [9:0] data_in_d;
 reg  [9:0] data_in_k;
 reg  [7:0] data_out;
  reg  [3:0] data_out_valid0_cnt;
 reg  d_decoder_en;
 reg data_valid1,data_valid2,data_valid3,data_valid_rise;
 reg data_out_valid0,data_out_valid1,data_out_valid2,data_out_valid3,data_out_valid;

 wire [7:0] data_out_d;
 wire [7:0] data_out_k;
 

d_decoder_5x ddecoder_5x (.rst(rst),
					.clk(clk),
					.data_in(data_in_d),
                   .data_out(data_out_d),
                   .data_valid(data_out_valid0),
						 .data_3b_false(data_3b_false),
						 .data_5b_false(data_5b_false)
                   );


k_decoder_5x kdecoder_5x (.rst(rst),
                   .clk(clk),
						 .data_in(data_in_k),
						 .data_out(data_out_k),
						 .data_valid(data_valid),
						 .rk_false(rk_false)
                   );


always @(posedge clk)	begin
	    if(!rst) begin
			 data_valid1 <= 1'b0;
			 data_valid2 <= 1'b0;
			 data_valid3 <= 1'b0;
       end
	    else begin
			 data_valid1 <= data_valid;
			 data_valid2 <= data_valid1;
			 data_valid3 <= data_valid2;
	    end
end

always @(posedge clk)	begin
	    if(!rst) begin
			 data_out_valid1 <= 1'b0;
			 data_out_valid2 <= 1'b0;
			 data_out_valid3 <= 1'b0;
       end
	    else begin
			 data_out_valid1 <= data_out_valid0;
			 data_out_valid2 <= data_out_valid1;
			 data_out_valid3 <= data_out_valid2;
			 data_out_valid <= data_out_valid3;
	    end
end



always @(posedge clk)	begin
	    if(!rst) begin
			 data_valid_rise <= 1'b0;
       end
	    else begin
		   if((data_valid2==1)&&(data_valid3==0)) begin
				 data_valid_rise <= 1'b1;
				 end	 
			 else
			   data_valid_rise <= 1'b0;
			 
	    end
end


always @(posedge clk)	begin
	    if(!rst) begin
			 data_out_valid0_cnt <= 4'b0000;
			  data_out_valid0 <= 1'b0; 
			  rk_flag <= 1'b0; 
       end
	    else if ((data_valid3==1)&&(data_valid2==0)) begin
			 data_out_valid0_cnt <= 1;
			 data_out_valid0 <= 1'b0; 
			  rk_flag <= 1'b0; 
			end
		 else if ((data_out_valid0_cnt>=1) && (data_out_valid0_cnt<=10)) begin
		     data_out_valid0_cnt <= data_out_valid0_cnt+1;
			  if (d_decoder_en) begin
			     data_out_valid0 <= 1'b1; 
			     rk_flag <= 1'b0;  
				  end
			  else if(rk) begin
			      data_out_valid0 <= 1'b0; 
			      rk_flag <= 1'b1; 
				  end
	    end
		 else  begin
		     data_out_valid0_cnt <= 4'b0000;
			  data_out_valid0 <= 1'b0; 
			  rk_flag <= 1'b0; 
	    end
end



						 
always @(posedge clk) begin
	if (!rst||!en) begin
		rk <= 0;
		data_in_d <= 8'b0000_0000;
		data_in_k <= 8'b0000_0000;
		d_decoder_en<= 0;
	end
	else if(data_valid_rise) begin//else if(data_valid) begin
      if ((data_in[9:4]==6'b001111)||(data_in[9:4]==6'b110000)) begin
       rk <= 1;
       data_in_k <= data_in;
		 d_decoder_en<= 0;
       end
      else if((data_in[3:0]==4'b1000)||(data_in[3:0]==4'b0111))begin
             case(data_in[9:4])
	          6'b111010,6'b110110,6'b101110,6'b011110,
	          6'b000101,6'b001001,6'b010001,6'b100001 : begin 
															rk <= 1;
															data_in_k <= data_in;
															d_decoder_en<= 0;
														end
	           default : begin 
						rk <= 0;
					   data_in_d <= data_in;
						d_decoder_en<= 1;
					end
	          endcase
	       end			 
	  else begin
	  rk <= 0;
	  data_in_d <= data_in;
	  d_decoder_en<= 1;
	  end	 
 end 
end

//always @(posedge clk) begin
//	if(!rst) 
//		rk_flag = 0;
//	else 
//		rk_flag = rk;
//end	

always @(posedge clk) begin
	if(!rst) 
		data_out <= 8'b0;
	else if(d_decoder_en) 
		data_out <= data_out_d;
	else if(rk) 
		data_out <= data_out_k;
end		

endmodule



module d_decoder_5x(clk,rst,data_in,data_out,data_valid,data_3b_false,data_5b_false);
input clk;
input rst;
input data_valid;
input [9:0] data_in;
output [7:0] data_out;
output data_3b_false,data_5b_false;
reg data_3b_false,data_5b_false;

 reg    [4:0] d5b_out;
 reg    [2:0] d3b_out;

assign data_out = {d3b_out,d5b_out};

always @(posedge clk) begin
	if(!rst) begin
		d5b_out <= 5'b00000;
		data_5b_false<= 1'b0;
	end
	else if (data_valid)begin	
    case({data_in[9:4]})
		6'b100111 : begin d5b_out <= 5'b00000; data_5b_false<= 1'b0; end
		6'b011101 : begin d5b_out <= 5'b00001; data_5b_false<= 1'b0; end
		6'b101101 : begin d5b_out <= 5'b00010; data_5b_false<= 1'b0; end
		6'b110001 : begin d5b_out <= 5'b00011; data_5b_false<= 1'b0; end
		6'b110101 : begin d5b_out <= 5'b00100; data_5b_false<= 1'b0; end
		6'b101001 : begin d5b_out <= 5'b00101; data_5b_false<= 1'b0; end
		6'b011001 : begin d5b_out <= 5'b00110; data_5b_false<= 1'b0; end
		6'b111000 : begin d5b_out <= 5'b00111; data_5b_false<= 1'b0; end
		6'b111001 : begin d5b_out <= 5'b01000; data_5b_false<= 1'b0; end
		6'b100101 : begin d5b_out <= 5'b01001; data_5b_false<= 1'b0; end
		6'b010101 : begin d5b_out <= 5'b01010; data_5b_false<= 1'b0; end
		6'b110100 : begin d5b_out <= 5'b01011; data_5b_false<= 1'b0; end
		6'b001101 : begin d5b_out <= 5'b01100; data_5b_false<= 1'b0; end
		6'b101100 : begin d5b_out <= 5'b01101; data_5b_false<= 1'b0; end
		6'b011100 : begin d5b_out <= 5'b01110; data_5b_false<= 1'b0; end
		6'b010111 : begin d5b_out <= 5'b01111; data_5b_false<= 1'b0; end
		//
		6'b011011 : begin d5b_out <= 5'b10000; data_5b_false<= 1'b0; end
		6'b100011 : begin d5b_out <= 5'b10001; data_5b_false<= 1'b0; end
		6'b010011 : begin d5b_out <= 5'b10010; data_5b_false<= 1'b0; end
		6'b110010 : begin d5b_out <= 5'b10011; data_5b_false<= 1'b0; end
		6'b001011 : begin d5b_out <= 5'b10100; data_5b_false<= 1'b0; end
		6'b101010 : begin d5b_out <= 5'b10101; data_5b_false<= 1'b0; end
		6'b011010 : begin d5b_out <= 5'b10110; data_5b_false<= 1'b0; end
		6'b111010 : begin d5b_out <= 5'b10111; data_5b_false<= 1'b0; end
		6'b001100 : begin d5b_out <= 5'b11000; data_5b_false<= 1'b0; end
		6'b100110 : begin d5b_out <= 5'b11001; data_5b_false<= 1'b0; end
		6'b010110 : begin d5b_out <= 5'b11010; data_5b_false<= 1'b0; end
		6'b110110 : begin d5b_out <= 5'b11011; data_5b_false<= 1'b0; end
		6'b001110 : begin d5b_out <= 5'b11100; data_5b_false<= 1'b0; end
		6'b101110 : begin d5b_out <= 5'b11101; data_5b_false<= 1'b0; end
		6'b011110 : begin d5b_out <= 5'b11110; data_5b_false<= 1'b0; end
		6'b101011 : begin d5b_out <= 5'b11111; data_5b_false<= 1'b0; end
		//
		6'b011000 : begin d5b_out <= 5'b00000; data_5b_false<= 1'b0; end
		6'b100010 : begin d5b_out <= 5'b00001; data_5b_false<= 1'b0; end
		6'b010010 : begin d5b_out <= 5'b00010; data_5b_false<= 1'b0; end
		6'b001010 : begin d5b_out <= 5'b00100; data_5b_false<= 1'b0; end
		6'b000111 : begin d5b_out <= 5'b00111; data_5b_false<= 1'b0; end
		6'b000110 : begin d5b_out <= 5'b01000; data_5b_false<= 1'b0; end
		6'b101000 : begin d5b_out <= 5'b01111; data_5b_false<= 1'b0; end
		//
		6'b100100 : begin d5b_out <= 5'b10000; data_5b_false<= 1'b0; end
		6'b000101 : begin d5b_out <= 5'b10111; data_5b_false<= 1'b0; end
		6'b110011 : begin d5b_out <= 5'b11000; data_5b_false<= 1'b0; end
		6'b001001 : begin d5b_out <= 5'b11011; data_5b_false<= 1'b0; end
		6'b010001 : begin d5b_out <= 5'b11101; data_5b_false<= 1'b0; end
		6'b100001 : begin d5b_out <= 5'b11110; data_5b_false<= 1'b0; end
		6'b010100 : begin d5b_out <= 5'b11111; data_5b_false<= 1'b0; end
		//
		default : begin d5b_out <= 5'b00000; data_5b_false<= 1'b1; end
		endcase
		end
end

always @(posedge clk) begin
	if(!rst) begin
		d3b_out <= 3'b000;
		data_3b_false<= 1'b0;
	end
	else if (data_valid) begin
    case({data_in[3:0]})
		4'b1011 : begin d3b_out <= 3'b000; data_3b_false<= 1'b0; end	
		4'b1001 : begin d3b_out <= 3'b001; data_3b_false<= 1'b0; end	
		4'b0101 : begin d3b_out <= 3'b010; data_3b_false<= 1'b0; end	
		4'b1100 : begin d3b_out <= 3'b011; data_3b_false<= 1'b0; end	
		4'b1101 : begin d3b_out <= 3'b100; data_3b_false<= 1'b0; end	
		4'b1010 : begin d3b_out <= 3'b101; data_3b_false<= 1'b0; end	
		4'b0110 : begin d3b_out <= 3'b110; data_3b_false<= 1'b0; end	
		4'b1110 : begin d3b_out <= 3'b111; data_3b_false<= 1'b0; end	
		4'b0111 : begin d3b_out <= 3'b111; data_3b_false<= 1'b0; end	
		//
		4'b0100 : begin d3b_out <= 3'b000; data_3b_false<= 1'b0; end	
		4'b0011 : begin d3b_out <= 3'b011; data_3b_false<= 1'b0; end	
		4'b0010 : begin d3b_out <= 3'b100; data_3b_false<= 1'b0; end	
		4'b0001 : begin d3b_out <= 3'b111; data_3b_false<= 1'b0; end	
		4'b1000 : begin d3b_out <= 3'b111; data_3b_false<= 1'b0; end	
		//
		default : begin d3b_out <= 5'b000; data_3b_false<= 1'b1; end
	endcase
	end
end
	
endmodule



module k_decoder_5x(rst,clk,data_in,data_out,data_valid,rk_false);
input rst;
input clk;
input data_valid;
input [9:0] data_in;
output [7:0] data_out;
output rk_false;
reg rk_false;
reg [7:0] data_out;


always @(posedge clk) begin
	if (!rst) begin
		data_out <= 8'b00000_000;
		rk_false <= 1'b0;
	end
	else if (data_valid)begin
		case(data_in)
		10'b001111_0100 : begin data_out <= 8'b000_11100; rk_false <= 1'b0;  end
		10'b001111_1001 : begin data_out <= 8'b001_11100; rk_false <= 1'b0;  end
		10'b001111_0101 : begin data_out <= 8'b010_11100; rk_false <= 1'b0;  end
		10'b001111_0011 : begin data_out <= 8'b011_11100; rk_false <= 1'b0;  end
		10'b001111_0010 : begin data_out <= 8'b100_11100; rk_false <= 1'b0;  end
		10'b001111_1010 : begin data_out <= 8'b101_11100; rk_false <= 1'b0;  end
		10'b001111_0110 : begin data_out <= 8'b110_11100; rk_false <= 1'b0;  end
		10'b001111_1000 : begin data_out <= 8'b111_11100; rk_false <= 1'b0;  end
		10'b111010_1000 : begin data_out <= 8'b111_10111; rk_false <= 1'b0;  end
		10'b110110_1000 : begin data_out <= 8'b111_11011; rk_false <= 1'b0;  end
		10'b101110_1000 : begin data_out <= 8'b111_11101; rk_false <= 1'b0;  end
		10'b011110_1000 : begin data_out <= 8'b111_11110; rk_false <= 1'b0;  end
//                                          
		10'b110000_1011 : begin data_out <= 8'b000_11100; rk_false <= 1'b0;  end
		10'b110000_0110 : begin data_out <= 8'b001_11100; rk_false <= 1'b0;  end
		10'b110000_1010 : begin data_out <= 8'b010_11100; rk_false <= 1'b0;  end
		10'b110000_1100 : begin data_out <= 8'b011_11100; rk_false <= 1'b0;  end
		10'b110000_1101 : begin data_out <= 8'b100_11100; rk_false <= 1'b0;  end
		10'b110000_0101 : begin data_out <= 8'b101_11100; rk_false <= 1'b0;  end
		10'b110000_1001 : begin data_out <= 8'b1101_1100; rk_false <= 1'b0;  end
		10'b110000_0111 : begin data_out <= 8'b111_11100; rk_false <= 1'b0;  end
		10'b000101_0111 : begin data_out <= 8'b111_10111; rk_false <= 1'b0;  end
		10'b001001_0111 : begin data_out <= 8'b111_11011; rk_false <= 1'b0;  end
		10'b010001_0111 : begin data_out <= 8'b111_11101; rk_false <= 1'b0;  end
		10'b100001_0111 : begin data_out <= 8'b111_11110; rk_false <= 1'b0;  end
//
      default : begin data_out <= 8'b00000_000;	 rk_false <= 1'b1;  end	
		endcase
		end
	end
			
endmodule
