----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    13:51:21 08/29/2023 
-- Design Name: 
-- Module Name:    FGM_ADDR - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity FGM_ADDR is
Port (
	clk			: in std_logic;
	reset 		: in std_logic;

	ms_edge     : in std_logic;
	fgm_chanel  : in std_logic_vector(7 downto 0);
    fgm_chanel_change  : in std_logic;	
	fgm_sample_state  : in std_logic;

    FGM_1      :out std_logic;
	FGM_2      :out std_logic;
	FGM_3      :out std_logic;
	FGM_4      :out std_logic;
	fgm_addr_out   :out std_logic_vector(7 downto 0)
);
end FGM_ADDR;

architecture Behavioral of FGM_ADDR is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal cnt: integer range 0 to 501;
signal fgm_reg:std_logic_vector(7 downto 0);
--signal fgm_set:std_logic;

type state_t is (start_s,wait_s,change_s);
signal state: state_t;


begin
  
	process(reset,clk)
	begin
		if reset='0' then
		    state<=start_s;	 cnt<=0;
		else 
		    if clk='1' and clk'event then 
			    case state is 
				    when start_s=>
					    if fgm_chanel_change='1' then 
				            cnt<=0;state<=wait_s;	
						end if;
				    when wait_s=>
					    if fgm_sample_state='1' then 
						    cnt<=1;state<=change_s;
						end if;
					when change_s=>
					    if ms_edge='1' then 
						    if cnt>0 and cnt<=200 then 
							    cnt<=cnt+1;
							else 
							    cnt<=0;state<=start_s;
							end if;
						end if;
					when others=>
					    state<=start_s;
				end case;
			end if;
		end if;
	end process;
		
	
    process(reset,clk)
	begin
		if reset='0' then
            fgm_reg<=x"00";
		else
		    if clk='1' and clk'event then
			    if fgm_chanel_change='1'then 
				   fgm_reg<=fgm_chanel;
				end if;
			end if;
		end if;
	end process;
	
    process(reset,clk)
	begin
		if reset='0' then
           FGM_1<='0';FGM_2<='0';FGM_3<='0';FGM_4<='0';fgm_addr_out<=x"01";
		else
		    if clk='1' and clk'event then				 
			    if cnt>=1 and cnt<=100 then 
			        if fgm_reg=x"01" then FGM_1<='1';fgm_addr_out<=x"01";
					elsif fgm_reg=x"02" then FGM_2<='1';fgm_addr_out<=x"02";
					elsif fgm_reg=x"03" then FGM_3<='1';fgm_addr_out<=x"03";
					elsif fgm_reg=x"04" then FGM_4<='1';fgm_addr_out<=x"04";
					else FGM_1<='0';FGM_2<='0';FGM_3<='0';FGM_4<='0';
					end if;
				else
				    FGM_1<='0';FGM_2<='0';FGM_3<='0';FGM_4<='0';
				end if;
			end if;
		end if;
	end process;
  	

end Behavioral;

