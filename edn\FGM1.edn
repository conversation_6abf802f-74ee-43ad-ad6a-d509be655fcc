(edif FGM1
  (edifVersion 2 0 0)
  (edifLevel 0)
  (keywordMap (keywordLevel 0))
  (external UNISIMS
    (edifLevel 0)
    (technology (numberDefinition))
    (cell VCC
      (cellType GENERIC)
        (view view_1
          (viewType NETLIST)
          (interface
            (port P
              (direction OUTPUT)
            )
          )
      )
    )
    (cell GND
      (cellType GENERIC)
        (view view_1
          (viewType NETLIST)
          (interface
            (port G
              (direction OUTPUT)
            )
          )
      )
    )
    (cell RAMB16
      (cellType GENERIC)
        (view view_1
          (viewType NETLIST)
          (interface
            (port CASCADEINA
              (direction INPUT)
            )
            (port CASCADEINB
              (direction INPUT)
            )
            (port CLKA
              (direction INPUT)
            )
            (port CLKB
              (direction INPUT)
            )
            (port ENA
              (direction INPUT)
            )
            (port REGCEA
              (direction INPUT)
            )
            (port REGCEB
              (direction INPUT)
            )
            (port ENB
              (direction INPUT)
            )
            (port SSRA
              (direction INPUT)
            )
            (port SSRB
              (direction INPUT)
            )
            (port CASCADE<PERSON>UTA
              (direction OUTPUT)
            )
            (port CA<PERSON>AD<PERSON>OUTB
              (direction OUTPUT)
            )
            (port (array (rename ADDRA "ADDRA<14:0>") 15)
              (direction INPUT))
            (port (array (rename ADDRB "ADDRB<14:0>") 15)
              (direction INPUT))
            (port (array (rename DIA "DIA<31:0>") 32)
              (direction INPUT))
            (port (array (rename DIB "DIB<31:0>") 32)
              (direction INPUT))
            (port (array (rename DIPA "DIPA<3:0>") 4)
              (direction INPUT))
            (port (array (rename DIPB "DIPB<3:0>") 4)
              (direction INPUT))
            (port (array (rename WEA "WEA<3:0>") 4)
              (direction INPUT))
            (port (array (rename WEB "WEB<3:0>") 4)
              (direction INPUT))
            (port (array (rename DOA "DOA<31:0>") 32)
              (direction OUTPUT))
            (port (array (rename DOB "DOB<31:0>") 32)
              (direction OUTPUT))
            (port (array (rename DOPA "DOPA<3:0>") 4)
              (direction OUTPUT))
            (port (array (rename DOPB "DOPB<3:0>") 4)
              (direction OUTPUT))
          )
      )
    )
  )

  (library FGM1_lib
    (edifLevel 0)
    (technology (numberDefinition))
    (cell FGM1
      (cellType GENERIC)
        (view view_1
          (viewType NETLIST)
          (interface
            (port clka
              (direction INPUT)
            )
            (port clkb
              (direction INPUT)
            )
            (port (array (rename wea "wea<0:0>") 1)
              (direction INPUT))
            (port (array (rename addra "addra<9:0>") 10)
              (direction INPUT))
            (port (array (rename dina "dina<7:0>") 8)
              (direction INPUT))
            (port (array (rename addrb "addrb<9:0>") 10)
              (direction INPUT))
            (port (array (rename doutb "doutb<7:0>") 8)
              (direction OUTPUT))
            (designator "4vsx55ff1148-10")
            (property TYPE (string "FGM1") (owner "Xilinx"))
            (property BUS_INFO (string "1:INPUT:wea<0:0>") (owner "Xilinx"))
            (property BUS_INFO (string "10:INPUT:addra<9:0>") (owner "Xilinx"))
            (property BUS_INFO (string "8:INPUT:dina<7:0>") (owner "Xilinx"))
            (property BUS_INFO (string "10:INPUT:addrb<9:0>") (owner "Xilinx"))
            (property BUS_INFO (string "8:OUTPUT:doutb<7:0>") (owner "Xilinx"))
            (property X_CORE_INFO (string "blk_mem_gen_v7_3, Xilinx CORE Generator 14.7") (owner "Xilinx"))
            (property CHECK_LICENSE_TYPE (string "FGM1,blk_mem_gen_v7_3,{}") (owner "Xilinx"))
            (property CORE_GENERATION_INFO (string "FGM1,blk_mem_gen_v7_3,{c_addra_width=10,c_addrb_width=10,c_algorithm=1,c_axi_id_width=4,c_axi_slave_type=0,c_axi_type=1,c_byte_size=9,c_common_clk=1,c_default_data=0,c_disable_warn_bhv_coll=0,c_disable_warn_bhv_range=0,c_elaboration_dir=C_/E/C7/CODE/D_C7_FPGA1_1114/IP/tmp/_cg/,c_enable_32bit_address=0,c_family=virtex4,c_has_axi_id=0,c_has_ena=0,c_has_enb=0,c_has_injecterr=0,c_has_mem_output_regs_a=0,c_has_mem_output_regs_b=0,c_has_mux_output_regs_a=0,c_has_mux_output_regs_b=0,c_has_regcea=0,c_has_regceb=0,c_has_rsta=0,c_has_rstb=0,c_has_softecc_input_regs_a=0,c_has_softecc_output_regs_b=0,c_init_file=BlankString,c_init_file_name=no_coe_file_loaded,c_inita_val=0,c_initb_val=0,c_interface_type=0,c_load_init_file=0,c_mem_type=1,c_mux_pipeline_stages=0,c_prim_type=1,c_read_depth_a=1024,c_read_depth_b=1024,c_read_width_a=8,c_read_width_b=8,c_rst_priority_a=CE,c_rst_priority_b=CE,c_rst_type=SYNC,c_rstram_a=0,c_rstram_b=0,c_sim_collision_check=ALL,c_use_bram_block=0,c_use_byte_wea=0,c_use_byte_web=0,c_use_default_data=0,c_use_ecc=0,c_use_softecc=0,c_wea_width=1,c_web_width=1,c_write_depth_a=1024,c_write_depth_b=1024,c_write_mode_a=WRITE_FIRST,c_write_mode_b=WRITE_FIRST,c_write_width_a=8,c_write_width_b=8,c_xdevicefamily=virtex4}") (owner "Xilinx"))
            (property SHREG_MIN_SIZE (string "-1") (owner "Xilinx"))
            (property SHREG_EXTRACT_NGC (string "Yes") (owner "Xilinx"))
            (property NLW_UNIQUE_ID (integer 0) (owner "Xilinx"))
            (property NLW_MACRO_TAG (integer 0) (owner "Xilinx"))
            (property NLW_MACRO_ALIAS (string "FGM1_FGM1") (owner "Xilinx"))
          )
          (contents
            (instance XST_VCC
              (viewRef view_1 (cellRef VCC (libraryRef UNISIMS)))
              (property XSTLIB (boolean (true)) (owner "Xilinx"))
            )
            (instance XST_GND
              (viewRef view_1 (cellRef GND (libraryRef UNISIMS)))
              (property XSTLIB (boolean (true)) (owner "Xilinx"))
            )
            (instance (rename U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP "U0/xst_blk_mem_generator/gnativebmg.native_blk_mem_gen/valid.cstr/ramloop[0].ram.r/v4_noinit.ram/SDP.SINGLE_PRIM.SDP")
              (viewRef view_1 (cellRef RAMB16 (libraryRef UNISIMS)))
              (property XSTLIB (boolean (true)) (owner "Xilinx"))
              (property BUS_INFO (string "15:INPUT:ADDRA<14:0>") (owner "Xilinx"))
              (property BUS_INFO (string "15:INPUT:ADDRB<14:0>") (owner "Xilinx"))
              (property BUS_INFO (string "4:INPUT:DIPA<3:0>") (owner "Xilinx"))
              (property BUS_INFO (string "4:INPUT:DIPB<3:0>") (owner "Xilinx"))
              (property BUS_INFO (string "4:OUTPUT:DOPA<3:0>") (owner "Xilinx"))
              (property BUS_INFO (string "4:OUTPUT:DOPB<3:0>") (owner "Xilinx"))
              (property BUS_INFO (string "32:INPUT:DIA<31:0>") (owner "Xilinx"))
              (property BUS_INFO (string "32:INPUT:DIB<31:0>") (owner "Xilinx"))
              (property BUS_INFO (string "4:INPUT:WEA<3:0>") (owner "Xilinx"))
              (property BUS_INFO (string "4:INPUT:WEB<3:0>") (owner "Xilinx"))
              (property BUS_INFO (string "32:OUTPUT:DOA<31:0>") (owner "Xilinx"))
              (property BUS_INFO (string "32:OUTPUT:DOB<31:0>") (owner "Xilinx"))
              (property OPTIMIZE_PRIMITIVES_NGC (string "no") (owner "Xilinx"))
              (property DOA_REG (integer 0) (owner "Xilinx"))
              (property DOB_REG (integer 0) (owner "Xilinx"))
              (property INITP_00 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INITP_01 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INITP_02 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INITP_03 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INITP_04 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INITP_05 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INITP_06 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INITP_07 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_00 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_01 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_02 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_03 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_04 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_05 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_06 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_07 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_08 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_09 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_0A (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_0B (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_0C (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_0D (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_0E (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_0F (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_10 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_11 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_12 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_13 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_14 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_15 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_16 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_17 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_18 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_19 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_1A (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_1B (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_1C (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_1D (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_1E (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_1F (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_20 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_21 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_22 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_23 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_24 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_25 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_26 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_27 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_28 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_29 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_2A (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_2B (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_2C (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_2D (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_2E (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_2F (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_30 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_31 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_32 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_33 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_34 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_35 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_36 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_37 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_38 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_39 (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_3A (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_3B (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_3C (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_3D (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_3E (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_3F (string "0000000000000000000000000000000000000000000000000000000000000000") (owner "Xilinx"))
              (property INIT_A (string "000000000") (owner "Xilinx"))
              (property INIT_B (string "000000000") (owner "Xilinx"))
              (property INIT_FILE (string "NONE") (owner "Xilinx"))
              (property INVERT_CLK_DOA_REG (string "FALSE") (owner "Xilinx"))
              (property INVERT_CLK_DOB_REG (string "FALSE") (owner "Xilinx"))
              (property RAM_EXTENSION_A (string "NONE") (owner "Xilinx"))
              (property RAM_EXTENSION_B (string "NONE") (owner "Xilinx"))
              (property READ_WIDTH_A (integer 18) (owner "Xilinx"))
              (property READ_WIDTH_B (integer 18) (owner "Xilinx"))
              (property SIM_COLLISION_CHECK (string "ALL") (owner "Xilinx"))
              (property SRVAL_A (string "000000000") (owner "Xilinx"))
              (property SRVAL_B (string "000000000") (owner "Xilinx"))
              (property WRITE_MODE_A (string "WRITE_FIRST") (owner "Xilinx"))
              (property WRITE_MODE_B (string "WRITE_FIRST") (owner "Xilinx"))
              (property WRITE_WIDTH_A (integer 18) (owner "Xilinx"))
              (property WRITE_WIDTH_B (integer 18) (owner "Xilinx"))
              (property SAVEDATA (string "FALSE") (owner "Xilinx"))
            )
            (net N0
              (joined
                (portRef P (instanceRef XST_VCC))
                (portRef ENA
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef ENB
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net N1
              (joined
                (portRef G (instanceRef XST_GND))
                (portRef (member ADDRA 0) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member ADDRA 11) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member ADDRA 12) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member ADDRA 13) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member ADDRA 14) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member ADDRB 0) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member ADDRB 11) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member ADDRB 12) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member ADDRB 13) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member ADDRB 14) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 0) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 1) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 2) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 3) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 4) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 5) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 6) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 7) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 8) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 9) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 10) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 11) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 12) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 13) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 14) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 15) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 16) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 17) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 18) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 19) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 24) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 25) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 26) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIA 27) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 0) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 1) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 2) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 3) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 4) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 5) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 6) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 7) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 8) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 9) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 10) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 11) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 12) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 13) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 14) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 15) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 16) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 17) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 18) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 19) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 20) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 21) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 22) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 23) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 24) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 25) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 26) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 27) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 28) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 29) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 30) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIB 31) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIPA 0) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIPA 1) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIPA 2) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIPA 3) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIPB 0) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIPB 1) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIPB 2) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member DIPB 3) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member WEB 0) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member WEB 1) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member WEB 2) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member WEB 3) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef CASCADEINA
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef CASCADEINB
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef REGCEA
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef REGCEB
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef SSRA
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef SSRB
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename doutb_0_ "doutb<0>")
              (joined
                (portRef (member doutb 7))
                (portRef (member DOB 31) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename doutb_1_ "doutb<1>")
              (joined
                (portRef (member doutb 6))
                (portRef (member DOB 30) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename doutb_2_ "doutb<2>")
              (joined
                (portRef (member doutb 5))
                (portRef (member DOB 29) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename doutb_3_ "doutb<3>")
              (joined
                (portRef (member doutb 4))
                (portRef (member DOB 28) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename doutb_4_ "doutb<4>")
              (joined
                (portRef (member doutb 3))
                (portRef (member DOB 23) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename doutb_5_ "doutb<5>")
              (joined
                (portRef (member doutb 2))
                (portRef (member DOB 22) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename doutb_6_ "doutb<6>")
              (joined
                (portRef (member doutb 1))
                (portRef (member DOB 21) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename doutb_7_ "doutb<7>")
              (joined
                (portRef (member doutb 0))
                (portRef (member DOB 20) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net clkb
              (joined
                (portRef clkb)
                (portRef CLKB
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net clka
              (joined
                (portRef clka)
                (portRef CLKA
 (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_0_ "addrb<0>")
              (joined
                (portRef (member addrb 9))
                (portRef (member ADDRB 10) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_1_ "addrb<1>")
              (joined
                (portRef (member addrb 8))
                (portRef (member ADDRB 9) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_2_ "addrb<2>")
              (joined
                (portRef (member addrb 7))
                (portRef (member ADDRB 8) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_3_ "addrb<3>")
              (joined
                (portRef (member addrb 6))
                (portRef (member ADDRB 7) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_4_ "addrb<4>")
              (joined
                (portRef (member addrb 5))
                (portRef (member ADDRB 6) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_5_ "addrb<5>")
              (joined
                (portRef (member addrb 4))
                (portRef (member ADDRB 5) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_6_ "addrb<6>")
              (joined
                (portRef (member addrb 3))
                (portRef (member ADDRB 4) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_7_ "addrb<7>")
              (joined
                (portRef (member addrb 2))
                (portRef (member ADDRB 3) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_8_ "addrb<8>")
              (joined
                (portRef (member addrb 1))
                (portRef (member ADDRB 2) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addrb_9_ "addrb<9>")
              (joined
                (portRef (member addrb 0))
                (portRef (member ADDRB 1) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename dina_0_ "dina<0>")
              (joined
                (portRef (member dina 7))
                (portRef (member DIA 31) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename dina_1_ "dina<1>")
              (joined
                (portRef (member dina 6))
                (portRef (member DIA 30) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename dina_2_ "dina<2>")
              (joined
                (portRef (member dina 5))
                (portRef (member DIA 29) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename dina_3_ "dina<3>")
              (joined
                (portRef (member dina 4))
                (portRef (member DIA 28) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename dina_4_ "dina<4>")
              (joined
                (portRef (member dina 3))
                (portRef (member DIA 23) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename dina_5_ "dina<5>")
              (joined
                (portRef (member dina 2))
                (portRef (member DIA 22) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename dina_6_ "dina<6>")
              (joined
                (portRef (member dina 1))
                (portRef (member DIA 21) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename dina_7_ "dina<7>")
              (joined
                (portRef (member dina 0))
                (portRef (member DIA 20) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_0_ "addra<0>")
              (joined
                (portRef (member addra 9))
                (portRef (member ADDRA 10) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_1_ "addra<1>")
              (joined
                (portRef (member addra 8))
                (portRef (member ADDRA 9) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_2_ "addra<2>")
              (joined
                (portRef (member addra 7))
                (portRef (member ADDRA 8) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_3_ "addra<3>")
              (joined
                (portRef (member addra 6))
                (portRef (member ADDRA 7) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_4_ "addra<4>")
              (joined
                (portRef (member addra 5))
                (portRef (member ADDRA 6) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_5_ "addra<5>")
              (joined
                (portRef (member addra 4))
                (portRef (member ADDRA 5) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_6_ "addra<6>")
              (joined
                (portRef (member addra 3))
                (portRef (member ADDRA 4) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_7_ "addra<7>")
              (joined
                (portRef (member addra 2))
                (portRef (member ADDRA 3) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_8_ "addra<8>")
              (joined
                (portRef (member addra 1))
                (portRef (member ADDRA 2) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename addra_9_ "addra<9>")
              (joined
                (portRef (member addra 0))
                (portRef (member ADDRA 1) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
            (net (rename wea_0_ "wea<0>")
              (joined
                (portRef (member wea 0))
                (portRef (member WEA 0) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member WEA 1) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member WEA 2) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
                (portRef (member WEA 3) (instanceRef U0_xst_blk_mem_generator_gnativebmg_native_blk_mem_gen_valid_cstr_ramloop_0__ram_r_v4_noinit_ram_SDP_SINGLE_PRIM_SDP))
              )
            )
          )
      )
    )
  )

  (design FGM1
    (cellRef FGM1
      (libraryRef FGM1_lib)
    )
    (property PART (string "4vsx55ff1148-10") (owner "Xilinx"))
  )
)

