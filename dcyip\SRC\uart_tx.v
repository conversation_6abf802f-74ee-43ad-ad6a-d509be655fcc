//*************************************************************************************
// Company:                                                                            
// Engineer:     LSL                                                           
// Create Date:  2016-11-29                                                          
// Design Name:                                                                        
// Module Name:   uart_tx                                                               
// Project Name:                                                                       
// Target Devices:                                                                 
// Description:    txd data Transfer                                       
//************************************************************************************/

`timescale 1 ns / 1 ns
(*keep_hierarchy="no"*)module uart_tx 
(
  input  wire         clk       ,
//  input  wire         clk_us_edge,
  input  wire         rst       ,
  input  wire         start     ,
  input  wire [07:0]  data_i    ,
  output reg          txd       ,
  output reg          over      ,
  input  wire         tx_state  ,
  output reg          tx_on     ,
  output reg          rx_on     
);



/* localparam integer baud_cntr_max = clock_frequency/baud_rate - 1; */
localparam integer baud_cntr_max = 276;
reg  [11:0] baud_cntr;
reg  baud_cntr_enable;
wire baud_tick;
reg  [07:0] data;
reg  parity;
//reg  [07:0] clk_cnt;

reg  [12:0] state;
localparam s_idle   = 13'b0_0000_0000_0001;
localparam s_start  = 13'b0_0000_0000_0010;
localparam s_bit0   = 13'b0_0000_0000_0100;
localparam s_bit1   = 13'b0_0000_0000_1000;
localparam s_bit2   = 13'b0_0000_0001_0000;
localparam s_bit3   = 13'b0_0000_0010_0000;
localparam s_bit4   = 13'b0_0000_0100_0000;
localparam s_bit5   = 13'b0_0000_1000_0000;
localparam s_bit6   = 13'b0_0001_0000_0000;
localparam s_bit7   = 13'b0_0010_0000_0000;
localparam s_parity = 13'b0_0100_0000_0000;
localparam s_stop1  = 13'b0_1000_0000_0000;
localparam s_stop2  = 13'b1_0000_0000_0000;

/* localparam pb_odd = 1'b1;
localparam pb_even = 1'b0; */
localparam parity_base = 1'b1;

//baud_tick
assign baud_tick = (baud_cntr==baud_cntr_max);

//state
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		state <= s_idle;
	else case(state)
		s_idle : if(start) 	  state <= s_start;
		s_start: if(baud_tick) state <= s_bit0;
		s_bit0 : if(baud_tick) state <= s_bit1;
		s_bit1 : if(baud_tick) state <= s_bit2;
		s_bit2 : if(baud_tick) state <= s_bit3;
		s_bit3 : if(baud_tick) state <= s_bit4;
		s_bit4 : if(baud_tick) state <= s_bit5;
		s_bit5 : if(baud_tick) state <= s_bit6;
		s_bit6 : if(baud_tick) state <= s_bit7;
		s_bit7 : if(baud_tick) state <= s_parity;					
		s_parity:if(baud_tick) state <= s_stop1;//校验
		s_stop1:if(baud_tick) state <= s_stop2;//校验
		s_stop2 : if(baud_tick) state <= s_idle;
		default: state <= s_idle;
	endcase
end

//baud_cntr_enable
always @(posedge clk or negedge rst)
begin
    if(rst==0)
		baud_cntr_enable <= 1'b0;
	else case(state)
		s_idle : baud_cntr_enable <= 1'b0;
		default: baud_cntr_enable <= 1'b1;
	endcase	
end

//baud_cntr
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		baud_cntr <= 0;
	else if(baud_cntr_enable)
	begin
		if(baud_tick)
			baud_cntr <= 0;
		else
			baud_cntr <= baud_cntr + 12'h1;
	end
end

//over
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		over <= 0;
	else case(state)
		s_stop2 : if(baud_tick) over <= 1;
		default: over <= 0;
	endcase
end

//data
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		data <= 0;
	else case(state)
		s_idle : if(start) data <= data_i;
		s_bit0,s_bit1,
		s_bit2,s_bit3,
		s_bit4,s_bit5,
		s_bit6,s_bit7: if(baud_tick) data <= {1'b0,data[7:1]};
		default: begin data<=data;end
	endcase
end

//parity
always @(posedge clk or negedge rst)
begin
	if(rst==0)
		parity <= parity_base;
	else case(state)
		s_idle : parity <= parity_base;
		s_bit0,s_bit1,
		s_bit2,s_bit3,
		s_bit4,s_bit5,
		s_bit6,s_bit7: if(baud_tick) parity <= parity + data[0];
		default: begin parity<= parity;  end
	endcase
end

//txd
always @(posedge clk or negedge rst)
begin
    if(rst==0)
	   txd <= 1'b1;
	else if(tx_state==0)
	    txd <= 1'b1;
	else case(state)
		s_start : txd <= 1'b0;
		s_bit0,s_bit1,
		s_bit2,s_bit3,
		s_bit4,s_bit5,
		s_bit6,s_bit7: txd <= data[0];
		s_parity: txd <= parity;
		s_stop1  : txd <= 1'b1;
		s_stop2  : txd <= 1'b1;
		s_idle  : txd <= 1'b1;
		default : txd <= 1'b1;
	endcase
end

//低有效
always @(posedge clk or negedge rst)
begin
	if(rst==0)
	    begin
		tx_on <= 0;
		rx_on <= 0;
		end
	else if(tx_state ==1)
        begin
		tx_on <= 0;
		rx_on <= 1;
	    end 
	else
	    begin
	    tx_on <= 1;
		rx_on <= 0;
	    end 
end

/* always @(posedge clk or negedge rst)
begin
	if(rst==0)
		clk_cnt <= 0;
	else
	begin 
	    if (tx_state ==0)
		    clk_cnt<=0;
		else 
		begin
		    if(clk_cnt>20)
		       clk_cnt<=clk_cnt;
			else if(clk_us_edge ==1)
			   clk_cnt<=clk_cnt+1;
			else
			   clk_cnt<=clk_cnt;
		end 
	end 
end  */
			
		
      


endmodule
