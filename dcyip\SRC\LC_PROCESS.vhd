----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    14:57:53 11/14/2022 
-- Design Name: 
-- Module Name:    LC_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity LC_PROCESS is
Port (
	clk			: in std_logic;
	reset 		: in std_logic;	
	init_lc     : in std_logic;
	
    lc_state      : in std_logic;

	off_cmd     : in std_logic;
	ms_edge     : in std_logic;
		

	
	LC_RST1    : out std_logic; --地面
	LC_EN1     : out std_logic  --在轨
	

);	
end LC_PROCESS;

architecture Behavioral of LC_PROCESS is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal init_lc_reg:std_logic;
signal init_lc_edge:std_logic;--在轨

signal lc1_reg:std_logic;
signal lc_run_edge:std_logic;--在轨
signal lc_ground_edge:std_logic;--地面
--signal init_pro:std_logic;
signal cnt1: integer range 0 to 201;
signal cnt2: integer range 0 to 201;
begin

--量程初始化
process(reset,clk)
	begin
		if reset='0' then
            init_lc_reg<='0';			
		else
		    if clk='1' and clk'event then
			    init_lc_reg<=init_lc;
			end if;
		end if;
	end process;

process(reset,clk)
	begin
		if reset='0' then
            init_lc_edge<='0';			
		else
		    if clk='1' and clk'event then
			    if init_lc='1' and init_lc_reg='0' then 
				    init_lc_edge<='1';
				else 
				    init_lc_edge<='0';
				end if;
			end if;
		end if;
	end process;

--指令控制
process(reset,clk)
	begin
		if reset='0' then
            lc1_reg<='0';			
		else
		    if clk='1' and clk'event then
			    lc1_reg<=lc_state;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
            lc_run_edge<='0';	lc_ground_edge<='0';		
		else
		    if clk='1' and clk'event then
			    if lc_state='1' and lc1_reg='0' then
				    lc_ground_edge<='1';lc_run_edge<='0';
				elsif lc_state='0' and lc1_reg='1' then
				    lc_run_edge<='1';lc_ground_edge<='0';
				else
				   lc_run_edge<='0';lc_ground_edge<='0';	
				end if;
			end if;
		end if;
	end process;
	
--在轨量程继电器控制		
	process(reset,clk)
	begin
		if reset='0' then
            cnt1<=0;
		else
		    if clk='1' and clk'event then
			    if lc_run_edge='1' or off_cmd='1' or init_lc_edge='1' then 
				    cnt1<=1;
			    else
				    if ms_edge='1' then 
					    if cnt1>=1 and cnt1<=200 then
						    cnt1<=cnt1+1;				         
					    else
				            cnt1<=0;
					    end if;
					end if;
				end if;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
            LC_EN1<='0';    
		else
		    if clk='1' and clk'event then
				if cnt1>=1 and cnt1<=100 then  LC_EN1<='1';
				else  LC_EN1<='0';
				end if;
			end if;
		end if;
	end process;

--地面量程继电器控制	
	process(reset,clk)
	begin
		if reset='0' then
            cnt2<=0;
		else
		    if clk='1' and clk'event then
			    if lc_ground_edge='1' then 
				    cnt2<=1;
			    else
				    if ms_edge='1' then 
				        if cnt2>=1 and cnt2<=200 then 
					         cnt2<=cnt2+1;							
					    else
				            cnt2<=0;
					    end if;
					end if;
				end if;
			end if;
		end if;
	end process;		
					
    process(reset,clk)
	begin
		if reset='0' then
            LC_RST1<='0';
		else
		    if clk='1' and clk'event then
			    if cnt2>=1 and cnt2<=100 then  LC_RST1<='1';
				else   LC_RST1<='0'; 
				end if;
			end if;
		end if;
	end process;	
  	
	
end Behavioral;

