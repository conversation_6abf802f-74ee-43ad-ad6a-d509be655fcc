----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    13:55:37 11/13/2022 
-- Design Name: 
-- Module Name:    DA_CHANGE - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity DA_CHANGE_MODE is
Port ( 	
			clk			: in std_logic;
			reset 		: in std_logic;
			da_data_in  : in std_logic_vector(47 downto 0);
			da_change   : in std_logic;
			s_edge      : in std_logic;
			init_da     : in std_logic;
			mode        : in std_logic;
			
			da_scan_step1  : out std_logic_vector(11 downto 0);
			da_scan_time1  : out std_logic_vector(3 downto 0);
			da_scan_hdata1 : out std_logic_vector(11 downto 0);
			da_scan_ldata1 : out std_logic_vector(11 downto 0);	
							
			da_scan_step2  : out std_logic_vector(11 downto 0);
			da_scan_time2  : out std_logic_vector(3 downto 0);
			da_scan_hdata2 : out std_logic_vector(11 downto 0);
			da_scan_ldata2 : out std_logic_vector(11 downto 0);	
							
			da_scan_step3  : out std_logic_vector(11 downto 0);
			da_scan_time3  : out std_logic_vector(3 downto 0);
			da_scan_hdata3 : out std_logic_vector(11 downto 0);
			da_scan_ldata3 : out std_logic_vector(11 downto 0);
							
			da_scan_step4  : out std_logic_vector(11 downto 0);
			da_scan_time4  : out std_logic_vector(3 downto 0);
			da_scan_hdata4 : out std_logic_vector(11 downto 0);
			da_scan_ldata4 : out std_logic_vector(11 downto 0);	

            da_num  :out std_logic_vector(7 downto 0);
			da_data :out std_logic_vector(15 downto 0);
			da_set   :out std_logic
			
	);


end DA_CHANGE_MODE;

architecture Behavioral of DA_CHANGE_MODE is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

--signal da_mode:std_logic_vector(7 downto 0);
--signal da_scan_mode_set:std_logic_vector(7 downto 0);
signal da_scan_num_set:std_logic_vector(7 downto 0);
signal da_scan_step_set  :  std_logic_vector(3 downto 0);
signal da_scan_time_set  :  std_logic_vector(3 downto 0);
signal da_scan_hdata_set :  std_logic_vector(11 downto 0);
signal da_scan_ldata_set :  std_logic_vector(11 downto 0);

signal init_da_reg:std_logic;
signal init_da_edge:std_logic;
signal cnt:integer range 0 to 3;
signal da_scan_set:std_logic;
--signal mode_change:std_logic;
--signal mode_reg:std_logic;

--初始化2s后接收指令
begin
	process(reset,clk)
	begin
		if reset ='0' then
			da_num <=(others=>'0');
			da_data <=(others=>'0');
            da_set  <='0';		
           
		   da_scan_num_set  <=(others=>'0');
		   da_scan_step_set <=(others=>'0');
		   da_scan_time_set <=(others=>'0');
		   da_scan_hdata_set<=(others=>'0');
		   da_scan_ldata_set<=(others=>'0');
	       da_scan_set      <='0';
		else
			if clk='1' and clk'event then
--初始化
				if  s_edge='1' and cnt=1 then 
					da_num  <=x"05";
				    da_data <=x"D002";	
                    da_set  <='1';
				elsif s_edge='1' and cnt=2 then 
					da_num  <=x"05";
				    da_data <=x"CB00";	
                    da_set  <='1';	
--指令控制					
				else
				    --退出扫描模式
				    -- if mode_change='1' then 
					    -- da_num  <=x"05";
                    -- --    da_data <=x"CB00";	
						-- da_set  <='1';
					-- els
					if  da_change='1' and da_data_in(47 downto 40)=x"CC" then
--				        da_mode <=da_data_in(47 downto 40);
--				        da_num  <=da_data_in(23 downto 16);
				        --da_data <=da_data_in(15 downto 0);	
                        da_data(15 downto 12) <=x"C";
						da_data(11 downto 0)<=da_data_in(11 downto 0);
						da_set  <='1';
--退出扫描模式，一起退出
						if mode='1' then da_num  <=x"05"; 
						else  da_num  <=da_data_in(23 downto 16);
						end if;
					elsif da_change='1' and da_data_in(47 downto 40)=x"DD" then
						da_scan_num_set   <=da_data_in(39 downto 32);
						da_scan_step_set  <=da_data_in(31 downto 28);
						--da_scan_time_set  <=da_data_in(27 downto 24);
						if da_data_in(27 downto 24)=x"0" then
							da_scan_time_set<=x"0";
						else
							da_scan_time_set  <=da_data_in(27 downto 24)-x"1";
						end if;
						da_scan_hdata_set <=da_data_in(23 downto 12);
						da_scan_ldata_set <=da_data_in(11 downto 0);
						da_scan_set<='1';
                    else
                        da_scan_set<='0';da_set  <='0';					
					end if;						
				end if;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
            init_da_reg<='0';			
		else
		    if clk='1' and clk'event then
			    init_da_reg<=init_da;
			end if;
		end if;
	end process;

process(reset,clk)
	begin
		if reset='0' then
            init_da_edge<='0';			
		else
		    if clk='1' and clk'event then
			    if init_da='1' and init_da_reg='0' then 
				    init_da_edge<='1';
				else 
				    init_da_edge<='0';
				end if;
			end if;
		end if;
	end process;
	
	process(clk,reset)
	begin
		if reset='0' then
			cnt <=0;
		else
			if clk'event and clk='1' then
			    if init_da_edge='1' then
                    cnt<=1;
                else 
                    if s_edge='1' then 
				        if cnt>0 then
                            if cnt>=3 then 						
				    	       cnt<=0;
				    	    else 
				               cnt<=cnt+1;
				    	    end if;
						end if;
				    end if;	
                end if;					
			end if;
		end if;
	end process;
	
	-- process(clk,reset)
	-- begin
		-- if reset='0' then
			-- mode_reg <='0';
		-- else 
		    -- if clk'event and clk='1' then
			    -- mode_reg<=mode;
			-- end if;
		-- end if;
	-- end process;
		
	
	-- process(clk,reset)
	-- begin
		-- if reset='0' then
			-- mode_change <='0';
		-- else
			-- if clk'event and clk='1' then
                -- if mode='0' and mode_reg='1' then 
                    -- mode_change <='1';
			    -- else 
				    -- mode_change <='0';
				-- end if;				
			-- end if;
		-- end if;
	-- end process; 
	
--扫描模式
	process(clk,reset)
	begin
		if reset='0' then
			da_scan_step1  <= x"00A"; da_scan_time1  <= X"0"; da_scan_hdata1 <= X"C00"; da_scan_ldata1 <= X"400" ;
			da_scan_step2  <= x"00A"; da_scan_time2  <= X"0"; da_scan_hdata2 <= X"C00"; da_scan_ldata2 <= X"400" ;
			da_scan_step3  <= x"00A"; da_scan_time3  <= X"0"; da_scan_hdata3 <= X"C00"; da_scan_ldata3 <= X"400" ;
			da_scan_step4  <= x"00A"; da_scan_time4  <= X"0"; da_scan_hdata4 <= X"C00"; da_scan_ldata4 <= X"400" ;
		else
			if clk'event and clk='1' then
				if da_scan_set='1' then 
				    if  da_scan_num_set=x"01" then 
					    da_scan_step1(3 downto 0)  <= da_scan_step_set; 
						da_scan_time1  <= da_scan_time_set  ;
					    da_scan_hdata1 <= da_scan_hdata_set ;
					    da_scan_ldata1 <= da_scan_ldata_set ;
				    elsif  da_scan_num_set=x"02" then 
					   	da_scan_step2(3 downto 0)  <= da_scan_step_set  ;
					    da_scan_time2  <= da_scan_time_set  ;
					    da_scan_hdata2 <= da_scan_hdata_set ;
					    da_scan_ldata2 <= da_scan_ldata_set ;
					elsif  da_scan_num_set=x"03" then 
					   	da_scan_step3 (3 downto 0) <= da_scan_step_set  ;
					    da_scan_time3  <= da_scan_time_set  ;
					    da_scan_hdata3 <= da_scan_hdata_set ;
					    da_scan_ldata3 <= da_scan_ldata_set ;
					elsif  da_scan_num_set=x"04" then 
					   	da_scan_step4(3 downto 0)  <= da_scan_step_set  ;
					    da_scan_time4  <= da_scan_time_set  ;
					    da_scan_hdata4 <= da_scan_hdata_set ;
					    da_scan_ldata4 <= da_scan_ldata_set ;
					else 
					    if da_scan_num_set=x"05" then 
					    da_scan_step1 (3 downto 0) <= da_scan_step_set  ;
					    da_scan_time1  <= da_scan_time_set  ;
					    da_scan_hdata1 <= da_scan_hdata_set ;
					    da_scan_ldata1 <= da_scan_ldata_set ;
						da_scan_step2 (3 downto 0) <= da_scan_step_set  ;
					    da_scan_time2  <= da_scan_time_set  ;
					    da_scan_hdata2 <= da_scan_hdata_set ;
					    da_scan_ldata2 <= da_scan_ldata_set ;
						da_scan_step3  (3 downto 0)<= da_scan_step_set  ;
					    da_scan_time3  <= da_scan_time_set  ;
					    da_scan_hdata3 <= da_scan_hdata_set ;
					    da_scan_ldata3 <= da_scan_ldata_set ;
					   	da_scan_step4 (3 downto 0) <= da_scan_step_set  ;
					    da_scan_time4  <= da_scan_time_set  ;
					    da_scan_hdata4 <= da_scan_hdata_set ;
					    da_scan_ldata4 <= da_scan_ldata_set ;end if;
					end if;									
				end if;
			end if;
		end if;
	end process;
	
		
	
	
	

end Behavioral;

