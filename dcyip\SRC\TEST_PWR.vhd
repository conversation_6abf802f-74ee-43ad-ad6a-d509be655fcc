----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    23:08:11 11/20/2022 
-- Design Name: 
-- Module Name:    TEST_PWR - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity TEST_PWR is
port(
    reset: 		in std_logic;
    clk : 		in std_logic; 
--	s_edge:     in std_logic;
	PW_EN :     out std_logic;
	EN1   :     out std_logic;
    EN2   :     out std_logic
);
end TEST_PWR;

architecture Behavioral of TEST_PWR is
signal cnt: integer range 0 to 90000;
signal clk_cnt: integer range 0 to 32000;


begin
EN1<='0';
EN2<='0';
-- process(reset,clk)
	-- begin
		-- if reset='0' then
		    -- PW_EN<='0';
		-- else 
		    -- if clk='1' and clk'event then
			    -- if clk_cnt=31999 then 
				    -- if cnt=5000 then
					    -- cnt<=5000;PW_EN<='1';
					-- else 
				        -- cnt<=cnt+1;
					-- end if;
				-- end if;
			-- end if;
		-- end if;
	-- end process;
		
-- process(clk,reset)
    -- begin
        -- if clk'event and clk='1' then
			-- if clk_cnt >=31999 then --1ms
			    -- clk_cnt<=0;
			-- else
			    -- clk_cnt<=clk_cnt+1;
		    -- end if;
		-- end if;
	-- end process;
	
	process(reset,clk)
	begin
		if reset='0' then
--		    PW_EN<='0';
			cnt<=0;
		else 
		    if clk='1' and clk'event then
			    if clk_cnt=31 then 
				    if cnt=90000 then
					    cnt<=90000;
					--	PW_EN<='1';
					else 
				        cnt<=cnt+1;
					end if;
				end if;
			end if;
		end if;
	end process;
		
process(clk,reset)
    begin
        if clk'event and clk='1' then
			if clk_cnt >=31999 then --1ms
			    clk_cnt<=0;
			else
			    clk_cnt<=clk_cnt+1;
		    end if;
		end if;
	end process;
	
	
--实际	
	-- process(clk,reset)
    -- begin
	    -- if reset='0' then
		   -- PW_EN<='0';
        -- else
		    -- if clk'event and clk='1' then
			    -- if clk_cnt =31999 and  cnt=10000 then --10s
			        -- PW_EN<='1';
			    -- elsif clk_cnt =31999 and  cnt=60000 then --60s
			         -- PW_EN<='0';
			    -- else 
				    -- if clk_cnt =31999 and  cnt=70000 then --70s
			           -- PW_EN<='1';
					-- end if;
		        -- end if;
			-- end if;
		-- end if;
	-- end process;
	
--测试	
	process(clk,reset)
    begin
	    if reset='0' then
		   PW_EN<='0';
        else
		    if clk'event and clk='1' then
			    if clk_cnt =3999 and  cnt=1 then --10ms
			        PW_EN<='1';
			    -- elsif clk_cnt =31999 and  cnt=20 then --60ms
			         -- PW_EN<='0';
			    -- else 
				    -- if clk_cnt =31999 and  cnt=30 then --30ms
			           -- PW_EN<='1';
					-- end if;
		        end if;
			end if;
		end if;
	end process;	
	
	
	
	
end Behavioral;

