`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    11:32:48 11/24/2022 
// Design Name: 
// Module Name:    DM_rv 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
(* keep_hierarchy = "no" *)module DM_rv(
//from M9
output  down_stream_fifo_wr_hfull,
input down_stream_fifo_wr_en,
input [7:0] down_stream_fifo_wr_data,
input down_stream_fifo_wr_clk,
input down_stream_fifo_rst,
//////////////////////
input fifo_rd_en,
output [12:0] fifo_rd_data_count,
output [7:0] fifo_data,
output fifo_empty,
//////////////////////
input reset,//high active
input clk
    );
	 
fifo1024x16_to_2048x8 v_fifo1024x16_to_2048x8
(
.rst(down_stream_fifo_rst),
.wr_clk(down_stream_fifo_wr_clk),
.din(down_stream_fifo_wr_data),
.wr_en(down_stream_fifo_wr_en),
.full(),
.prog_full(down_stream_fifo_wr_hfull),

.rd_clk(clk),
.dout(fifo_data),
.rd_en(fifo_rd_en),
.empty(fifo_empty),
.rd_data_count(fifo_rd_data_count)
);
endmodule
