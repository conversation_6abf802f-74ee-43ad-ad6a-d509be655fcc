`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    16:27:52 11/24/2020 
// Design Name: 
// Module Name:    RESET 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module RST( sclk,rst
    
    );
input sclk;
output rst;

reg[3:0]cnt;
reg rst;
always@(posedge sclk)
begin
	if(cnt<4'd5)
		cnt<=cnt+1'b1;
	else if(cnt==4'd5)
		cnt<=cnt;
	else
		cnt<=0;
end

always@(posedge sclk)
begin
	if (cnt==4'd5)
		rst<=1'b1;
	else
		rst<=1'b0;
end
		

endmodule
