----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    10:30:37 09/10/2021 
-- Design Name: 
-- Module Name:    CMD_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity CMD_PROCESS is
port(
    reset      :  in std_logic;
    clk        :  in std_logic; 
	clk_us_edge:  in std_logic;
	
    RXD        :  in std_logic;    
    -- rxd_flag   : in std_logic;
	-- rxd_data    : in std_logic_vector(7 downto 0);  
	
	TXD        :  out std_logic	;
	-- TX_ON      :  out std_logic;
	-- RX_ON      :  out std_logic;
	
--科学数据包
    s_data_in     :in std_logic_vector(31 downto 0);
    ms_data_in    :in std_logic_vector(15 downto 0);	
	data_out_lx1  :in std_logic_vector(7 downto 0);
	data_out_lx2  :in std_logic_vector(7 downto 0);
	data_out_lx3  :in std_logic_vector(7 downto 0);
	data_out_lx4  :in std_logic_vector(7 downto 0);
	data_out_lx5  :in std_logic_vector(7 downto 0);
	tmp1          :in std_logic_vector(7 downto 0);
	tmp2          :in std_logic_vector(7 downto 0);
	tmp3          :in std_logic_vector(7 downto 0);
	tmp4          :in std_logic_vector(7 downto 0);
	dy            :in std_logic_vector(7 downto 0);
	cmd_addr_rd   :out  std_logic_vector (9 downto 0);
	cmd_fgm       :in std_logic_vector(7 downto 0);
	
     da1_data   :in std_logic_vector(15 downto 0);
     da2_data   :in std_logic_vector(15 downto 0);
     da3_data   :in std_logic_vector(15 downto 0);
     da4_data   :in std_logic_vector(15 downto 0);	
--工况	
	power_state : in std_logic_vector(7 downto 0);
	off_cmd     : out std_logic;
--	off_state      : out std_logic;
--校时	
	s_data      : out std_logic_vector(31 downto 0);
    ms_data     : out std_logic_vector(15 downto 0);
	time_set    : out std_logic;
	
   mode_change : out std_logic;
   cmd_mode    : out std_logic_vector(7 downto 0);
   da_change   : out std_logic;
   da_data     : out std_logic_vector(47 downto 0);
   fgm_chanel_data  : out std_logic_vector(47 downto 0);
   fgm_change  : out std_logic;
 --  lc_change   : out std_logic;

--指令计数
--  cmd_count_right:out std_logic_vector(7 downto 0);
  cmd_count_error:out std_logic_vector(7 downto 0);
  cmd_error_type :out std_logic_vector(7 downto 0);
  cmd_sz_errcnt  :out std_logic_vector(7 downto 0);
  cmd_sz_errtype :out std_logic_vector(7 downto 0);
  cmd_count_lx   :out std_logic_vector(7 downto 0);
  cmd_count_time :out std_logic_vector(7 downto 0);
  cmd_count_sz   :out std_logic_vector(7 downto 0);
--  cmd_last       :out std_logic_vector(7 downto 0);
--  cmd_sz_code    :out std_logic_vector(7 downto 0);
  cmd_sz_data    :out std_logic_vector(47 downto 0)
		
);
end CMD_PROCESS;

architecture Behavioral of CMD_PROCESS is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

component CMD_REV is
port
(
    clk         :    in std_logic;        
    rst         :    in std_logic;        
    rxd         :    in std_logic;
	
    data_valid  :    out std_logic;        
    data_o      :    out std_logic_vector(7 downto 0); 
	rev_flag    :    out std_logic;     
--    parity_err  :    out std_logic;        
--    frame_err   :    out std_logic;
    trans_error_flag : out std_logic;
    trans_error_type : out std_logic_vector(7 downto 0)	
);
end component;

component cmd_check is
port
(
    clk_in          :    in std_logic;        
    rst_n           :    in std_logic;        
    rxd_flag        :    in std_logic;
	rxd_data        :    in std_logic_vector(7 downto 0);   
    data_valid      :    out std_logic;        
    data_o          :    out std_logic_vector(7 downto 0);         
    data_addr       :    out std_logic_vector(7 downto 0);
	
	error_state     :    out std_logic_vector(7 downto 0);
	cmd_type        :    out std_logic_vector(7 downto 0);
    rev_ok_flag     :    out std_logic ;       
    rev_error_flag  :    out std_logic 
);
end component;

component cmd_ram
	port (
	addra: IN std_logic_VECTOR(7 downto 0);
	addrb: IN std_logic_VECTOR(7 downto 0);
	clka: IN std_logic;
	clkb: IN std_logic;
	dina: IN std_logic_VECTOR(7 downto 0);
	doutb: OUT std_logic_VECTOR(7 downto 0);
	wea: IN STD_LOGIC_VECTOR(0 DOWNTO 0));
end component;


component CMD_ANALY is
port(
    reset: 		in std_logic;
    clk : 		in std_logic; 
	clk_us_edge:  in std_logic;
	
	rev_flag    : in std_logic;
	
	rev_ok_flag     : in std_logic ;       
    rev_error_flag  : in std_logic ;
    error_state     : in std_logic_vector(7 downto 0);
	cmd_type       : in std_logic_vector(7 downto 0);
	
	trans_error_flag : in std_logic;
    trans_error_type : in std_logic_vector(7 downto 0);
	
	ram_addr:  out std_logic_vector(7 downto 0);
    data_in:  in std_logic_vector(7 downto 0);
	
    s_data:  out std_logic_vector(31 downto 0);
    ms_data: out std_logic_vector(15 downto 0);
	time_set: out std_logic;
	
	send_start     : out std_logic;
	data_out  : inout std_logic_vector(7 downto 0);
	send_over : in std_logic;
	tx_state  : out std_logic;
	
	s_data_in    :in std_logic_vector(31 downto 0);
    ms_data_in   :in std_logic_vector(15 downto 0);
	data_out_lx1  :in std_logic_vector(7 downto 0);
	data_out_lx2  :in std_logic_vector(7 downto 0);
	data_out_lx3  :in std_logic_vector(7 downto 0);
	data_out_lx4  :in std_logic_vector(7 downto 0);
	data_out_lx5  :in std_logic_vector(7 downto 0);
	tmp1          :in std_logic_vector(7 downto 0);
	tmp2          :in std_logic_vector(7 downto 0);
	tmp3          :in std_logic_vector(7 downto 0);
	tmp4          :in std_logic_vector(7 downto 0);
	dy            :in std_logic_vector(7 downto 0);
	cmd_addr_rd   :out  std_logic_vector (9 downto 0);
	cmd_fgm       :in std_logic_vector(7 downto 0);
	
	 da1_data   :in std_logic_vector(15 downto 0);
     da2_data   :in std_logic_vector(15 downto 0);
     da3_data   :in std_logic_vector(15 downto 0);
     da4_data   :in std_logic_vector(15 downto 0);
	
   mode_change : out std_logic;
   cmd_mode    : out std_logic_vector(7 downto 0);
   da_change   : out std_logic;
   da_data     : out std_logic_vector(47 downto 0);
   fgm_chanel_data  : out std_logic_vector(47 downto 0);
   fgm_change  : out std_logic;
--   lc_change   : out std_logic;
   
--   cmd_count_right:out std_logic_vector(7 downto 0);
   cmd_count_error:out std_logic_vector(7 downto 0);
   cmd_sz_errcnt  :out std_logic_vector(7 downto 0);
   cmd_sz_errtype :out std_logic_vector(7 downto 0);
   cmd_error_type :out std_logic_vector(7 downto 0);
   cmd_count_lx   :out std_logic_vector(7 downto 0);
   cmd_count_time :out std_logic_vector(7 downto 0);
   cmd_count_sz   :out std_logic_vector(7 downto 0);
--   cmd_last       :out std_logic_vector(7 downto 0);
--   cmd_sz_code    :out std_logic_vector(7 downto 0);
   cmd_sz_data    :out std_logic_vector(47 downto 0);
   
   	off_set     : out std_logic;
--	off_state      : out std_logic;
   power_state    : in std_logic_vector(7 downto 0)
);
end component;

component uart_tx is
port(
   clk     : in  STD_LOGIC;
--   clk_us_edge:  in std_logic;
   rst     : in  STD_LOGIC;
   start   : in  STD_LOGIC;
   data_i  : in std_logic_vector(7 downto 0); 
   txd     : out std_logic;
   over    : out std_logic;
   tx_state: in  std_logic;
   tx_on   : out std_logic;
   rx_on   : out std_logic
);
end component;

    signal rxd_flag      : std_logic;
	signal rxd_data      : std_logic_vector(7 downto 0);     
     
    signal data_valid      :  std_logic;        
    signal data_o          :  std_logic_vector(7 downto 0);         
    signal data_addr       :  std_logic_vector(7 downto 0);
	signal ram_addr       :  std_logic_vector(7 downto 0);
    signal data_in        :  std_logic_vector(7 downto 0);
              
	signal error_state     :  std_logic_vector(7 downto 0);
	signal cmd_type        :  std_logic_vector(7 downto 0);
    signal rev_ok_flag     :  std_logic ;       
    signal rev_error_flag  :  std_logic ;

               
	signal send_start      :  std_logic;
	signal data_out        :  std_logic_vector(7 downto 0);
	signal send_over       :  std_logic;
    signal tx_state       :  std_logic;
	
	signal trans_error_flag : std_logic;
    signal trans_error_type : std_logic_vector(7 downto 0);
	
	signal rev_flag     :std_logic;
	signal ram_wr       :std_logic_vector(0 downto 0);
	                       

begin

ram_wr(0)<=data_valid ;
    u_cmd_rev:cmd_rev
   PORT MAP (
    clk         => clk        ,
    rst         => reset      ,
    rxd         => RXD        ,
    data_valid  => rxd_flag   ,
    data_o      => rxd_data   ,
	rev_flag    => rev_flag   ,
    trans_error_flag => trans_error_flag,
    trans_error_type => trans_error_type	
--    parity_err  => parity_err ,
--    frame_err   => frame_err  
    );   
	

	
   u_cmd_check: cmd_check 
   PORT MAP (
        clk_in         => clk,
        rst_n          => reset,
        rxd_flag       => rxd_flag,
        rxd_data       => rxd_data,
		
        data_valid     => data_valid,
        data_o         => data_o,
        data_addr      => data_addr,
        rev_ok_flag    => rev_ok_flag,
	    error_state    => error_state ,
        cmd_type       => cmd_type ,
        rev_error_flag => rev_error_flag
		
        );
		
	U_cmd_ram : cmd_ram
	port map (
		addra => data_addr,
		addrb => ram_addr,
		clka  => clk,
		clkb  => clk,
		dina  => data_o,
		doutb => data_in,
		wea   => ram_wr
	);

	
	u_cmd_amaly:CMD_ANALY
	PORT MAP (
	    reset            =>   reset          ,
	    clk              =>   clk            ,
		clk_us_edge      =>   clk_us_edge    ,
		
		rev_flag        =>     rev_flag      ,
	                                       
	    rev_ok_flag      =>   rev_ok_flag    ,
	    rev_error_flag   =>   rev_error_flag ,
	    error_state      =>   error_state    ,
	    cmd_type         =>   cmd_type       ,
		
		trans_error_flag => trans_error_flag ,
        trans_error_type => trans_error_type ,	
	            
	    ram_addr         =>   ram_addr       ,
	    data_in          =>   data_in        ,
	                                
	    s_data           =>   s_data         ,
	    ms_data          =>   ms_data        ,
	    time_set         =>   time_set       ,
	                                      
	    send_start       =>   send_start     ,    
	    data_out         =>   data_out       ,
	    send_over        =>   send_over      ,
		tx_state         =>   tx_state       ,
		
		s_data_in    => s_data_in    ,
        ms_data_in   => ms_data_in   ,
		data_out_lx1 => data_out_lx1 ,
        data_out_lx2 => data_out_lx2 ,
        data_out_lx3 => data_out_lx3 ,
        data_out_lx4 => data_out_lx4 ,
        data_out_lx5 => data_out_lx5 ,
        tmp1         => tmp1         ,
        tmp2         => tmp2         ,
        tmp3         => tmp3         ,
        tmp4         => tmp4         ,
        dy           => dy           ,
        cmd_addr_rd  => cmd_addr_rd  ,
        cmd_fgm      => cmd_fgm      ,
		
		da1_data     =>  da1_data ,
		da2_data     =>  da2_data ,
		da3_data     =>  da3_data ,
		da4_data     =>  da4_data ,
		
        cmd_mode        =>  cmd_mode       ,
        mode_change     =>  mode_change    ,
	    da_change       =>  da_change      ,
        da_data         =>  da_data        , 
        fgm_chanel_data      =>  fgm_chanel_data     ,
        fgm_change      =>  fgm_change     ,
--		lc_change      =>  lc_change,
		
		
		
--		cmd_count_right =>  cmd_count_right,
		cmd_count_error =>  cmd_count_error,
		cmd_error_type  =>  cmd_error_type ,
		cmd_count_lx    =>  cmd_count_lx   ,
		cmd_count_time  =>  cmd_count_time   ,
		cmd_count_sz    =>  cmd_count_sz   ,
--		cmd_last        =>  cmd_last       ,
--		cmd_sz_code     =>  cmd_sz_code    ,
		cmd_sz_data     =>  cmd_sz_data    ,
		cmd_sz_errcnt   =>  cmd_sz_errcnt  ,
        cmd_sz_errtype  =>  cmd_sz_errtype ,
		
--		off_state       =>  off_state      ,
		off_set   => off_cmd        ,
		power_state     =>  power_state
		
	);
	
	 u_cmd_tx:uart_tx 
	PORT MAP (
       clk      => clk,
--	   clk_us_edge=>clk_us_edge,
       rst      => reset,
       start    => send_start,
       data_i   => data_out,
       txd      => TXD,
       over     => send_over,
	   tx_state => '1'
	   -- tx_on    => TX_ON   ,
	   -- rx_on    => RX_ON   
     );

end Behavioral;

