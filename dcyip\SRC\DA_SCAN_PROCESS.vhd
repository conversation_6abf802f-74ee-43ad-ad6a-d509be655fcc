----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    00:27:07 11/14/2022 
-- Design Name: 
-- Module Name:    DA_SCAN_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity DA_SCAN_PROCESS is
Port ( 	
			clk			: in std_logic;
			reset 		: in std_logic;			
			start_scan  : in std_logic;
			s_edge      : in std_logic;
			send_over   : in std_logic;
						
			da_scan_step1  : in std_logic_vector(11 downto 0);
			da_scan_time1  : in std_logic_vector(3 downto 0);
			da_scan_hdata1 : in std_logic_vector(11 downto 0);
			da_scan_ldata1 : in std_logic_vector(11 downto 0);	
			
			da_scan_step2  : in std_logic_vector(11 downto 0);
			da_scan_time2  : in std_logic_vector(3 downto 0);
			da_scan_hdata2 : in std_logic_vector(11 downto 0);
			da_scan_ldata2 : in std_logic_vector(11 downto 0);	
			
			da_scan_step3  : in std_logic_vector(11 downto 0);
			da_scan_time3  : in std_logic_vector(3 downto 0);
			da_scan_hdata3 : in std_logic_vector(11 downto 0);
			da_scan_ldata3 : in std_logic_vector(11 downto 0);

			da_scan_step4  : in std_logic_vector(11 downto 0);
			da_scan_time4  : in std_logic_vector(3 downto 0);
			da_scan_hdata4 : in std_logic_vector(11 downto 0);
			da_scan_ldata4 : in std_logic_vector(11 downto 0);	

            da1_data   : out std_logic_vector(15 downto 0);
            da2_data   : out std_logic_vector(15 downto 0);
            da3_data   : out std_logic_vector(15 downto 0);
            da4_data   : out std_logic_vector(15 downto 0);				
			
	        send_mark1  : out std_logic;
	        send_mark2  : out std_logic;
	        send_mark3  : out std_logic;
	        send_mark4  : out std_logic;	
            da_out      : out std_logic_vector(15 downto 0)			
	);
end DA_SCAN_PROCESS;



architecture Behavioral of DA_SCAN_PROCESS is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

component DA_SCAN is
Port ( 	
			clk			: in std_logic;
			reset 		: in std_logic;
			
			start_scan    : in std_logic;
			s_edge        : in std_logic;
			
			da_scan_step  : in std_logic_vector(11 downto 0);
			da_scan_time  : in std_logic_vector(3 downto 0);
			da_scan_hdata : in std_logic_vector(11 downto 0);
			da_scan_ldata : in std_logic_vector(11 downto 0);			
            da_out        : out std_logic_vector(15 downto 0)			
	);
end component;

component DA_SCAN_OUT is
Port ( 	
	clk			: in std_logic;
	reset 		: in std_logic;
	send_over   : in std_logic;
	s_edge      : in std_logic;
	
	da_out1     : in std_logic_vector(15 downto 0);
	da_out2     : in std_logic_vector(15 downto 0);
	da_out3     : in std_logic_vector(15 downto 0);
	da_out4     : in std_logic_vector(15 downto 0);
	
	da1_data   : out std_logic_vector(15 downto 0);
    da2_data   : out std_logic_vector(15 downto 0);
    da3_data   : out std_logic_vector(15 downto 0);
    da4_data   : out std_logic_vector(15 downto 0);	
	
	send_mark1  : out std_logic;
	send_mark2  : out std_logic;
	send_mark3  : out std_logic;
	send_mark4  : out std_logic;	
    da_out      : out std_logic_vector(15 downto 0)
);
end component;

signal da_out1 : std_logic_vector(15 downto 0);
signal da_out2 : std_logic_vector(15 downto 0);
signal da_out3 : std_logic_vector(15 downto 0);
signal da_out4 : std_logic_vector(15 downto 0);

begin
	-- da1_data  <=da_out1;
    -- da2_data  <=da_out2;
    -- da3_data  <=da_out3;
    -- da4_data  <=da_out4;

U_DA_SCAN1:DA_SCAN
  PORT MAP (
    clk         => clk        ,
    reset       => reset      ,
	
	start_scan   =>start_scan   ,
	s_edge       =>s_edge       ,
	
	da_scan_step =>da_scan_step1 ,
	da_scan_time =>da_scan_time1 ,
	da_scan_hdata=>da_scan_hdata1,
	da_scan_ldata=>da_scan_ldata1,
	da_out       =>da_out1       	
   );	

U_DA_SCAN2:DA_SCAN
  PORT MAP (
    clk         => clk        ,
    reset       => reset      ,
	
	start_scan   =>start_scan   ,
	s_edge       =>s_edge       ,
	
	da_scan_step =>da_scan_step2 ,
	da_scan_time =>da_scan_time2 ,
	da_scan_hdata=>da_scan_hdata2,
	da_scan_ldata=>da_scan_ldata2,
	da_out       =>da_out2       	
   );	
   U_DA_SCAN3:DA_SCAN
  PORT MAP (
    clk         => clk        ,
    reset       => reset      ,
	
	start_scan   =>start_scan   ,
	s_edge       =>s_edge       ,
	
	da_scan_step =>da_scan_step3 ,
	da_scan_time =>da_scan_time3 ,
	da_scan_hdata=>da_scan_hdata3,
	da_scan_ldata=>da_scan_ldata3,
	da_out       =>da_out3       	
   );	
   U_DA_SCAN4:DA_SCAN
  PORT MAP (
    clk         => clk        ,
    reset       => reset      ,
	
	start_scan   =>start_scan   ,
	s_edge       =>s_edge       ,
	
	da_scan_step =>da_scan_step4 ,
	da_scan_time =>da_scan_time4 ,
	da_scan_hdata=>da_scan_hdata4,
	da_scan_ldata=>da_scan_ldata4,
	da_out       =>da_out4       	
   );	
   
   U_DA_SCAN_OUT:DA_SCAN_OUT
  PORT MAP (
    clk         => clk        ,
    reset       => reset      ,
	send_over   => send_over  ,
	s_edge      =>s_edge       ,
	
	da_out1     =>da_out1     ,  
	da_out2     =>da_out2     ,
	da_out3     =>da_out3     ,
	da_out4     =>da_out4     ,
	
	send_mark1 =>send_mark1,
	send_mark2 =>send_mark2,
	send_mark3 =>send_mark3,
	send_mark4 =>send_mark4,
	
	da1_data   =>da1_data  ,
	da2_data   =>da2_data  ,
	da3_data   =>da3_data  ,
	da4_data   =>da4_data  ,
	
	da_out      =>da_out       	
   );	




end Behavioral;

