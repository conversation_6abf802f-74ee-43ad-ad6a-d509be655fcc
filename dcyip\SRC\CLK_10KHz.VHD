----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    22:19:24 11/12/2013 
-- Design Name: 
-- Module Name:    DIV1000 - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity CLK_10KHz is
    Port ( clk : in  STD_LOGIC;
		   reset : in std_logic;
           clk_us_edge: in std_logic;		 
           clk10k : out  STD_LOGIC
);
end CLK_10KHz;

architecture Behavioral of CLK_10KHz is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";
	
signal cnt: integer range 0 to 99;
--signal ms_edge:std_logic;
begin
    process(clk,reset)
    begin
        if reset='0' then
            cnt<=0;
        else
            if clk'event and clk='1' then
                if clk_us_edge ='1' then
                    if cnt =99 then
                        cnt <=0;
                    else
                        cnt <= cnt+1;
                    end if;
                end if;
            end if;
        end if;
    end process;

    process(clk,reset)
    begin
        if reset ='0' then
            clk10k <='0';
        else
            if clk'event and clk='1' then
                if cnt <50 then
                    clk10k<='0';
                else
                    clk10k<='1' ;
                end if;
            end if;
        end if;
    end process;
	
end Behavioral;
   
