<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<project xmlns="http://www.xilinx.com/XMLSchema" xmlns:xil_pn="http://www.xilinx.com/XMLSchema">

  <header>
    <!-- ISE source project file created by Project Navigator.             -->
    <!--                                                                   -->
    <!-- This file contains project source information including a list of -->
    <!-- project source files, project and process properties.  This file, -->
    <!-- along with the project source files, is sufficient to open and    -->
    <!-- implement in ISE Project Navigator.                               -->
    <!--                                                                   -->
    <!-- Copyright (c) 1995-2013 Xilinx, Inc.  All rights reserved. -->
  </header>

  <version xil_pn:ise_version="14.7" xil_pn:schema_version="2"/>

  <files>
    <file xil_pn:name="CMD_RAM.ngc" xil_pn:type="FILE_NGC">
      <association xil_pn:name="BehavioralSimulation" xil_pn:seqID="2"/>
      <association xil_pn:name="Implementation" xil_pn:seqID="0"/>
    </file>
    <file xil_pn:name="CMD_RAM.vhd" xil_pn:type="FILE_VHDL">
      <association xil_pn:name="BehavioralSimulation" xil_pn:seqID="1"/>
      <association xil_pn:name="Implementation" xil_pn:seqID="1"/>
      <association xil_pn:name="PostMapSimulation" xil_pn:seqID="4"/>
      <association xil_pn:name="PostRouteSimulation" xil_pn:seqID="4"/>
      <association xil_pn:name="PostTranslateSimulation" xil_pn:seqID="4"/>
    </file>
  </files>

  <properties>
    <property xil_pn:name="Auto Implementation Top" xil_pn:value="false" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Device" xil_pn:value="xc4vsx55" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Device Family" xil_pn:value="Virtex4" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Enable Internal Done Pipe" xil_pn:value="true" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Implementation Stop View" xil_pn:value="PreSynthesis" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Implementation Top" xil_pn:value="Architecture|CMD_RAM|CMD_RAM_a" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Implementation Top File" xil_pn:value="CMD_RAM.vhd" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Implementation Top Instance Path" xil_pn:value="/CMD_RAM" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Max Fanout" xil_pn:value="100000" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Package" xil_pn:value="ff1148" xil_pn:valueState="default"/>
    <property xil_pn:name="Preferred Language" xil_pn:value="Verilog" xil_pn:valueState="default"/>
    <property xil_pn:name="Project Generator" xil_pn:value="CoreGen" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Property Specification in Project File" xil_pn:value="Store all values" xil_pn:valueState="default"/>
    <property xil_pn:name="Simulator" xil_pn:value="ISim (VHDL/Verilog)" xil_pn:valueState="default"/>
    <property xil_pn:name="Speed Grade" xil_pn:value="-10" xil_pn:valueState="non-default"/>
    <property xil_pn:name="Synthesis Tool" xil_pn:value="XST (VHDL/Verilog)" xil_pn:valueState="default"/>
    <property xil_pn:name="Top-Level Source Type" xil_pn:value="HDL" xil_pn:valueState="default"/>
    <property xil_pn:name="Working Directory" xil_pn:value="." xil_pn:valueState="non-default"/>
    <!--                                                                                  -->
    <!-- The following properties are for internal use only. These should not be modified.-->
    <!--                                                                                  -->
    <property xil_pn:name="PROP_DesignName" xil_pn:value="CMD_RAM" xil_pn:valueState="non-default"/>
    <property xil_pn:name="PROP_DevFamilyPMName" xil_pn:value="virtex4" xil_pn:valueState="default"/>
    <property xil_pn:name="PROP_intProjectCreationTimestamp" xil_pn:value="2022-11-07T21:31:56" xil_pn:valueState="non-default"/>
    <property xil_pn:name="PROP_intWbtProjectID" xil_pn:value="26E1CA44E92147C6A6390DB70D2A8C46" xil_pn:valueState="non-default"/>
    <property xil_pn:name="PROP_intWorkingDirLocWRTProjDir" xil_pn:value="Same" xil_pn:valueState="non-default"/>
    <property xil_pn:name="PROP_intWorkingDirUsed" xil_pn:value="No" xil_pn:valueState="non-default"/>
  </properties>

  <bindings/>

  <libraries/>

  <autoManagedFiles>
    <!-- The following files are identified by `include statements in verilog -->
    <!-- source files and are automatically managed by Project Navigator.     -->
    <!--                                                                      -->
    <!-- Do not hand-edit this section, as it will be overwritten when the    -->
    <!-- project is analyzed based on files automatically identified as       -->
    <!-- include files.                                                       -->
  </autoManagedFiles>

</project>
