
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    14:35:44 04/20/2014 
// Design Name: 
// Module Name:    rx_module 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module rx_module5x(rx,clk_5x,rst,data_out,
                 rk_flag,data_out_valid,BC_flag,false_detect,rk_false,data_3b_false,data_5b_false);
input rx;
input clk_5x;
input rst;
output rk_flag;
output BC_flag;
output data_out_valid,false_detect;
output rk_false,data_3b_false,data_5b_false;
output [7:0] data_out;

wire clk_5x;
wire  recovery_data;
wire [9:0] s2p_to_comma;
wire [9:0] comma_to_decoder;
wire comflag_to_en_decoder;
wire decoder_en;
wire false_detect_temp;
wire rk_false_temp,valid,ctrl;

assign false_detect=false_detect_temp &  decoder_en;
assign rk_false = rk_false_temp & decoder_en;
					   
						   
x_cdr5x		 data_recovery_5x (
							.clk5x(clk_5x), 
							.rst(rst), 
							.seri_dat(rx), 
							.cmr_dat(recovery_data),
							.cmr_dat_vld(ctrl),
							.false_detect( false_detect_temp));
							

serdes5x_pmu         s2p_5x    (.clk(clk_5x),
                         .en(ctrl),
								 .data_in(recovery_data),
								 .data_out(s2p_to_comma),
								 .rst(rst),
								 .valid(valid));


//comma check
comma_check_5x      comma_5x (.data_i(s2p_to_comma),
                        .clk(clk_5x),
						      .rst(rst),
						      .data_out(comma_to_decoder),
							  .valid(valid),
								.comma_flag(BC_flag),
								.valid_out(data_valid),
								.decoder_en(decoder_en));

//decoder
decoder_8b10b_5x    decoder_5x (.clk(clk_5x), 
                          .data_in(comma_to_decoder), 
                          .rst(rst),
						        .en(decoder_en), 
                          .rk_flag(rk_flag), 
                          .data_out(data_out),
								  .data_valid(data_valid),
								  .data_out_valid(data_out_valid),
								  .rk_false(rk_false_temp), 
								  .data_3b_false(data_3b_false), 
								  .data_5b_false(data_5b_false) );

endmodule
