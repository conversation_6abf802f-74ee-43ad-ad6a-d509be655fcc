library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;
--接收到PW_EN信号及关机前输出复位信号。

entity RST_CTRL is
    Port (  
			clk       : in  STD_LOGIC;
		    PW_EN     : in  STD_LOGIC;
			off_cmd   : in std_logic;
			clk_s_edge: in std_logic;
			
			reset_n   : out STD_LOGIC
		   );

end RST_CTRL;

architecture Behavioral of RST_CTRL is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

--signal reset_1:std_logic;
--signal start_off:std_logic;
signal cnt:integer range 0 to 5;
signal pw_en_reg:std_logic;
signal pw_en_edge:Std_logic;
signal pw_en_reg1:std_logic;
signal reset_1:std_logic;
signal start_off:std_logic;

begin
process(clk)
	 begin
	    if clk'event and clk='1' then	
		     pw_en_reg<=PW_EN;
			 pw_en_reg1<=pw_en_reg;
	    end if;
	end process;


process(clk)
	 begin
	    if clk'event and clk='1' then	
		     if pw_en_reg='1' and pw_en_reg1='0' then 
			    pw_en_edge<='1';
			 else 
			    pw_en_edge<='0';
			end if;
	    end if;
	end process;
		
 process(clk)
	 begin
	    if clk'event and clk='1' then
	        if pw_en_edge='1' then
		        start_off<='0';
		    else 
			    if off_cmd='1' then 
			        start_off<='1';
			    end if;
			end if;
		end if;
    end process;
		
	 process(clk)
	 begin
		if clk'event and clk='1' then
            if pw_en_edge='1' then 
   			    cnt<=0;
			else
			    if  start_off='1' then 
			        if clk_s_edge='1' then 
--                    if clk_us_edge='1' then 
			    	    if cnt<5 then  cnt<= cnt+1;
			    		else cnt<=5;
			    		end if;
			    	end if;
			    end if;
			end if;
		end if;
    end process;
	
	process(clk)
	 begin
		if clk'event and clk='1' then
            if pw_en_edge='1' then 	
			    reset_1<='1';
            else			
			    if  cnt=5 then 
			        reset_1<='0';
			    else 
			        reset_1<='1';
			    end if;
			end if;
		end if;
    end process;
	
	reset_n<=((PW_EN) and (reset_1));

end Behavioral;