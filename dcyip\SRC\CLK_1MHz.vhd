----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    21:16:33 11/12/2013 
-- Design Name: 
-- Module Name:    CLK_1MHz - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity CLK_1MHz is
    Port ( clk : in  STD_LOGIC;
		   reset : in std_logic;		 
           clk_1M : out  STD_LOGIC;
           us_edge:out std_logic);

end CLK_1MHz;

architecture Behavioral of CLK_1MHz is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal cnt: integer range 0 to 32;
	
begin
	process(clk,reset)
    begin
        if reset ='0' then
           cnt <=0;
        else
            if clk='1' and clk'event then
                if cnt =31 then
                    cnt <=0;
                else
                    cnt <= cnt+1;
                end if;
            end if;
        end if;
    end process;

    process(clk,reset)
    begin
        if reset ='0' then
            clk_1M <='0';
        else
            if clk='1' and clk'event then
                if cnt <16 then
                    clk_1M <='0';
                else
                    clk_1M<='1';
                end if;
            end if;
        end if;
    end process;

    process(clk,reset)
    begin
        if reset ='0' then
            us_edge<='0';
        else
            if clk='1' and clk'event then
                if cnt =0 then
                    us_edge <='1';
                else
                    us_edge <='0';
                end if;
            end if;
        end if;
    end process;
	

end Behavioral;
