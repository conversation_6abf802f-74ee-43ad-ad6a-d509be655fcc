----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    10:50:13 11/13/2022 
-- Design Name: 
-- Module Name:    DA_DRV - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library ieee;
use ieee.std_logic_1164.all;
use ieee.std_logic_unsigned.all;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity DA_DRV is
Port ( 	
			clk			: in std_logic;
			reset 		: in std_logic;
			clk_128     : in std_logic;
			da_data_in  : in std_logic_vector(15 downto 0);
			send_mark1   : in std_logic;
			send_mark2   : in std_logic;
			send_mark3   : in std_logic;
			send_mark4   : in std_logic;
			send_over	: out std_logic;
            busy_flag    : out std_logic;			
			
			SC_CS1		: out std_logic; 
            SC_CS2		: out std_logic; 
            SC_CS3		: out std_logic; 
            SC_CS4		: out std_logic; 			
			SC_DATA		: out std_logic;                  
			SC_CLK 		: out std_logic     
	);
end DA_DRV;

architecture Behavioral of DA_DRV is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal cnt:integer range 0 to 33:=0;
	signal clk_128_delay1: std_logic;
	signal clk_128_delay2: std_logic;
	signal clk_128_delay3: std_logic;
	signal clk_128_delay4: std_logic;

	signal clk_128_edge1:std_logic;
	signal clk_128_edge3:std_logic;
	signal CS_buf: std_logic;
	signal SC_DATA_buf: std_logic;
	signal send_mark:std_logic;

begin
    send_mark<= (send_mark1 or send_mark2 or send_mark3 or send_mark4);
	
	process(clk,reset)
	begin
		if reset='0' then
			clk_128_delay1 <='0';
            clk_128_delay2 <='0';
            clk_128_delay3 <='0';
            clk_128_delay4 <='0';
		else
			if  clk'event and clk='1' then 
				clk_128_delay1<= clk_128;           --为计数设置触发            --zadder
				clk_128_delay2<=clk_128_delay1;     --计数变化占用一个时钟
				clk_128_delay3<=clk_128_delay2;    --为正式输出设置触发
				clk_128_delay4<=clk_128_delay3;     --输出的sclk
			end if;
		end if;
	end process;

	process(clk,reset)
	begin
		if reset='0' then
			clk_128_edge1 <='0';
            clk_128_edge3 <='0';
		else
			if clk'event and clk='1' then
				if clk_128_delay1 ='0' and clk_128='1' then
					clk_128_edge1<= '1';
				else
					clk_128_edge1<='0';
                end if;
			end if;
			if clk'event and clk='1' then
				if clk_128_delay3 ='0' and clk_128_delay2='1' then
					clk_128_edge3<= '1';
				else
					clk_128_edge3<='0';
                end if;
			end if;
		end if;
	end process;

	process(clk,reset)
	begin
		if reset='0' then
			cnt <=0;
		else
			if clk'event and clk='1' then
				if send_mark = '1' then 
                    if clk_128_edge1 ='1' then
					    if cnt = 17 then
						    cnt <=0;
					    else
						    cnt <= cnt+1;
					    end if;
                    end if;
				else
					cnt <=0;
				end if;
				
			end if;
		end if;
	end process;
	
	process(reset,clk)              
	begin 
		if reset='0' then
			CS_buf <='1';
			SC_DATA_buf<='0';	
		else
			if clk'event and clk='1' then      
				case cnt is
					when 1 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(15);
					when 2 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(14);
					when 3 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(13);
					when 4 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(12);
					when 5 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(11);
					when 6 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(10);
					when 7 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(9);
					when 8 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(8);
					when 9 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(7);
					when 10 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(6);
					when 11 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(5);
					when 12 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(4);
					when 13 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(3);
					when 14 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(2);
					when 15 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(1);
					when 16 =>
						CS_buf <='0';
						SC_DATA_buf <= da_data_in(0);
					when others =>
						CS_buf <='1';
						SC_DATA_buf <= '0';
				end case;
			end if;
		end if;
	end process;

    process(reset,clk)
	begin 
		if reset='0' then
			SC_CS1 <='0';
			SC_CS2 <='0';
			SC_CS3 <='0';
			SC_CS4 <='0';
			SC_DATA <='0';
		else
			if clk'event and clk='1' then
				if clk_128_edge3 ='1'  then     --下一个时钟到来时即把数据发送出去  --jadder
                    if send_mark1='1' then SC_CS1<=CS_BUF; else SC_CS1 <='1';end if;
					if send_mark2='1' then SC_CS2<=CS_BUF; else SC_CS2 <='1';end if; 
					if send_mark3='1' then SC_CS3<=CS_BUF; else SC_CS3 <='1';end if; 
					if send_mark4='1' then SC_CS4<=CS_BUF; else SC_CS4 <='1';end if;
                    -- SC_CS1<=CS_BUF; 
					-- SC_CS2<=CS_BUF; 
					-- SC_CS3<=CS_BUF; 
					-- SC_CS4<=CS_BUF;  
					SC_DATA <= SC_DATA_buf;
				end if;
			end if;
		end if;
	end process;	
	
	process(reset,clk)
	begin 
		if reset='0' then
			SC_CLK <='0';
		else
            if clk'event and clk='1' then
                SC_CLK<=clk_128_delay3;  
--                SC_CLK<=clk_128_delay4;  
            end if;
        end if;
    end process;		

    process(reset,clk)
	begin 
		if reset='0' then
			send_over <='0';
		else
			if clk'event and clk='1' then
				if cnt =17 and clk_128_edge3 ='1' then --一个data_in发送结束，置send_over为1
					send_over <= '1';
				else
					send_over <='0';
				end if;
			end if;
		end if;
	end process;

    process(reset,clk)
	begin 
		if reset='0' then
			busy_flag <='0';
		else
			if clk'event and clk='1' then
				if cnt>=1 and cnt<=17 then --一个data_in发送结束，置send_over为1
					busy_flag <= '1';
				else
					busy_flag <='0';
				end if;
			end if;
		end if;
	end process;

	
		
	

end Behavioral;

