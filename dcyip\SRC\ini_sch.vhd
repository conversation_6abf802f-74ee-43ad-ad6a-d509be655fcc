-- Init_sch.vhd

library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;


entity Init_sch is
    Port ( 	clk		: in  STD_LOGIC;
			reset 	: in 	std_logic;
			clk_1ms_edge	:	in	std_logic;
	
            init_lc: out std_logic;
			init_da: out std_logic;
			init_fgm: out std_logic;
            mp_enable: out std_logic ;
            driv_enable:out std_logic			
          );
end Init_sch;

architecture Behavioral of Init_sch is
type init_stat_t is (lc_init_s,da_init_s,fgm_init_s,driv_init_s,mp_init_s);
signal init_stat:init_stat_t;
attribute syn_encoding: string;
attribute syn_encoding of init_stat: signal is "safe,onehot";
signal init_ms: integer range 0 to 2047;
begin
	process(clk,reset)
	begin
		if reset='0' then
			init_ms<=0;
		else
			if clk'event and clk='1' then
				if clk_1ms_edge ='1' then
					if init_ms =2047 then
						init_ms <=2047;
					else
						init_ms<= init_ms +1;
					end if;
				end if;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
			init_lc<='0'; init_da<='0'; init_fgm<='0'; mp_enable<='0'; driv_enable<='0';init_stat <= lc_init_s;
		else
			if clk='1' and clk'event then
				case init_stat is
				when lc_init_s =>
					if init_ms>=5 then
						init_lc <='1'; init_stat <= da_init_s ;  
					else
						init_lc <='0'; init_da <='0'; init_fgm <='0'; mp_enable<='0';driv_enable<='0';
					end if;
				when da_init_s =>
					if init_ms>=8 then
						init_da <='1';	init_stat <= fgm_init_s;
					else
						init_lc <='0'; init_da <='0'; init_fgm <='0'; mp_enable<='0';driv_enable<='0';
					end if;
				when fgm_init_s =>
					if init_ms>=10 then
						init_fgm <='1';	init_stat <= driv_init_s;
					else
						init_lc <='0'; init_da <='0'; init_fgm <='0'; mp_enable<='0';driv_enable<='0';
					end if;
				when driv_init_s=>
				    if init_ms>=1800 then
						driv_enable<='1';init_stat <= mp_init_s;
					else
						init_lc <='0'; init_da <='0'; init_fgm <='0'; mp_enable<='0';driv_enable<='0';
					end if;				
				when mp_init_s =>
					if init_ms>=2000 then
						mp_enable<='1';driv_enable<='1';
					else
						init_lc <='0'; init_da <='0'; init_fgm <='0'; mp_enable<='0';driv_enable<='1';
					end if;
				when others => null;
				end case;
			end if;
		end if;
	end process;
				
end Behavioral;
