----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    22:19:24 11/12/2013 
-- Design Name: 
-- Module Name:    DIV1000 - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity DIV1 is
    Port ( clk : in  STD_LOGIC;
		   reset : in std_logic;
           clk_ms_edge: in std_logic;		 
		   s_edge:out std_logic
);
end DIV1;

architecture Behavioral of DIV1 is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";
	
signal cnt: integer range 0 to 1023;

begin
    process(clk,reset)
    begin
        if reset='0' then
            cnt<=0;
        else
            if clk'event and clk='1' then
                if clk_ms_edge ='1' then
                    if cnt =999 then
                        cnt <=0;
                    else
                        cnt <= cnt+1;
                    end if;
                end if;
            end if;
        end if;
    end process;

    -- process(clk,reset)
    -- begin
        -- if reset ='0' then
            -- clk1000 <='0';
        -- else
            -- if clk'event and clk='1' then
                -- if cnt <500 then
                    -- clk1000<='0';
                -- else
                    -- clk1000<='1' ;
                -- end if;
            -- end if;
        -- end if;
    -- end process;
	
	process(clk,reset)
    begin
        if reset ='0' then
            s_edge<='0';
        else
            if clk='1' and clk'event then
                if cnt =999 and clk_ms_edge='1' then
                    s_edge <='1';
                else
                    s_edge <='0';
                end if;
            end if;
        end if;
    end process;
	
end Behavioral;
   
