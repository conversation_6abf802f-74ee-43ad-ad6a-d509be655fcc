//*************************************************************************************
// Company:                                                                            
// Engineer:                                                              
// Create Date:                                                     
// Design Name:                                                                        
// Module Name:   uart_rx_control                                                               
// Project Name:                                                                       
// Target Devices:                                                                  
// Description:                              
//************************************************************************************/
`timescale 1 ns / 1 ns
(*keep_hierarchy="no"*)module cmd_check
(
  input  wire            clk_in          ,               
  input  wire            rst_n           ,
 input  wire            rxd_flag        ,
  input  wire  [7:0]     rxd_data        ,
  
/*   input wire            parity_err       ,
  input wire            frame_err */
  
  output reg             data_valid      ,
  output reg   [07:0]    data_o          ,
  output reg   [07:0]    data_addr       ,
  output reg   [7:0]     error_state     ,
//  output reg     [7:0]  code_number     ,
  output reg   [7:0]     cmd_type        ,
  output reg             rev_ok_flag     ,
  output reg             rev_error_flag      
); 

localparam RX_MAX_LENGTH = 16'd252;
localparam CMD_HEAD1 = 8'hEB;
localparam CMD_HEAD2 = 8'h90;
localparam CMD_ADDR  = 8'h13;

// clk_in is 50 Mhz
//assign     clk_rx_out    =   clk_in  ; 
 
reg  [15:0] uart_time_cnt;

reg  start_rev_flag;
//error 标识
reg  geshi_error  ;
reg  length_error ;
reg  time_error   ;
reg  check_sum_error ;
//reg  trans_error_flag;
reg cmd_type_error ;

wire  [7:0] rx_max;
reg  [7:0] length;
reg  [7:0] head1;
reg  [7:0] head2;
reg  [7:0] zaihe_addr;
reg  [15:0] checksum;
reg  [15:0] data_sum;


reg         rxd_flag_r1;
reg  [7:0]  rxd_data_r1;
reg  [7:0] rx_cntr;

//reg  [07:0] sum_data;

//(*KEEP="TRUE"*)reg  [04:0]   st_curr;
reg  [04:0]   st_next;

localparam     ST_RST_IDLE   = 5'b00001;
localparam     ST_CHECK      = 5'b00010;
localparam     ST_RECEIVE    = 5'b00100;
localparam     ST_ERROR	  = 5'b01000;
localparam     ST_OVER       = 5'b10000;


// add by zzs

localparam     FRAME_OVER_TIME_CNT       = 16'd32000;

//time_cnt
// clk_in is 50Mhz, min baud_rate is 614400, every Byte max has (1/(614400/11))*50M = 896 clks
// if baud_rate = 1Mhz(1bit), every Byte max has 1+8+1+1 =11bit, so Byte rate = 1/11M, time_clks = (1/(1/11)M)*50M = 550
// signaltap = 222h = 546
// let set uart_time_cnt > 5*896 ~= 4502 = 0.0009s = 90us, defined as over time error

always @(posedge clk_in or negedge rst_n)
begin
	if(rst_n==0)
		uart_time_cnt <= 16'b0;
	else case(st_next)
		ST_RECEIVE:
		begin
			if(rxd_flag_r1==1)
				uart_time_cnt <= 16'b0;
			else
				uart_time_cnt <= uart_time_cnt + 16'b1;
		end
		default:uart_time_cnt <= 0;
	endcase
end

//--------rxd_flag_r1-----------
always @(posedge clk_in or negedge rst_n)
begin
	if(rst_n==0)
	begin
		rxd_flag_r1 <= 1'd0;
		rxd_data_r1 <= 8'd0;
  end
	else
	begin
		rxd_flag_r1 <= rxd_flag;
		rxd_data_r1 <= rxd_data;
	end
end

//1st segment
/* always @(posedge clk_in or negedge rst_n)
begin
	if(rst_n==0)
		st_curr <= ST_RST_IDLE;
	else
		st_curr <= st_next;
end */

//2nd segment
//always @(*)//like an assign statement
always @(posedge clk_in or negedge rst_n)
begin
	if(rst_n==0)
	    begin
		st_next <= ST_RST_IDLE;
		rev_ok_flag<=1'b0;     //接收完指令标识
		rev_error_flag<=1'b0;  //接收错误标识
		start_rev_flag<=1'b0;  //开始接收指令标识
		end 
	else 
	case(st_next)
		ST_RST_IDLE:
		    begin
		        rev_ok_flag<=1'b0;	
                start_rev_flag<=1'b0;
	            rev_error_flag<=1'b0;
				st_next <= ST_CHECK;
			end
		ST_CHECK:	
            begin		
                if (error_state!=8'b0)
		    		st_next <= ST_ERROR;	
					
		    	else if((rxd_flag==1)&&(rxd_data==CMD_HEAD1))
				    begin
		    		    st_next <= ST_RECEIVE;
					    start_rev_flag<=1'b1;
					end
		    	else
		    		st_next <= ST_CHECK;
		    end	
		ST_RECEIVE:
		    begin
/*		    	if (uart_time_cnt > FRAME_OVER_TIME_CNT)
		    		st_next = ST_FRAME_OVER_TIME;
 				else if(length>RX_MAX_LENGTH)
				    st_next = ST_LENGTH_ERROR; */
			    if (error_state!=8'b0)
		    		st_next <= ST_ERROR;		
		        else if(rx_cntr==rx_max)
				    st_next<= ST_OVER;
				else if(rx_cntr<rx_max)
				     st_next <= ST_RECEIVE;					
		    	else
		    	st_next <= ST_OVER;
		    end
		ST_OVER:
		    begin
			    if (error_state!=8'b0)
			        st_next <= ST_ERROR;
                else 
                    begin			
		         	    st_next <= ST_RST_IDLE;
		         	    rev_ok_flag<=1'b1;
					end 
	        end
		ST_ERROR:
		    begin
				rev_error_flag<=1'b1;
				start_rev_flag<=1'b0;
		    	st_next <= ST_RST_IDLE;
		    end	
		default:
		    begin	
				st_next <= ST_RST_IDLE;
		    end
	endcase
end

//3rd segment

//data_valid
always @(posedge clk_in or negedge rst_n)
begin
	if(rst_n==0)
	    begin
		    data_valid <= 0;
		    data_o <= 0;
		    data_addr <= 0;
		end 
	else if((rxd_flag_r1==1)&& (start_rev_flag==1))
		begin
		    data_valid <= 1;
		    data_o <= rxd_data_r1;
		    data_addr <= rx_cntr[7:0];
		end
	else 
	    begin
		    data_valid <= 0;
		    data_o <= data_o;
		    data_addr <= data_addr;
		end 
end

//rx_cntr
always @(posedge clk_in or negedge rst_n)
begin
	if(rst_n==0)
		rx_cntr <= 0;
	else if(start_rev_flag==1)
		begin
			if(rxd_flag_r1==1)
				if(rx_cntr<rx_max)
					rx_cntr <= rx_cntr + 1'b1;
				else
					rx_cntr <= 0;
			else
				begin
					rx_cntr <= rx_cntr;
				end
		end
    else
	    rx_cntr <= 0;
end

//---rx_max----------------
always @(posedge clk_in or negedge rst_n)
begin
	if(rst_n==0)
	    begin
		    length <= 8'd0;
		    head1<=8'h00;
		    head2<=8'h00;
		    zaihe_addr<=8'h00;
		    checksum<=16'h00;
//			code_number<=8'h00;
			cmd_type<=8'h00;
		end
	else if(start_rev_flag==1)
		begin
            if((rx_cntr==0)&&(rxd_flag_r1==1))
			    head1<=rxd_data_r1 ;
			else if((rx_cntr==1)&&(rxd_flag_r1==1))
	    		head2 <= rxd_data_r1 ;
			else if((rx_cntr==2)&&(rxd_flag_r1==1))
	    		zaihe_addr <= rxd_data_r1 ;
			else if((rx_cntr==3)&&(rxd_flag_r1==1))
	    		cmd_type <= rxd_data_r1 ;
			else if((rx_cntr==4)&&(rxd_flag_r1==1))
	    		length <= rxd_data_r1 ;
//			else if((rx_cntr==4)&&(rxd_flag_r1==1))
//	    		code_number <= rxd_data_r1 ;	
			else if((rx_cntr==rx_max-2)&&(rxd_flag_r1==1))
	    		checksum[15:8] <= rxd_data_r1 ;
			else if((rx_cntr==rx_max-1)&&(rxd_flag_r1==1))
	    		checksum[7:0] <= rxd_data_r1 ;
	    	else
			    begin
			    	length       <= length      ;
			    	head1        <= head1       ;
			    	head2        <= head2       ;
			    	zaihe_addr   <= zaihe_addr  ;
			    	checksum     <= checksum    ;
//			    	code_number  <= code_number ;	
					cmd_type     <= cmd_type    ;
			    end
		end 
	else
	    begin   
		    length    <= 8'd0;
		    head1      <=8'h00;
		    head2      <=8'h00;
		    zaihe_addr <=8'h00;
		    checksum   <=16'h00;
//			code_number<=code_number;
			cmd_type   <=cmd_type;
		end
end

assign rx_max= length+8;

always @(posedge clk_in or negedge rst_n)
begin
	if(rst_n==0)
	    begin
		    error_state  <= 8'd0;
		    geshi_error  <= 0;
		    length_error <= 0;
		    time_error   <= 0;
		    check_sum_error <= 0;
			cmd_type_error<=0;
		end
/* 	else if(parity_err==1)
			error_state <= 8'h11;
	else if(frame_err==1)
			error_state <= 8'h12;	 */
	else if(start_rev_flag==1)
		begin
			if(((head1!=CMD_HEAD1)||(head2!=CMD_HEAD2)||(zaihe_addr!=CMD_ADDR))&&(rx_cntr==3))
			    begin
			    geshi_error<= 1 ;
//				error_state[0]<=1'b1;
                error_state <= 8'h21;
				end
			else if(rx_cntr==5)
			    begin
				if(cmd_type==8'h13) begin  if(length != 8'h05)  begin length_error <= 1 ; error_state <= 8'h23; end end 
				else if(cmd_type==8'h25) begin  if(length != 8'h01)  begin length_error <= 1 ; error_state <= 8'h23; end end 	
				else if(cmd_type==8'h87) begin  if(length != 8'h05)  begin length_error <= 1 ; error_state <= 8'h23; end end 	
				else if(cmd_type==8'h94) begin  if(length != 8'h01)  begin length_error <= 1 ; error_state <= 8'h23; end end 
//				else if(cmd_type==8'h63) begin  if(length >= 8'd217)  begin length_error <= 1 ; error_state <= 8'h23; end end 
                else begin cmd_type_error <= 1 ; error_state <= 8'h22;end 
				end 					
/* 	    		length_error <= 1 ;
//				error_state[3]<=1'b1;
                error_state <= 8'h23; 
				end*/
			else if(uart_time_cnt > FRAME_OVER_TIME_CNT)
			    begin
	    		time_error <= 1 ;
				error_state <= 8'h13;
//				error_state[4]<=1'b1;
				end
			else if((data_sum!=checksum)&&(rx_cntr==rx_max))
			    begin
	    		check_sum_error <= 1;
//				error_state[1]<=1'b1;
                error_state <= 8'h24;
				end
	    	else
			    begin
	    	    	geshi_error     <= geshi_error;
			    	length_error    <= length_error;
			    	time_error      <= time_error;
			    	check_sum_error <= check_sum_error;
					cmd_type_error  <= cmd_type_error;
					error_state     <= error_state;
		        end
		end 
	else
	    begin
	    	geshi_error  <= 0;
			length_error <= 0;
			time_error   <= 0;
			check_sum_error <= 0;
			cmd_type_error  <= 0;
			error_state<= 8'd0;
		end
end


		

	
//data_sum
always @(posedge clk_in or negedge rst_n)
begin
	if(rst_n==0)
		data_sum <= 0;
	else if(start_rev_flag==1)
		begin
/* 			if((rx_cntr<5)&&(rx_cntr>1)&&(rxd_flag_r1==1))
				data_sum <= data_sum + rxd_data_r1;
			else  */
			if ((rx_cntr<rx_max-2)&&(rx_cntr>1)&&(rxd_flag_r1==1))
			    data_sum <= data_sum + rxd_data_r1;
			else
				data_sum <= data_sum;
		end
	else
	   data_sum <= 0;
end

endmodule
