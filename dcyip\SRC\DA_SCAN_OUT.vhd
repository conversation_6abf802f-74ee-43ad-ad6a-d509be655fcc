----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    00:56:53 11/14/2022 
-- Design Name: 
-- Module Name:    DA_SCAN_OUT - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity DA_SCAN_OUT is
Port ( 	
	clk			: in std_logic;
	reset 		: in std_logic;
	send_over   : in std_logic;
	s_edge      : in std_logic;
	
	da_out1     : in std_logic_vector(15 downto 0);
	da_out2     : in std_logic_vector(15 downto 0);
	da_out3     : in std_logic_vector(15 downto 0);
	da_out4     : in std_logic_vector(15 downto 0);
	
	da1_data   : out std_logic_vector(15 downto 0);
    da2_data   : out std_logic_vector(15 downto 0);
    da3_data   : out std_logic_vector(15 downto 0);
    da4_data   : out std_logic_vector(15 downto 0);	
	
	send_mark1  : out std_logic;
	send_mark2  : out std_logic;
	send_mark3  : out std_logic;
	send_mark4  : out std_logic;	
    da_out      : out std_logic_vector(15 downto 0)
);

end DA_SCAN_OUT;

architecture Behavioral of DA_SCAN_OUT is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

type sc_state_t is (sc_start_s,wait_update,send_data,wait_send,check_data);
signal sc_state : sc_state_t;
signal cnt: integer range 0 to 5;



begin
process(reset,clk)
	begin
		if reset='0' then
			sc_state	<=sc_start_s;
			send_mark1<='0';send_mark2<='0';send_mark3<='0';send_mark4<='0';da_out<=(others=>'0');
			cnt         <=0;
			da1_data<=x"0800";da2_data<=x"0800";da3_data<=x"0800";da4_data<=x"0800";
		else
            if clk='1' and clk'event then
				case sc_state is 	
                    when sc_start_s =>
					    send_mark1<='0';send_mark2<='0';send_mark3<='0';send_mark4<='0';
						if s_edge ='1' then 
							sc_state<=wait_update;
							cnt<=0;
						end if;
					when wait_update=>
					    if cnt>=4 then 
						    cnt<=0;sc_state<=send_data;
						else
						    cnt<=cnt+1;
					    end if;
                    when send_data=>
					    if cnt=0 then da_out<=da_out1;da1_data<=da_out1;send_mark1<='1'; end if;
						if cnt=1 then da_out<=da_out2;da2_data<=da_out2;send_mark2<='1'; end if;
						if cnt=2 then da_out<=da_out3;da3_data<=da_out3;send_mark3<='1'; end if;
						if cnt=3 then da_out<=da_out4;da4_data<=da_out4;send_mark4<='1'; end if;						
					    sc_state<=wait_send;						
					when wait_send=>
					    if send_over='1' then 						    
						    sc_state<=check_data;cnt<=cnt+1;
							send_mark1<='0';send_mark2<='0';send_mark3<='0';send_mark4<='0';
						end if;
					 when check_data =>
					    if cnt>=4 then 
					        sc_state<=sc_start_s;
						else
						    sc_state<=send_data;
						end if;
					when others =>
                        sc_state<=sc_start_s;
				end case;
			end if;
		end if;
    end process;

end Behavioral;

