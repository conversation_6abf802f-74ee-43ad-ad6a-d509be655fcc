----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    00:48:54 11/14/2022 
-- Design Name: 
-- Module Name:    DA_NORMAL - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity DA_NORMAL is
Port ( 	
        clk			: in std_logic;
		reset 		: in std_logic;
		send_over   : in std_logic;
		busy_flag   : in std_logic;
--		us_edge     : in std_logic;
		
		mode        : in std_logic;
		da_num  :in std_logic_vector(7 downto 0);
		da_data :in std_logic_vector(15 downto 0);
		da_set  :in std_logic;
		
		send_mark1  : out std_logic;
	    send_mark2  : out std_logic;
	    send_mark3  : out std_logic;
	    send_mark4  : out std_logic;	
        da_out      : out std_logic_vector(15 downto 0);	
		
		da_out1 :out std_logic_vector(15 downto 0);
		da_out2 :out std_logic_vector(15 downto 0);
		da_out3 :out std_logic_vector(15 downto 0);
		da_out4 :out std_logic_vector(15 downto 0)
	);
end DA_NORMAL;

architecture Behavioral of DA_NORMAL is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

type t_states is (s_start,s_read,s_trans);
signal state: t_states;
signal data_out :std_logic_vector(15 downto 0);
signal data_num  : std_logic_vector(7 downto 0);

begin

process(reset,clk)
	begin
		if reset='0' then
			send_mark1<='0';send_mark2<='0';send_mark3<='0';send_mark4<='0';data_out<=(others=>'0');data_num<=(others=>'0');
			da_out<=(others=>'0');da_out1<=(others=>'0');da_out2<=(others=>'0');da_out3<=(others=>'0');da_out4<=(others=>'0');
			state<=s_start; 
		else
			if clk='1' and clk'event then
			    if da_set='1' then 
				    state<=s_start; data_num<=da_num; data_out<=da_data;
				else
				    case state is
				        when s_start=>
				    		state<=s_read; 
				    	-- when s_wait =>
				    		-- if da_set='1' then 
				    		    -- state<=s_read; data_num<=da_num; data_out<=da_data;
				    		-- end if;
				    	when s_read=>
				    	    if busy_flag='0' and mode='0' then 
				    		    if data_num=x"01" then  send_mark1<='1'; da_out<=data_out; da_out1<=data_out; 
				        	    elsif da_num=x"02" then  send_mark2<='1';da_out<=data_out;da_out2<=data_out;
				        	    elsif da_num=x"03" then  send_mark3<='1';da_out<=data_out;da_out3<=data_out;
				        	    elsif da_num=x"04" then  send_mark4<='1';da_out<=data_out;da_out4<=data_out;
				        	    elsif da_num=x"05" then  send_mark1<='1';send_mark2<='1';send_mark3<='1';send_mark4<='1'; da_out<=data_out;
				        	        da_out1<=data_out;da_out2<=data_out;da_out3<=data_out;da_out4<=data_out;
				        	    else  send_mark1<='0';send_mark2<='0';send_mark3<='0';send_mark4<='0';
				                end if;	
				    		    state<=s_trans; 
				    		end if;
                        when  s_trans=>
                            if send_over='1' then 
                                state<=s_start; 
                            end if;
                        when others =>state<=s_start;	
                    end case;
				end if;
            end if;
        end if;
	end process;	
						
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	

end Behavioral;

