`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    21:52:50 12/26/2013 
// Design Name: 
// Module Name:    serdes 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module serdes5x_pmu(clk,en,data_in,data_out,rst,valid);
input data_in;//input [1:0]data_in;
input  en;//input [1:0] en;
input rst;
input clk;
output [9:0] data_out;
output valid;

//reg [3:0] count1;
reg [9:0] out;
reg [3:0] count;
reg [9:0] data_out;
reg valid;
//counter and output
always @(posedge clk or negedge rst) begin
	if (!rst) begin
		count <= 4'b0000;
	end
	else 
	if (en) begin
			if(count>=4'b1001) begin
			count <= 4'b0000;
			end
		else
			count <= count + 4'b0001;	
	end
end

always @(posedge clk or negedge rst) begin
	if (!rst) begin
			valid <= 1'b0;
			data_out <= 10'b0000_0000_00;
			end
	else if(count>=4'b1001) begin
			valid <= 1'b1;
			data_out <= out;
			end
	else 
			valid <= 1'b0;
			
end

	
//shifter
always @(posedge clk or negedge rst) begin
	if (!rst) 
		out <= 10'b0000_0000_00;
	else 
	if (en)
		out <= {out,data_in};
	end

endmodule
