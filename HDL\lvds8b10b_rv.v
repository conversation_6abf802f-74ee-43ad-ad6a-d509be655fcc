`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    08:41:08 12/13/2023 
// Design Name: 
// Module Name:    lvds8b10b_rv 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module lvds8b10b_rv(
input clk,
input clk_100M,
input reset,
input soft_reset,
input RX_line,

input	  fifo_rd_en,
output [12:0]	  fifo_rd_data_count,
output [7:0]	  fifo_data,
output	  fifo_empty,
output	  fifo_prog_full,
	  
output [7:0]	  frame_num_o,
output [7:0]	  checksum_err_o
    );


endmodule
