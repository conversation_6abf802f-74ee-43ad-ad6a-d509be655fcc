//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    13:31:10 04/25/2017 
// Design Name: 
// Module Name:    caiyang1 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module x_cdr5x(clk5x,rst,seri_dat,cmr_dat,cmr_dat_vld,false_detect);

	input clk5x;
	input rst;
	input seri_dat;

	output cmr_dat;
	output cmr_dat_vld;
	output false_detect;
		 
	reg	seri_dat_d1;
	reg	seri_dat_d2;
	reg	seri_dat_d3;

	reg	seri_dat_vld;
	reg	seri_dat_en;

	reg	[2:0]	cnt;
	reg	[4:0]cnt_detect ;
	reg	false_detect;

//reg	[9:0]	par_dat;


assign cmr_dat = seri_dat_vld ;
assign cmr_dat_vld = seri_dat_en ;


always@(posedge clk5x or negedge rst)begin//always@(posedge clk5x or posedge rst)begin
	if(!rst)begin
		seri_dat_d1 <= 1'b0;
		seri_dat_d2 <= 1'b0;
		seri_dat_d3 <= 1'b0;
//		cnt_reg		<= 3'b000;   //test
		
	end
	else begin
		seri_dat_d1 <= seri_dat;
		seri_dat_d2 <= seri_dat_d1;
		seri_dat_d3 <= seri_dat_d2;
//		cnt_reg     <= cnt ;		//test
	end
end



always@(posedge clk5x or negedge rst)begin//always@(posedge clk5x or posedge rst)begin
	if(!rst)begin
		cnt <= 3'd0;
	end
	else begin
		if(seri_dat_d3 ^ seri_dat_d2) begin
			cnt <= 3'd0;
		end
		else if( cnt == 3'd4 ) begin
			cnt <= 3'd0;
		end
		else begin	
			cnt <= cnt + 1'b1;
		end
	end
end


always@(posedge clk5x or negedge rst)begin//always@(posedge clk5x or posedge rst)begin
	if(!rst)begin
		cnt_detect <= 5'd0;
		false_detect <= 1'd0;
	end
	else begin
		if(seri_dat_d3 ^ seri_dat_d2) begin
			cnt_detect <= 5'd0;
			false_detect <= 1'd0;
		end
		else if( cnt_detect >= 5'd26 ) begin
			false_detect <= 1'd1;
		end
		else begin	
			cnt_detect <= cnt_detect + 5'b1;
			false_detect <= 1'd0;
		end
	end
end


always@(posedge clk5x or negedge rst)begin //always@(posedge clk5x or posedge rst)begin
	if(!rst)begin
		seri_dat_vld <= 1'b0;
		seri_dat_en <= 1'b0;
	end
	else begin
		if( cnt == 3'd2 ) begin
			seri_dat_vld <= seri_dat_d3;
			seri_dat_en <= 1'b1;
		end
		else begin
			seri_dat_vld <= 1'b0;
			seri_dat_en <= 1'b0;
		end
	end
end


endmodule
