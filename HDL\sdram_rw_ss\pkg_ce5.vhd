--	Package File Template
--
--	Purpose: This package defines supplemental types, subtypes, 
--		 constants, and functions 


library IEEE;
use IEEE.STD_LOGIC_1164.all;

package pkg_ce5 is


--SDRAM control command
  constant NOP_COMMAND         : std_logic_vector(2 downto 0) := "000";
  constant WRITE_COMMAND       : std_logic_vector(2 downto 0) := "001";
  constant READ_COMMAND        : std_logic_vector(2 downto 0) := "010";
  constant REFRESH_COMMAND     : std_logic_vector(2 downto 0) := "011";
  constant SR_ENTER_COMMAND    : std_logic_vector(2 downto 0) := "100";
  constant SR_EXIT_COMMAND     : std_logic_vector(2 downto 0) := "101";

-------for DWT-------  
--  constant fifo_data_bus_width_C : integer := 16;
--  constant IMG_WIDTH_C : integer := 2048;
--  constant IMG_HEIGHT_C : integer := 2048;
--  constant sdram_data_bus_width : integer := 16;
--  constant dwt_wr_length : integer := 224;
--  constant dwt_rd_length : integer := 224;
---------------------

--  constant SDRAM_READ_CYCLE : std_logic_vector(7 downto 0) := x"A0";

end pkg_ce5;


