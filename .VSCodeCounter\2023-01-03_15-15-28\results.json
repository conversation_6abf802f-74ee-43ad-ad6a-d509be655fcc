{"file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Buffer_RAM0.v": {"language": "Verilog", "code": 32, "comment": 42, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Buffer_RAM1.v": {"language": "Verilog", "code": 32, "comment": 39, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/apart_data.v": {"language": "Verilog", "code": 95, "comment": 23, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/bram_ip.v": {"language": "Verilog", "code": 44, "comment": 25, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/bram_cntrl.v": {"language": "Verilog", "code": 146, "comment": 21, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/body_top.v": {"language": "Verilog", "code": 102, "comment": 20, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/blk_num_ram_ctrl.v": {"language": "Verilog", "code": 56, "comment": 22, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/bit_extract.v": {"language": "Verilog", "code": 63, "comment": 19, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CMD_PROCESS.vhd": {"language": "VHDL", "code": 281, "comment": 35, "blank": 62}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/blocking_c.v": {"language": "Verilog", "code": 519, "comment": 27, "blank": 82}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CMD_REV.v": {"language": "Verilog", "code": 294, "comment": 81, "blank": 39}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/bram_coef_wr_cntrl.v": {"language": "Verilog", "code": 562, "comment": 33, "blank": 43}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CMD_ANALY.vhd": {"language": "VHDL", "code": 594, "comment": 45, "blank": 60}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/bit_add.v": {"language": "Verilog", "code": 1196, "comment": 25, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CLK_2MHz.vhd": {"language": "VHDL", "code": 41, "comment": 37, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CLK_1s.vhd": {"language": "VHDL", "code": 107, "comment": 18, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CLK_1MHz.vhd": {"language": "VHDL", "code": 56, "comment": 23, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/clk_manage.v": {"language": "Verilog", "code": 25, "comment": 15, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CMD_CHECK.v": {"language": "Verilog", "code": 296, "comment": 62, "blank": 34}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Code.v": {"language": "Verilog", "code": 520, "comment": 0, "blank": 50}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CODER_RDAT_top.v": {"language": "Verilog", "code": 479, "comment": 25, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/checksum.v": {"language": "Verilog", "code": 68, "comment": 17, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/col_lift.v": {"language": "Verilog", "code": 69, "comment": 37, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ce7_comp_top.v": {"language": "Verilog", "code": 678, "comment": 89, "blank": 115}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/coder_wr_sdram.v": {"language": "Verilog", "code": 638, "comment": 45, "blank": 52}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CODER_WDAT_top.v": {"language": "Verilog", "code": 179, "comment": 19, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/col_lift_ctrl_lev1.v": {"language": "Verilog", "code": 615, "comment": 29, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/col_lift_pre.v": {"language": "Verilog", "code": 74, "comment": 28, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/col_trans_lev1.v": {"language": "Verilog", "code": 395, "comment": 31, "blank": 40}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CODER_top.v": {"language": "Verilog", "code": 174, "comment": 21, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/col_ram_ctrl.v": {"language": "Verilog", "code": 102, "comment": 27, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/coder_rd_sdram.v": {"language": "Verilog", "code": 735, "comment": 45, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/asyn_dpram_ip.v": {"language": "Verilog", "code": 46, "comment": 25, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/col_lift_ctrl_lev234.v": {"language": "Verilog", "code": 919, "comment": 30, "blank": 57}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/assembly_direct.v": {"language": "Verilog", "code": 72, "comment": 35, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/col_trans_lev234.v": {"language": "Verilog", "code": 443, "comment": 30, "blank": 46}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/assembly_comp.v": {"language": "Verilog", "code": 271, "comment": 67, "blank": 32}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Create_Buffer0.v": {"language": "Verilog", "code": 260, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Create_Buffer1.v": {"language": "Verilog", "code": 261, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/COREUART_summary.html": {"language": "HTML", "code": 66, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/cpu_top.v": {"language": "Verilog", "code": 321, "comment": 25, "blank": 34}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/CUT_top.v": {"language": "Verilog", "code": 191, "comment": 19, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/compare.v": {"language": "Verilog", "code": 186, "comment": 30, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/COMP_TOP.v": {"language": "Verilog", "code": 418, "comment": 335, "blank": 48}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/data_buffer_new.v": {"language": "Verilog", "code": 168, "comment": 6, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/comp_rd_sdram.v": {"language": "Verilog", "code": 335, "comment": 26, "blank": 46}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DATA_PROCESS.vhd": {"language": "VHDL", "code": 442, "comment": 30, "blank": 81}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DA_DRV.vhd": {"language": "VHDL", "code": 192, "comment": 32, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Create_Layer.v": {"language": "Verilog", "code": 387, "comment": 6, "blank": 37}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dat_receive.v": {"language": "Verilog", "code": 110, "comment": 16, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DA_CHANGE.vhd": {"language": "VHDL", "code": 158, "comment": 28, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DA_PROCESS.vhd": {"language": "VHDL", "code": 333, "comment": 26, "blank": 68}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DA_NORMAL.vhd": {"language": "VHDL", "code": 65, "comment": 26, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Create_Value.v": {"language": "Verilog", "code": 75, "comment": 1, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DA_SCAN_OUT.vhd": {"language": "VHDL", "code": 65, "comment": 26, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Create_Tag_Tree.v": {"language": "Verilog", "code": 109, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DA_SCAN.vhd": {"language": "VHDL", "code": 91, "comment": 28, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/cuter.v": {"language": "Verilog", "code": 244, "comment": 26, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DA_SCAN_PROCESS.vhd": {"language": "VHDL", "code": 143, "comment": 26, "blank": 39}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ComControl_summary.html": {"language": "HTML", "code": 67, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Create_Layer_Controller.v": {"language": "Verilog", "code": 433, "comment": 0, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DCM1_arwz.ucf": {"language": "vivado ucf", "code": 16, "comment": 3, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DA_SCH.vhd": {"language": "VHDL", "code": 71, "comment": 26, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DCM1.vhd": {"language": "VHDL", "code": 77, "comment": 22, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DCM0_arwz.ucf": {"language": "vivado ucf", "code": 16, "comment": 3, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DWT_top.v": {"language": "Verilog", "code": 104, "comment": 21, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DCM0.vhd": {"language": "VHDL", "code": 78, "comment": 22, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dwt_trans.v": {"language": "Verilog", "code": 291, "comment": 30, "blank": 49}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dual_port_bram.v": {"language": "Verilog", "code": 23, "comment": 21, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dpram32b.v": {"language": "Verilog", "code": 35, "comment": 59, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DIV_TIME_EDGE.vhd": {"language": "VHDL", "code": 272, "comment": 32, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DM_rv.v": {"language": "Verilog", "code": 29, "comment": 22, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/D_C7_FPGA.vhd": {"language": "VHDL", "code": 661, "comment": 166, "blank": 161}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dwt_store.v": {"language": "Verilog", "code": 288, "comment": 31, "blank": 35}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/direct_rd_sdram.v": {"language": "Verilog", "code": 313, "comment": 37, "blank": 35}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcy_sci.v": {"language": "Verilog", "code": 45, "comment": 21, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DECODE.v": {"language": "Verilog", "code": 132, "comment": 23, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/H_EFI_DATA_PROCESS1.vhd": {"language": "VHDL", "code": 79, "comment": 5, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/head_top.v": {"language": "Verilog", "code": 137, "comment": 21, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Leaf_Address.v": {"language": "Verilog", "code": 41, "comment": 0, "blank": 4}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/head_len.v": {"language": "Verilog", "code": 164, "comment": 16, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/head_body_add.v": {"language": "Verilog", "code": 162, "comment": 19, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/LC_PROCESS.vhd": {"language": "VHDL", "code": 166, "comment": 26, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Kcoef_select.v": {"language": "Verilog", "code": 138, "comment": 23, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Leaf_RAM0.v": {"language": "Verilog", "code": 25, "comment": 20, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/row_lift.v": {"language": "Verilog", "code": 68, "comment": 24, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/K_scale_ctrl.v": {"language": "Verilog", "code": 294, "comment": 22, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/K_scale.v": {"language": "Verilog", "code": 251, "comment": 32, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ROM_162x8.v": {"language": "Verilog", "code": 18, "comment": 21, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/CLKManage.v": {"language": "Verilog", "code": 60, "comment": 39, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/reg_top_xs_cntrl.v": {"language": "Verilog", "code": 52, "comment": 19, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/row_lift_ctrl.v": {"language": "Verilog", "code": 298, "comment": 37, "blank": 38}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Receive_uart_summary.html": {"language": "HTML", "code": 67, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Result_Store.v": {"language": "Verilog", "code": 743, "comment": 18, "blank": 77}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/rd_MQ_sdram.v": {"language": "Verilog", "code": 173, "comment": 21, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/row_lift_ctrl_lev234.v": {"language": "Verilog", "code": 544, "comment": 41, "blank": 52}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/rd_one_pack_ctrl.v": {"language": "Verilog", "code": 172, "comment": 22, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/rd_MQ_dpram.v": {"language": "Verilog", "code": 331, "comment": 19, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Receive_8b10b_summary.html": {"language": "HTML", "code": 67, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/rd_dpram.v": {"language": "Verilog", "code": 533, "comment": 33, "blank": 62}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/row_trans.v": {"language": "Verilog", "code": 235, "comment": 27, "blank": 31}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ram_LL_adjust.v": {"language": "Verilog", "code": 42, "comment": 32, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ram_col_lift.v": {"language": "Verilog", "code": 42, "comment": 30, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/CCSDS_PACK.vhd": {"language": "VHDL", "code": 216, "comment": 27, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/RST.v": {"language": "Verilog", "code": 24, "comment": 19, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/rst_manage.v": {"language": "Verilog", "code": 37, "comment": 15, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/rdblk3_wrblkband.v": {"language": "Verilog", "code": 882, "comment": 38, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/processing_analysis.v": {"language": "Verilog", "code": 380, "comment": 22, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/row_trans_lev234.v": {"language": "Verilog", "code": 316, "comment": 32, "blank": 40}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/rw_sdramcntrl.vhd": {"language": "VHDL", "code": 1036, "comment": 76, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pre_wr_sdram_top.v": {"language": "Verilog", "code": 216, "comment": 23, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pre_wr_info_dpram.v": {"language": "Verilog", "code": 251, "comment": 30, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pre_wr_img_dpram.v": {"language": "Verilog", "code": 248, "comment": 34, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pre_wr_sdram.v": {"language": "Verilog", "code": 744, "comment": 56, "blank": 48}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/AD_ctrl.vhd": {"language": "VHDL", "code": 343, "comment": 24, "blank": 30}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/ComControl.vhd": {"language": "VHDL", "code": 300, "comment": 30, "blank": 41}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/schedule.v": {"language": "Verilog", "code": 324, "comment": 59, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pre_rd_fifo.v": {"language": "Verilog", "code": 64, "comment": 17, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pre_wr_direct_dpram.v": {"language": "Verilog", "code": 230, "comment": 30, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pre_wr_config_fifo.v": {"language": "Verilog", "code": 157, "comment": 23, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/CPU_IF.vhd": {"language": "VHDL", "code": 142, "comment": 23, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pkt_wr_fifo.v": {"language": "Verilog", "code": 46, "comment": 16, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/planAhead.ngc2edif.log": {"language": "Log", "code": 52, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/PK_top.v": {"language": "Verilog", "code": 218, "comment": 24, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pkt_info_analysis.v": {"language": "Verilog", "code": 113, "comment": 18, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pkg_ce5.vhd": {"language": "VHDL", "code": 14, "comment": 13, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/sdram_rw_ss/sdram_contrl_top.vhd": {"language": "VHDL", "code": 26, "comment": 29, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pkt_pre_top.v": {"language": "Verilog", "code": 173, "comment": 34, "blank": 28}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/sdram_rw_ss/sdram_write.vhd": {"language": "VHDL", "code": 115, "comment": 31, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pass_len_code.v": {"language": "Verilog", "code": 412, "comment": 19, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/tb.v": {"language": "Verilog", "code": 154, "comment": 28, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/sdram_rw_ss/sdram_read.vhd": {"language": "VHDL", "code": 106, "comment": 34, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/TAG_TREE_top.v": {"language": "Verilog", "code": 227, "comment": 9, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/T2_top.v": {"language": "Verilog", "code": 205, "comment": 21, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Tag_Tree_RAM_Ctrl.v": {"language": "Verilog", "code": 127, "comment": 199, "blank": 30}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/tbb.v": {"language": "Verilog", "code": 147, "comment": 28, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/sdram_rw_ss/rw_sdramcntrl.vhd": {"language": "VHDL", "code": 1036, "comment": 70, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/syn_dpram_ip.v": {"language": "Verilog", "code": 76, "comment": 25, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SYN_BC.v": {"language": "Verilog", "code": 227, "comment": 21, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/sdram_rw_ss/pkg_ce5.vhd": {"language": "VHDL", "code": 10, "comment": 14, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/T1_top.v": {"language": "Verilog", "code": 286, "comment": 0, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/subband_apart.v": {"language": "Verilog", "code": 107, "comment": 25, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/stack.v": {"language": "Verilog", "code": 100, "comment": 31, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/slope_thrs_calc.v": {"language": "Verilog", "code": 68, "comment": 19, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/slope_rd_dpram_ctrl.v": {"language": "Verilog", "code": 200, "comment": 4, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/slope_top_new.v": {"language": "Verilog", "code": 179, "comment": 27, "blank": 39}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/slope_rd_dpram.v": {"language": "Verilog", "code": 23, "comment": 22, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/state_reg.v": {"language": "Verilog", "code": 212, "comment": 32, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/UARTtoRS01.vhd": {"language": "VHDL", "code": 227, "comment": 32, "blank": 37}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/StateUpdate_and_ParameterPrepare.v": {"language": "Verilog", "code": 527, "comment": 0, "blank": 63}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/serdes_5x.v": {"language": "Verilog", "code": 41, "comment": 14, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/sdram_write.vhd": {"language": "VHDL", "code": 115, "comment": 31, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SEND_PACK.vhd": {"language": "VHDL", "code": 219, "comment": 33, "blank": 54}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/slope_calc_m.v": {"language": "Verilog", "code": 377, "comment": 34, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/UART/Tx_async.vhd": {"language": "VHDL", "code": 160, "comment": 27, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/UART/Rx_async.vhd": {"language": "VHDL", "code": 272, "comment": 54, "blank": 32}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/UART/Clock_gen.vhd": {"language": "VHDL", "code": 50, "comment": 23, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/UART/COREUART.vhd": {"language": "VHDL", "code": 100, "comment": 23, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/slope_control.v": {"language": "Verilog", "code": 402, "comment": 52, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/wr_blk3_dpram.v": {"language": "Verilog", "code": 177, "comment": 21, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/TOP_summary.xml": {"language": "XML", "code": 5, "comment": 5, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/TOP_summary.html": {"language": "HTML", "code": 234, "comment": 0, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/wr_blen_bram_ctrl.v": {"language": "Verilog", "code": 44, "comment": 29, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/dual_port_bram/_primary.vhd": {"language": "VHDL", "code": 21, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/write_to_ram.v": {"language": "Verilog", "code": 464, "comment": 41, "blank": 39}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/wr_blocks_c/_primary.vhd": {"language": "VHDL", "code": 45, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/wr_slope_dpram/_primary.vhd": {"language": "VHDL", "code": 16, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/write_to_sdram.v": {"language": "Verilog", "code": 1520, "comment": 56, "blank": 79}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/tbb/_primary.vhd": {"language": "VHDL", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/TOP_usage.xml": {"language": "XML", "code": 3279, "comment": 5, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/zerobit_out/_primary.vhd": {"language": "VHDL", "code": 28, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/wr_dpram/_primary.vhd": {"language": "VHDL", "code": 18, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/wr_b_dpram/_primary.vhd": {"language": "VHDL", "code": 16, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/wr_blk3_dpram/_primary.vhd": {"language": "VHDL", "code": 24, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/wr_blen_bram_ctrl/_primary.vhd": {"language": "VHDL", "code": 17, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/write_to_sdram/_primary.vhd": {"language": "VHDL", "code": 65, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/stack/_primary.vhd": {"language": "VHDL", "code": 27, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/uart_tx/_primary.vhd": {"language": "VHDL", "code": 15, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/write_to_ram/_primary.vhd": {"language": "VHDL", "code": 37, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/time_guard/_primary.vhd": {"language": "VHDL", "code": 12, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/tb/_primary.vhd": {"language": "VHDL", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/truncate/_primary.vhd": {"language": "VHDL", "code": 33, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/tb_v/_primary.vhd": {"language": "VHDL", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/state_reg/_primary.vhd": {"language": "VHDL", "code": 24, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/syn_dpram_ip/_primary.vhd": {"language": "VHDL", "code": 32, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/subband_apart/_primary.vhd": {"language": "VHDL", "code": 35, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/rdblk3_wrblkband/_primary.vhd": {"language": "VHDL", "code": 74, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/slope_top_new/_primary.vhd": {"language": "VHDL", "code": 19, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/slope_thrs_calc/_primary.vhd": {"language": "VHDL", "code": 15, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/slope_rd_dpram_ctrl/_primary.vhd": {"language": "VHDL", "code": 17, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/schedule/_primary.vhd": {"language": "VHDL", "code": 54, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/slope_rd_dpram/_primary.vhd": {"language": "VHDL", "code": 21, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/serdes_5x/_primary.vhd": {"language": "VHDL", "code": 12, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/slope_control/_primary.vhd": {"language": "VHDL", "code": 54, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/slope_calc_m/_primary.vhd": {"language": "VHDL", "code": 14, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/sdram_contrl_top_fifo/_primary.vhd": {"language": "VHDL", "code": 28, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/sdram2dpram/_primary.vhd": {"language": "VHDL", "code": 43, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/rst_manage/_primary.vhd": {"language": "VHDL", "code": 14, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/row_lift_ctrl_lev234/_primary.vhd": {"language": "VHDL", "code": 62, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/row_trans/_primary.vhd": {"language": "VHDL", "code": 34, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/row_trans_lev234/_primary.vhd": {"language": "VHDL", "code": 45, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/reg_top_xs_cntrl/_primary.vhd": {"language": "VHDL", "code": 22, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/row_lift/_primary.vhd": {"language": "VHDL", "code": 26, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/row_lift_ctrl/_primary.vhd": {"language": "VHDL", "code": 43, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/rd_one_pack_ctrl/_primary.vhd": {"language": "VHDL", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/rd_dpram/_primary.vhd": {"language": "VHDL", "code": 49, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/rd_%40m%40q_sdram/_primary.vhd": {"language": "VHDL", "code": 23, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/rd_%40m%40q_dpram/_primary.vhd": {"language": "VHDL", "code": 21, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/ram_%40l%40l_adjust/_primary.vhd": {"language": "VHDL", "code": 26, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/ram_col_lift/_primary.vhd": {"language": "VHDL", "code": 26, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pack_head_%40i%40n%40p%40l_top/_primary.vhd": {"language": "VHDL", "code": 17, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/processing_analysis/_primary.vhd": {"language": "VHDL", "code": 52, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pre_wr_sdram_top/_primary.vhd": {"language": "VHDL", "code": 46, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pre_wr_config_fifo/_primary.vhd": {"language": "VHDL", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pre_wr_sdram/_primary.vhd": {"language": "VHDL", "code": 45, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pre_wr_info_dpram/_primary.vhd": {"language": "VHDL", "code": 26, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pre_wr_img_dpram/_primary.vhd": {"language": "VHDL", "code": 26, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pre_wr_direct_dpram/_primary.vhd": {"language": "VHDL", "code": 26, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pkt_wr_fifo/_primary.vhd": {"language": "VHDL", "code": 13, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pre_rd_fifo/_primary.vhd": {"language": "VHDL", "code": 15, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pkt_info_analysis/_primary.vhd": {"language": "VHDL", "code": 16, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pkt_pre_top/_primary.vhd": {"language": "VHDL", "code": 37, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pass_len_code/_primary.vhd": {"language": "VHDL", "code": 36, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/param_pre/_primary.vhd": {"language": "VHDL", "code": 24, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pack_head_char/_primary.vhd": {"language": "VHDL", "code": 32, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pack_head_%40i%40n%40p%40l_mux/_primary.vhd": {"language": "VHDL", "code": 36, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/pack_control/_primary.vhd": {"language": "VHDL", "code": 70, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/lev_sel/_primary.vhd": {"language": "VHDL", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/packing_top/_primary.vhd": {"language": "VHDL", "code": 30, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/packing_sel/_primary.vhd": {"language": "VHDL", "code": 32, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/mul_cut/_primary.vhd": {"language": "VHDL", "code": 22, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/mux_apart_4level/_primary.vhd": {"language": "VHDL", "code": 42, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/packing/_primary.vhd": {"language": "VHDL", "code": 44, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/multiply_ip/_primary.vhd": {"language": "VHDL", "code": 14, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/mult_pepi/_primary.vhd": {"language": "VHDL", "code": 18, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/mselen_input_buffer/_primary.vhd": {"language": "VHDL", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/mid_top/_primary.vhd": {"language": "VHDL", "code": 35, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/lvds_8b10b_decoder/_primary.vhd": {"language": "VHDL", "code": 12, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/lvde_send/_primary.vhd": {"language": "VHDL", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/lev_2_3_4/_primary.vhd": {"language": "VHDL", "code": 35, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/lift_pro_element/_primary.vhd": {"language": "VHDL", "code": 31, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/interactive_contrl/_primary.vhd": {"language": "VHDL", "code": 14, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/lev_1/_primary.vhd": {"language": "VHDL", "code": 27, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/info_data_separate/_primary.vhd": {"language": "VHDL", "code": 36, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/head_body_add/_primary.vhd": {"language": "VHDL", "code": 21, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/head_len/_primary.vhd": {"language": "VHDL", "code": 16, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/head_top/_primary.vhd": {"language": "VHDL", "code": 32, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/dpram32b/_primary.vhd": {"language": "VHDL", "code": 25, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/glbl/_primary.vhd": {"language": "VHDL", "code": 11, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/gen_frame_syn/_primary.vhd": {"language": "VHDL", "code": 51, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/fast_look_new_new/_primary.vhd": {"language": "VHDL", "code": 17, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/frame_head_out/_primary.vhd": {"language": "VHDL", "code": 15, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/dwt_store/_primary.vhd": {"language": "VHDL", "code": 40, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/framing_c/_primary.vhd": {"language": "VHDL", "code": 59, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/encode_lenth/_primary.vhd": {"language": "VHDL", "code": 15, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/dwt_trans/_primary.vhd": {"language": "VHDL", "code": 38, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/data_buffer_new/_primary.vhd": {"language": "VHDL", "code": 29, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/dat_receive/_primary.vhd": {"language": "VHDL", "code": 15, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/dcshift/_primary.vhd": {"language": "VHDL", "code": 16, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/cpu_top/_primary.vhd": {"language": "VHDL", "code": 23, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/cuter/_primary.vhd": {"language": "VHDL", "code": 39, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/direct_rd_sdram/_primary.vhd": {"language": "VHDL", "code": 46, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/col_trans_lev234/_primary.vhd": {"language": "VHDL", "code": 44, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/compare/_primary.vhd": {"language": "VHDL", "code": 30, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/col_ram_ctrl/_primary.vhd": {"language": "VHDL", "code": 32, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/comp_rd_sdram/_primary.vhd": {"language": "VHDL", "code": 48, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/col_lift_pre/_primary.vhd": {"language": "VHDL", "code": 28, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/col_trans_lev1/_primary.vhd": {"language": "VHDL", "code": 38, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/coder_wr_sdram/_primary.vhd": {"language": "VHDL", "code": 57, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/col_lift_ctrl_lev234/_primary.vhd": {"language": "VHDL", "code": 86, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/col_lift_ctrl_lev1/_primary.vhd": {"language": "VHDL", "code": 65, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/coder_rd_sdram/_primary.vhd": {"language": "VHDL", "code": 37, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/col_lift/_primary.vhd": {"language": "VHDL", "code": 35, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/cmd_check/_primary.vhd": {"language": "VHDL", "code": 17, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/checksum/_primary.vhd": {"language": "VHDL", "code": 13, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/ce7_comp_top/_primary.vhd": {"language": "VHDL", "code": 31, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/blocking_c/_primary.vhd": {"language": "VHDL", "code": 47, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/bram_ip/_primary.vhd": {"language": "VHDL", "code": 26, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40t2_top/_primary.vhd": {"language": "VHDL", "code": 31, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/bram_cntrl/_primary.vhd": {"language": "VHDL", "code": 66, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/blk_num_ram_ctrl/_primary.vhd": {"language": "VHDL", "code": 18, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/body_top/_primary.vhd": {"language": "VHDL", "code": 31, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/bram_coef_wr_cntrl/_primary.vhd": {"language": "VHDL", "code": 39, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40leaf_%40address/_primary.vhd": {"language": "VHDL", "code": 14, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/bit_extract/_primary.vhd": {"language": "VHDL", "code": 11, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/bit_add/_primary.vhd": {"language": "VHDL", "code": 36, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/assembly_direct/_primary.vhd": {"language": "VHDL", "code": 31, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/asyn_dpram_ip/_primary.vhd": {"language": "VHDL", "code": 27, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/assembly_comp/_primary.vhd": {"language": "VHDL", "code": 48, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/apart_data/_primary.vhd": {"language": "VHDL", "code": 25, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40t1_top/_primary.vhd": {"language": "VHDL", "code": 44, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40tag_%40tree_%40r%40a%40m_%40ctrl/_primary.vhd": {"language": "VHDL", "code": 41, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40t%40a%40g_%40t%40r%40e%40e_top/_primary.vhd": {"language": "VHDL", "code": 23, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40s%40y%40n_%40b%40c/_primary.vhd": {"language": "VHDL", "code": 26, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40t%40o%40p/_primary.vhd": {"language": "VHDL", "code": 69, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40state%40update_and_%40parameter%40prepare/_primary.vhd": {"language": "VHDL", "code": 51, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40result_%40store/_primary.vhd": {"language": "VHDL", "code": 35, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40r%40s%40t/_primary.vhd": {"language": "VHDL", "code": 8, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40r%40o%40m_162x8/_primary.vhd": {"language": "VHDL", "code": 17, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40parent_%40r%40a%40m0/_primary.vhd": {"language": "VHDL", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40p%40k_top/_primary.vhd": {"language": "VHDL", "code": 33, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40p2_refresh_new/_primary.vhd": {"language": "VHDL", "code": 18, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40m%40q_coder/_primary.vhd": {"language": "VHDL", "code": 18, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40output_%40b%40r%40a%40m/_primary.vhd": {"language": "VHDL", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40k_scale_ctrl/_primary.vhd": {"language": "VHDL", "code": 44, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40l%40l_ctrl/_primary.vhd": {"language": "VHDL", "code": 42, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40k_scale/_primary.vhd": {"language": "VHDL", "code": 40, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40kcoef_select/_primary.vhd": {"language": "VHDL", "code": 66, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40f%40i%40f%40o/_primary.vhd": {"language": "VHDL", "code": 21, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40encode_%40tag_%40tree/_primary.vhd": {"language": "VHDL", "code": 31, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40p3_byteout_new/_primary.vhd": {"language": "VHDL", "code": 16, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40encoder/_primary.vhd": {"language": "VHDL", "code": 27, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40leaf_%40r%40a%40m0/_primary.vhd": {"language": "VHDL", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_ila_pro_1/coregen.log": {"language": "Log", "code": 61, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40create_%40layer_%40controller/_primary.vhd": {"language": "VHDL", "code": 27, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_ila_pro_1/ila_pro_1.ucf": {"language": "vivado ucf", "code": 2, "comment": 14, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40encode_%40controller/_primary.vhd": {"language": "VHDL", "code": 32, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40create_%40buffer1/_primary.vhd": {"language": "VHDL", "code": 24, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40d%40w%40t_top/_primary.vhd": {"language": "VHDL", "code": 33, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_ila_pro_1/ila_pro_1.vhd": {"language": "VHDL", "code": 11, "comment": 17, "blank": 4}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_ila_pro_1/ila_pro_1.vho": {"language": "VHDL", "code": 11, "comment": 24, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40l%40l_adjust/_primary.vhd": {"language": "VHDL", "code": 37, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40create_%40buffer0/_primary.vhd": {"language": "VHDL", "code": 27, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40d%40m_rv/_primary.vhd": {"language": "VHDL", "code": 16, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40global_%40control/_primary.vhd": {"language": "VHDL", "code": 49, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40create_%40tag_%40tree/_primary.vhd": {"language": "VHDL", "code": 43, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40create_%40layer/_primary.vhd": {"language": "VHDL", "code": 42, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_icon_pro/coregen.log": {"language": "Log", "code": 61, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_ila_pro_0/coregen.log": {"language": "Log", "code": 61, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40d%40e%40c%40o%40d%40e/_primary.vhd": {"language": "VHDL", "code": 23, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_icon_pro/icon_pro.ucf": {"language": "vivado ucf", "code": 1, "comment": 9, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40c%40o%40d%40e%40r_%40w%40d%40a%40t_top/_primary.vhd": {"language": "VHDL", "code": 54, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_icon_pro/icon_pro.vhd": {"language": "VHDL", "code": 9, "comment": 17, "blank": 4}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40c%40o%40d%40e%40r_%40r%40d%40a%40t_top/_primary.vhd": {"language": "VHDL", "code": 44, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40c%40o%40m%40p_%40t%40o%40p/_primary.vhd": {"language": "VHDL", "code": 93, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40c%40o%40d%40e%40r_top/_primary.vhd": {"language": "VHDL", "code": 50, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40c%40m%40d_%40r%40e%40v/_primary.vhd": {"language": "VHDL", "code": 14, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40c%40u%40t_top/_primary.vhd": {"language": "VHDL", "code": 41, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40code/_primary.vhd": {"language": "VHDL", "code": 34, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40create_%40value/_primary.vhd": {"language": "VHDL", "code": 18, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_ila_pro_0/ila_pro_0.vhd": {"language": "VHDL", "code": 11, "comment": 17, "blank": 4}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_ila_pro_0/ila_pro_0.vho": {"language": "VHDL", "code": 11, "comment": 24, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_icon_pro/icon_pro.vho": {"language": "VHDL", "code": 7, "comment": 24, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/_ngo/cs_ila_pro_0/ila_pro_0.ucf": {"language": "vivado ucf", "code": 2, "comment": 14, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40c%40l%40k%40manage/_primary.vhd": {"language": "VHDL", "code": 14, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40a%40d%40d%40e%40r16%40b%40i%40t%40c_new/_primary.vhd": {"language": "VHDL", "code": 10, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/zerobit_out.v": {"language": "Verilog", "code": 463, "comment": 19, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40buffer_%40r%40a%40m0/_primary.vhd": {"language": "VHDL", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/wr_dpram.v": {"language": "Verilog", "code": 129, "comment": 19, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/wr_slope_dpram.v": {"language": "Verilog", "code": 88, "comment": 19, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/wr_b_dpram.v": {"language": "Verilog", "code": 201, "comment": 60, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xaw2vhdl.log": {"language": "Log", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xaw2verilog.log": {"language": "Log", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/webtalk_pn.xml": {"language": "XML", "code": 60, "comment": 3, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/wr_blocks_c.v": {"language": "Verilog", "code": 495, "comment": 28, "blank": 75}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/webtalk_impact.xml": {"language": "XML", "code": 55, "comment": 3, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/uart_tx.v": {"language": "Verilog", "code": 150, "comment": 43, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/truncate.v": {"language": "Verilog", "code": 445, "comment": 68, "blank": 40}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/tb_TOP.v": {"language": "Verilog", "code": 288, "comment": 34, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/TOP_envsettings.html": {"language": "HTML", "code": 550, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/work/%40buffer_%40r%40a%40m1/_primary.vhd": {"language": "VHDL", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/webtalk.log": {"language": "Log", "code": 12, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/tb_AD_ctrl.v": {"language": "Verilog", "code": 67, "comment": 28, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/TOP_xpa.log": {"language": "Log", "code": 714, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/SDRAM32x16_model.v": {"language": "Verilog", "code": 35, "comment": 19, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/Sci_RS422_TX.v": {"language": "Verilog", "code": 287, "comment": 33, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/TOP.ucf": {"language": "vivado ucf", "code": 240, "comment": 22, "blank": 40}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/time_guard.v": {"language": "Verilog", "code": 134, "comment": 13, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/time_ucf.ucf": {"language": "vivado ucf", "code": 5, "comment": 8, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/MT48LC128M4A2.v": {"language": "Verilog", "code": 761, "comment": 167, "blank": 125}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/sdram_schedule_v1_fifohuansdram.v": {"language": "Verilog", "code": 15, "comment": 19, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/TEST_PWR.vhd": {"language": "VHDL", "code": 34, "comment": 26, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/TIME_COUNTER.vhd": {"language": "VHDL", "code": 68, "comment": 38, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/tb_v.v": {"language": "Verilog", "code": 90, "comment": 59, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/SciTX.vhd": {"language": "VHDL", "code": 102, "comment": 23, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/TOP.v": {"language": "Verilog", "code": 203, "comment": 34, "blank": 38}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/decoder/serdes5x.v": {"language": "Verilog", "code": 45, "comment": 22, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/decoder/rx_module5x.v": {"language": "Verilog", "code": 53, "comment": 21, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/sdram_read.vhd": {"language": "VHDL", "code": 106, "comment": 36, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/SciReceive_uart.vhd": {"language": "VHDL", "code": 97, "comment": 26, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Sdram_Schedule.vhd": {"language": "VHDL", "code": 217, "comment": 31, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/decoder/decoder_8b10b_5x.v": {"language": "Verilog", "code": 288, "comment": 33, "blank": 37}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/decoder/x_cdr5x.v": {"language": "Verilog", "code": 81, "comment": 22, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/sdram_contrl_top_sim.v": {"language": "Verilog", "code": 29, "comment": 21, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/SciReceive_8b10b.vhd": {"language": "VHDL", "code": 72, "comment": 26, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/sdram_contrl_top_fifo.v": {"language": "Verilog", "code": 107, "comment": 23, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/decoder/comma_check_5x.v": {"language": "Verilog", "code": 180, "comment": 19, "blank": 31}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/SciData_Transmit.vhd": {"language": "VHDL", "code": 170, "comment": 25, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SC_SEND_CTRL.vhd": {"language": "VHDL", "code": 134, "comment": 106, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/sdram_contrl_top.vhd": {"language": "VHDL", "code": 1104, "comment": 78, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SciReceive_uart_summary.html": {"language": "HTML", "code": 67, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/sdram2dpram.v": {"language": "Verilog", "code": 480, "comment": 27, "blank": 43}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SciReceive_uart_envsettings.html": {"language": "HTML", "code": 400, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl06.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl07.vho": {"language": "VHDL", "code": 3, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl102.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl05.vho": {"language": "VHDL", "code": 19, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl101.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl04.vho": {"language": "VHDL", "code": 5, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl08.vho": {"language": "VHDL", "code": 22, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl100.vho": {"language": "VHDL", "code": 42, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl03.vho": {"language": "VHDL", "code": 157, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl09.vho": {"language": "VHDL", "code": 11, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl02.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl00.vho": {"language": "VHDL", "code": 3, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl104.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl12.vho": {"language": "VHDL", "code": 39, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl01.vho": {"language": "VHDL", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl13.vho": {"language": "VHDL", "code": 8, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl103.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl107.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl106.vho": {"language": "VHDL", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl105.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl108.vho": {"language": "VHDL", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl11.vho": {"language": "VHDL", "code": 9, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl32.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl109.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl31.vho": {"language": "VHDL", "code": 9, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl30.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl14.vho": {"language": "VHDL", "code": 40, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl126.vho": {"language": "VHDL", "code": 45, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl29.vho": {"language": "VHDL", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl125.vho": {"language": "VHDL", "code": 16, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl132.vho": {"language": "VHDL", "code": 21, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl10.vho": {"language": "VHDL", "code": 614, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl28.vho": {"language": "VHDL", "code": 264, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl137.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl41.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/encoeder_8b10b.v": {"language": "Verilog", "code": 94, "comment": 22, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl152.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl138.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl166.vho": {"language": "VHDL", "code": 3, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl145.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl56.vho": {"language": "VHDL", "code": 370, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl66.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl164.vho": {"language": "VHDL", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl65.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl165.vho": {"language": "VHDL", "code": 45, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl163.vho": {"language": "VHDL", "code": 24, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo.vhd": {"language": "VHDL", "code": 242, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl64.vho": {"language": "VHDL", "code": 49, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl63.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl67.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo.vho": {"language": "VHDL", "code": 29, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl167.vho": {"language": "VHDL", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl161.vho": {"language": "VHDL", "code": 21, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl160.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl68.vho": {"language": "VHDL", "code": 43, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl162.vho": {"language": "VHDL", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl62.vho": {"language": "VHDL", "code": 48, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl61.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl159.vho": {"language": "VHDL", "code": 5, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl158.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl170.vho": {"language": "VHDL", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl169.vho": {"language": "VHDL", "code": 3, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl71.vho": {"language": "VHDL", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl70.vho": {"language": "VHDL", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl69.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl72.vho": {"language": "VHDL", "code": 16, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl171.vho": {"language": "VHDL", "code": 12, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl60.vho": {"language": "VHDL", "code": 177, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl157.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl168.vho": {"language": "VHDL", "code": 23, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl59.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl156.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl173.vho": {"language": "VHDL", "code": 19, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl73.vho": {"language": "VHDL", "code": 12, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl75.vho": {"language": "VHDL", "code": 17, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl172.vho": {"language": "VHDL", "code": 469, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl155.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl79.vho": {"language": "VHDL", "code": 9, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl80.vho": {"language": "VHDL", "code": 8, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl174.vho": {"language": "VHDL", "code": 477, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl74.vho": {"language": "VHDL", "code": 28, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl81.vho": {"language": "VHDL", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl77.vho": {"language": "VHDL", "code": 12, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl78.vho": {"language": "VHDL", "code": 14, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl76.vho": {"language": "VHDL", "code": 29, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl58.vho": {"language": "VHDL", "code": 13, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl154.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl82.vho": {"language": "VHDL", "code": 20, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl57.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl84.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl153.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/k_encoder.v": {"language": "Verilog", "code": 87, "comment": 19, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl83.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl151.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl55.vho": {"language": "VHDL", "code": 14, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl85.vho": {"language": "VHDL", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl94.vho": {"language": "VHDL", "code": 19, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl95.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl96.vho": {"language": "VHDL", "code": 21, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl93.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/lvdstxmodule.v": {"language": "Verilog", "code": 89, "comment": 84, "blank": 41}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl91.vho": {"language": "VHDL", "code": 3, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl97.vho": {"language": "VHDL", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl92.vho": {"language": "VHDL", "code": 33, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl89.vho": {"language": "VHDL", "code": 3, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl90.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/usage_statistics_webtalk.html": {"language": "HTML", "code": 6170, "comment": 0, "blank": 758}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl87.vho": {"language": "VHDL", "code": 19, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl150.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl88.vho": {"language": "VHDL", "code": 388, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl86.vho": {"language": "VHDL", "code": 390, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl54.vho": {"language": "VHDL", "code": 374, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl53.vho": {"language": "VHDL", "code": 16, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl149.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/lddata_pkg.v": {"language": "Verilog", "code": 727, "comment": 189, "blank": 88}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl98.vho": {"language": "VHDL", "code": 23, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl148.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl52.vho": {"language": "VHDL", "code": 26, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl51.vho": {"language": "VHDL", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl147.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl99.vho": {"language": "VHDL", "code": 9, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl146.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl49.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl47.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl144.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl50.vho": {"language": "VHDL", "code": 304, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl143.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl46.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl142.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl48.vho": {"language": "VHDL", "code": 294, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl45.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl141.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl43.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl44.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl140.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl136.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl139.vho": {"language": "VHDL", "code": 5, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl42.vho": {"language": "VHDL", "code": 45, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl40.vho": {"language": "VHDL", "code": 326, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl135.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl39.vho": {"language": "VHDL", "code": 22, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/encoder.v": {"language": "Verilog", "code": 282, "comment": 33, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl38.vho": {"language": "VHDL", "code": 21, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl133.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl36.vho": {"language": "VHDL", "code": 21, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl134.vho": {"language": "VHDL", "code": 466, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl37.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl35.vho": {"language": "VHDL", "code": 5, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl34.vho": {"language": "VHDL", "code": 12, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/deserdes.v": {"language": "Verilog", "code": 44, "comment": 20, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl33.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl129.vho": {"language": "VHDL", "code": 5, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl131.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl128.vho": {"language": "VHDL", "code": 28, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl124.vho": {"language": "VHDL", "code": 47, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl130.vho": {"language": "VHDL", "code": 334, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl26.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl27.vho": {"language": "VHDL", "code": 6, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl123.vho": {"language": "VHDL", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl25.vho": {"language": "VHDL", "code": 9, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl122.vho": {"language": "VHDL", "code": 29, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl121.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl119.vho": {"language": "VHDL", "code": 8, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl120.vho": {"language": "VHDL", "code": 14, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl23.vho": {"language": "VHDL", "code": 14, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl24.vho": {"language": "VHDL", "code": 471, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl22.vho": {"language": "VHDL", "code": 15, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl117.vho": {"language": "VHDL", "code": 9, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl21.vho": {"language": "VHDL", "code": 8, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl115.vho": {"language": "VHDL", "code": 11, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl20.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl127.vho": {"language": "VHDL", "code": 5, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl19.vho": {"language": "VHDL", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl116.vho": {"language": "VHDL", "code": 14, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl114.vho": {"language": "VHDL", "code": 14, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl113.vho": {"language": "VHDL", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl112.vho": {"language": "VHDL", "code": 5, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl18.vho": {"language": "VHDL", "code": 31, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl17.vho": {"language": "VHDL", "code": 2, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl16.vho": {"language": "VHDL", "code": 8, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl111.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl110.vho": {"language": "VHDL", "code": 5, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub00/vhpl15.vho": {"language": "VHDL", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xst/work/sub01/vhpl118.vho": {"language": "VHDL", "code": 20, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/kw_pkg_fifo_pkg.vhd": {"language": "VHDL", "code": 254, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/kw_pkg_fifo_pctrl.vhd": {"language": "VHDL", "code": 438, "comment": 97, "blank": 48}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/kw_pkg_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/kw_pkg_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/kw_pkg_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/kw_pkg_fifo_synth.vhd": {"language": "VHDL", "code": 204, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/kw_pkg_fifo_tb.vhd": {"language": "VHDL", "code": 122, "comment": 67, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo.vho": {"language": "VHDL", "code": 27, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo.vhd": {"language": "VHDL", "code": 239, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo.vho": {"language": "VHDL", "code": 29, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo.v": {"language": "Verilog", "code": 445, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo.vhd": {"language": "VHDL", "code": 242, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/asyn_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/asyn_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/DCM_CASCADE_arwz.ucf": {"language": "vivado ucf", "code": 32, "comment": 3, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/DCM_CASCADE.v": {"language": "Verilog", "code": 146, "comment": 24, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/coregen.log": {"language": "Log", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/example_design/kw_pkg_fifo_exdes.vhd": {"language": "VHDL", "code": 62, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/example_design/kw_pkg_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/blkout_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/blkout_fifo_pkg.vhd": {"language": "VHDL", "code": 254, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/blkout_fifo_synth.vhd": {"language": "VHDL", "code": 203, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/blkout_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/config_fifo_dverif.vhd": {"language": "VHDL", "code": 72, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/config_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/config_fifo_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/example_design/blk_len_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/config_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/config_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/config_fifo_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/example_design/config_fifo_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/example_design/config_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/example_design/blk_len_fifo_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/config_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/blkout_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/blkout_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/simulation/blkout_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/example_design/blkout_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo/example_design/blkout_fifo_exdes.vhd": {"language": "VHDL", "code": 62, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/asyn_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/asyn_fifo_synth.vhd": {"language": "VHDL", "code": 201, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/asyn_fifo_pkg.vhd": {"language": "VHDL", "code": 253, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/asyn_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/simulation/8b10_tx/kw_pkg_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/simulation/asyn_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blk_len_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/blkout_fifo.v": {"language": "Verilog", "code": 447, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/example_design/asyn_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/example_design/asyn_fifo_exdes.vhd": {"language": "VHDL", "code": 59, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/config_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/asyn_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo.vho": {"language": "VHDL", "code": 29, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo.vhd": {"language": "VHDL", "code": 242, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo.v": {"language": "Verilog", "code": 447, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/SciData_Receive.vhd": {"language": "VHDL", "code": 400, "comment": 40, "blank": 75}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SciReceive_ram.vhd": {"language": "VHDL", "code": 102, "comment": 26, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SciReceive_8b10b_summary.html": {"language": "HTML", "code": 67, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/SciData_Multi.vhd": {"language": "VHDL", "code": 334, "comment": 26, "blank": 53}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SciReceive_8b10b_envsettings.html": {"language": "HTML", "code": 400, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SciData_Transmit_summary.html": {"language": "HTML", "code": 67, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/SciData_Transmit_envsettings.html": {"language": "HTML", "code": 400, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/Receive_8b10b.vhd": {"language": "VHDL", "code": 81, "comment": 25, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/Format_Check.vhd": {"language": "VHDL", "code": 168, "comment": 28, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/Receive_uart.vhd": {"language": "VHDL", "code": 54, "comment": 26, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/HDL/multiplex.vhd": {"language": "VHDL", "code": 328, "comment": 29, "blank": 51}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/PACK_PROCESS.vhd": {"language": "VHDL", "code": 311, "comment": 32, "blank": 58}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/param_pre.v": {"language": "Verilog", "code": 62, "comment": 15, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Parent_RAM0.v": {"language": "Verilog", "code": 32, "comment": 20, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pack_head_INPL_mux.v": {"language": "Verilog", "code": 153, "comment": 20, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pack_head_char.v": {"language": "Verilog", "code": 135, "comment": 19, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pack_head_INPL_top.v": {"language": "Verilog", "code": 68, "comment": 19, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/PACK_OUTPUT.vhd": {"language": "VHDL", "code": 169, "comment": 29, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/pack_control.v": {"language": "Verilog", "code": 399, "comment": 33, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/packing_top.v": {"language": "Verilog", "code": 83, "comment": 42, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/P3_byteout_new.v": {"language": "Verilog", "code": 428, "comment": 23, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/packing.v": {"language": "Verilog", "code": 636, "comment": 23, "blank": 82}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Output_BRAM.v": {"language": "Verilog", "code": 24, "comment": 1, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/P2_refresh_new.v": {"language": "Verilog", "code": 190, "comment": 169, "blank": 37}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/packing_sel.v": {"language": "Verilog", "code": 153, "comment": 24, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/mul_cut.v": {"language": "Verilog", "code": 40, "comment": 23, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/mux_apart_4level.v": {"language": "Verilog", "code": 268, "comment": 23, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/mult_pepi.v": {"language": "Verilog", "code": 17, "comment": 19, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/multiplex_envsettings.html": {"language": "HTML", "code": 400, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/mselen_input_buffer.v": {"language": "Verilog", "code": 168, "comment": 25, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/mid_top.v": {"language": "Verilog", "code": 140, "comment": 16, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/MQ_coder.v": {"language": "Verilog", "code": 87, "comment": 36, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/MAIN_CTRL.vhd": {"language": "VHDL", "code": 45, "comment": 48, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/L_SEND_PACK.vhd": {"language": "VHDL", "code": 219, "comment": 33, "blank": 54}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/L_PACK_MODE.vhd": {"language": "VHDL", "code": 273, "comment": 37, "blank": 54}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/modelsim.ini": {"language": "Ini", "code": 262, "comment": 1377, "blank": 300}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/L_PACK_PROCESS.vhd": {"language": "VHDL", "code": 317, "comment": 35, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/L_FGM_DATA_PROCESS.vhd": {"language": "VHDL", "code": 79, "comment": 6, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/L_EFI_DATA_PROCESS.vhd": {"language": "VHDL", "code": 80, "comment": 6, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/example_design/downstream_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/example_design/fifo_16width_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/L_DATA_PROCESS.vhd": {"language": "VHDL", "code": 339, "comment": 42, "blank": 81}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/example_design/downstream_fifo_exdes.vhd": {"language": "VHDL", "code": 62, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/example_design/fifo_16width_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/fifo_16width_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/fifo_16width_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/multiply_ip.v": {"language": "Verilog", "code": 10, "comment": 19, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/example_design/fifo_26width_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/downstream_fifo_pkg.vhd": {"language": "VHDL", "code": 254, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/downstream_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/downstream_fifo_dverif.vhd": {"language": "VHDL", "code": 72, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/fifo_26width_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/fifo_26width_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/downstream_fifo_synth.vhd": {"language": "VHDL", "code": 203, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/fifo_26width_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/downstream_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/downstream_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/example_design/fifo_26width_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/fifo_26width_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/downstream_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/fifo_26width_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/fifo_26width_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/assembly_comp.v": {"language": "Verilog", "code": 266, "comment": 70, "blank": 32}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/direct_rd_sdram.v": {"language": "Verilog", "code": 318, "comment": 38, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/cpu/cpu_top.v": {"language": "Verilog", "code": 321, "comment": 32, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/blocking_c.v": {"language": "Verilog", "code": 519, "comment": 27, "blank": 82}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/framing_c.v": {"language": "Verilog", "code": 506, "comment": 40, "blank": 64}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/assembly_direct.v": {"language": "Verilog", "code": 72, "comment": 35, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/packing.v": {"language": "Verilog", "code": 627, "comment": 23, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/packing_sel.v": {"language": "Verilog", "code": 153, "comment": 24, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/cpu/time_guard.v": {"language": "Verilog", "code": 134, "comment": 12, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/processing_analysis.v": {"language": "Verilog", "code": 386, "comment": 23, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/packing_top.v": {"language": "Verilog", "code": 83, "comment": 42, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/back/wr_blocks_c.v": {"language": "Verilog", "code": 487, "comment": 28, "blank": 75}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/rst_manage.v": {"language": "Verilog", "code": 49, "comment": 15, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/example_design/fifo_generator_v9_3_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/example_design/fifo_generator_v9_3_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/serdes_5x/bit_extract.v": {"language": "Verilog", "code": 63, "comment": 19, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/serdes_5x/DECODE.v": {"language": "Verilog", "code": 132, "comment": 23, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/lvds_8b10b_decoder.v": {"language": "Verilog", "code": 46, "comment": 26, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/serdes_5x/serdes_5x.v": {"language": "Verilog", "code": 41, "comment": 14, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/serdes_5x/SYN_BC.v": {"language": "Verilog", "code": 227, "comment": 21, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/ce7_comp_top.v": {"language": "Verilog", "code": 662, "comment": 103, "blank": 115}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/frame_len_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/frame_len_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/frame_len_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/example_design/frame_len_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/frame_len_fifo_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/frame_len_fifo_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/frame_len_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/sdram_control_top/pkg_ce5.vhd": {"language": "VHDL", "code": 14, "comment": 13, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/example_design/frame_len_fifo_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/mid/asyn_dpram_ip.v": {"language": "Verilog", "code": 46, "comment": 25, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/checksum.v": {"language": "Verilog", "code": 68, "comment": 17, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/gen_frame_syn.v": {"language": "Verilog", "code": 366, "comment": 52, "blank": 38}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/mid/rd_dpram.v": {"language": "Verilog", "code": 533, "comment": 33, "blank": 62}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/mid/param_pre.v": {"language": "Verilog", "code": 62, "comment": 15, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pkt_info_analysis.v": {"language": "Verilog", "code": 113, "comment": 18, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pkt_pre_top.v": {"language": "Verilog", "code": 173, "comment": 34, "blank": 28}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/frame_len_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo.vho": {"language": "VHDL", "code": 31, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo.v": {"language": "Verilog", "code": 449, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/mid/wr_dpram.v": {"language": "Verilog", "code": 129, "comment": 19, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/info_data_separate.v": {"language": "Verilog", "code": 205, "comment": 30, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/mid/mid_top.v": {"language": "Verilog", "code": 140, "comment": 16, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/dpram32b.v": {"language": "Verilog", "code": 35, "comment": 59, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo.vhd": {"language": "VHDL", "code": 245, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pkt_wr_fifo.v": {"language": "Verilog", "code": 46, "comment": 16, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/example_design/img_type_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/dat_receive.v": {"language": "Verilog", "code": 110, "comment": 16, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/sdram_control_top/sdram_write.vhd": {"language": "VHDL", "code": 115, "comment": 31, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/fifo_generator_v9_3_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/mid/comp_rd_sdram.v": {"language": "Verilog", "code": 335, "comment": 26, "blank": 46}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/fifo_generator_v9_3_dverif.vhd": {"language": "VHDL", "code": 72, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pre_rd_fifo.v": {"language": "Verilog", "code": 64, "comment": 17, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/fifo_generator_v9_3_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/example_design/img_type_fifo_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/sdram_control_top/sdram_read.vhd": {"language": "VHDL", "code": 106, "comment": 36, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/fifo_generator_v9_3_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/fifo_generator_v9_3_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/xaw2verilog.log": {"language": "Log", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/fifo_generator_v9_3_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/bram_cntrl.v": {"language": "Verilog", "code": 146, "comment": 21, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/body_top.v": {"language": "Verilog", "code": 102, "comment": 20, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/bit_add.v": {"language": "Verilog", "code": 1196, "comment": 25, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/blk_num_ram_ctrl.v": {"language": "Verilog", "code": 56, "comment": 22, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/.VSCodeCounter/2023-01-03_15-15-03/diff-details.md": {"language": "<PERSON><PERSON>", "code": 9, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/img_type_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/img_type_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/bram_coef_wr_cntrl.v": {"language": "Verilog", "code": 562, "comment": 33, "blank": 43}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/img_type_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/.VSCodeCounter/2023-01-03_15-15-03/details.md": {"language": "<PERSON><PERSON>", "code": 170, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/bram_ip.v": {"language": "Verilog", "code": 44, "comment": 25, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/state_reg.v": {"language": "Verilog", "code": 218, "comment": 31, "blank": 28}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/example_design/pktout_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/apart_data.v": {"language": "Verilog", "code": 95, "comment": 23, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pre_wr_sdram_top.v": {"language": "Verilog", "code": 216, "comment": 23, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/img_type_fifo_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/img_type_fifo_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Buffer_RAM1.v": {"language": "Verilog", "code": 32, "comment": 39, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Code.v": {"language": "Verilog", "code": 520, "comment": 0, "blank": 50}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/img_type_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Buffer_RAM0.v": {"language": "Verilog", "code": 32, "comment": 42, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/.VSCodeCounter/2023-01-03_15-15-03/diff.md": {"language": "<PERSON><PERSON>", "code": 12, "comment": 0, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/pktout_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/CODER_RDAT_top.v": {"language": "Verilog", "code": 479, "comment": 25, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/img_type_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/pktout_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/coder_rd_sdram.v": {"language": "Verilog", "code": 735, "comment": 45, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pre_wr_sdram.v": {"language": "Verilog", "code": 733, "comment": 79, "blank": 48}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/example_design/pktout_fifo_exdes.vhd": {"language": "VHDL", "code": 65, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/pktout_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/coder_wr_sdram.v": {"language": "Verilog", "code": 638, "comment": 45, "blank": 52}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/col_lift_ctrl_lev1.v": {"language": "Verilog", "code": 615, "comment": 29, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/col_lift_ctrl_lev234.v": {"language": "Verilog", "code": 919, "comment": 30, "blank": 57}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/col_lift_pre.v": {"language": "Verilog", "code": 74, "comment": 28, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/CODER_WDAT_top.v": {"language": "Verilog", "code": 179, "comment": 19, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/col_lift.v": {"language": "Verilog", "code": 69, "comment": 37, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/pktout_fifo_synth.vhd": {"language": "VHDL", "code": 205, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/pktout_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/pktout_fifo_pkg.vhd": {"language": "VHDL", "code": 255, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/.VSCodeCounter/2023-01-03_15-15-03/results.md": {"language": "<PERSON><PERSON>", "code": 22, "comment": 0, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/CODER_top.v": {"language": "Verilog", "code": 174, "comment": 21, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/col_trans_lev1.v": {"language": "Verilog", "code": 395, "comment": 31, "blank": 40}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/col_ram_ctrl.v": {"language": "Verilog", "code": 102, "comment": 27, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/pktout_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pre_wr_info_dpram.v": {"language": "Verilog", "code": 251, "comment": 30, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/.VSCodeCounter/2023-01-03_15-15-03/results.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pre_wr_img_dpram.v": {"language": "Verilog", "code": 248, "comment": 34, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/col_trans_lev234.v": {"language": "Verilog", "code": 443, "comment": 30, "blank": 46}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/compare.v": {"language": "Verilog", "code": 186, "comment": 30, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Create_Buffer0.v": {"language": "Verilog", "code": 260, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/COMP_TOP.v": {"language": "Verilog", "code": 418, "comment": 335, "blank": 48}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pre_wr_direct_dpram.v": {"language": "Verilog", "code": 230, "comment": 30, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/pre/pre_wr_config_fifo.v": {"language": "Verilog", "code": 157, "comment": 26, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/fifo_generator_v9_3_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Create_Tag_Tree.v": {"language": "Verilog", "code": 109, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Create_Value.v": {"language": "Verilog", "code": 75, "comment": 1, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Create_Layer_Controller.v": {"language": "Verilog", "code": 433, "comment": 0, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/cuter.v": {"language": "Verilog", "code": 244, "comment": 26, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Create_Layer.v": {"language": "Verilog", "code": 387, "comment": 6, "blank": 37}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Create_Buffer1.v": {"language": "Verilog", "code": 261, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/sdram_control_top/sdram_contrl_top.vhd": {"language": "VHDL", "code": 1104, "comment": 78, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/frame_len_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/sdram_control_top/rw_sdramcntrl.vhd": {"language": "VHDL", "code": 1036, "comment": 76, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/simulation/fifo_26width_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_26width/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/fifo_16width_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/img_type_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/fifo_16width_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/CUT_top.v": {"language": "Verilog", "code": 191, "comment": 19, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/data_buffer_new.v": {"language": "Verilog", "code": 168, "comment": 6, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/dcshift.v": {"language": "Verilog", "code": 32, "comment": 23, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 14, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/dual_port_bram.v": {"language": "Verilog", "code": 23, "comment": 21, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 64, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/dwt_store.v": {"language": "Verilog", "code": 288, "comment": 31, "blank": 35}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/fifo_16width_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/fifo_16width_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/lvds_8b10b_decoder.v": {"language": "Verilog", "code": 46, "comment": 26, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/fifo_16width_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/DWT_top.v": {"language": "Verilog", "code": 104, "comment": 21, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/lvde_send.v": {"language": "Verilog", "code": 491, "comment": 63, "blank": 59}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/pktout_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/dwt_trans.v": {"language": "Verilog", "code": 291, "comment": 30, "blank": 49}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/LL_adjust.v": {"language": "Verilog", "code": 128, "comment": 26, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/LL_ctrl.v": {"language": "Verilog", "code": 401, "comment": 26, "blank": 37}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/lev_sel.v": {"language": "Verilog", "code": 113, "comment": 19, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/lev_2_3_4.v": {"language": "Verilog", "code": 241, "comment": 29, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/lev_1.v": {"language": "Verilog", "code": 126, "comment": 30, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Encoder.v": {"language": "Verilog", "code": 397, "comment": 0, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_generator_v9_3/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/encode_lenth.v": {"language": "Verilog", "code": 123, "comment": 22, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/lift_pro_element.v": {"language": "Verilog", "code": 173, "comment": 28, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Encode_Tag_Tree.v": {"language": "Verilog", "code": 86, "comment": 2, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/fast_look_new_new.v": {"language": "Verilog", "code": 1554, "comment": 93, "blank": 61}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/FIFO.v": {"language": "Verilog", "code": 104, "comment": 0, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Global_Control.v": {"language": "Verilog", "code": 431, "comment": 0, "blank": 49}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/frame_head_out.v": {"language": "Verilog", "code": 462, "comment": 21, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Encode_Controller.v": {"language": "Verilog", "code": 289, "comment": 4, "blank": 41}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_16width/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/head_body_add.v": {"language": "Verilog", "code": 162, "comment": 19, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/interactive_contrl.v": {"language": "Verilog", "code": 90, "comment": 19, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/info_data_separate.v": {"language": "Verilog", "code": 205, "comment": 30, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/impact.xsl": {"language": "XSL", "code": 40, "comment": 3, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/H_PACK_PROCESS.vhd": {"language": "VHDL", "code": 547, "comment": 63, "blank": 48}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/H_FGM_DATA_PROCESS.vhd": {"language": "VHDL", "code": 85, "comment": 6, "blank": 35}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/H_EFI_DATA_PROCESS2.vhd": {"language": "VHDL", "code": 81, "comment": 3, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/head_len.v": {"language": "Verilog", "code": 164, "comment": 16, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/GC_DATA_PROCESS.vhd": {"language": "VHDL", "code": 100, "comment": 7, "blank": 32}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Global_Control.v": {"language": "Verilog", "code": 431, "comment": 0, "blank": 49}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/gen_frame_syn.v": {"language": "Verilog", "code": 349, "comment": 47, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/framing_c.v": {"language": "Verilog", "code": 558, "comment": 20, "blank": 64}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Format_Check_summary.html": {"language": "HTML", "code": 67, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/frame_head_out.v": {"language": "Verilog", "code": 462, "comment": 21, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/head_top.v": {"language": "Verilog", "code": 137, "comment": 21, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/interactive_contrl.v": {"language": "Verilog", "code": 90, "comment": 19, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/K_scale_ctrl.v": {"language": "Verilog", "code": 294, "comment": 22, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Kcoef_select.v": {"language": "Verilog", "code": 138, "comment": 23, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Leaf_Address.v": {"language": "Verilog", "code": 41, "comment": 0, "blank": 4}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/K_scale.v": {"language": "Verilog", "code": 251, "comment": 32, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/FIFO8192x8_summary.html": {"language": "HTML", "code": 66, "comment": 0, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Fifo_Schedule.v": {"language": "Verilog", "code": 32, "comment": 20, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/FIFO8192x8.vho": {"language": "VHDL", "code": 31, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Leaf_RAM0.v": {"language": "Verilog", "code": 25, "comment": 20, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/FIFO8192x8.vhd": {"language": "VHDL", "code": 245, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/FIFO.v": {"language": "Verilog", "code": 104, "comment": 0, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/lev_1.v": {"language": "Verilog", "code": 126, "comment": 30, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/encode_lenth.v": {"language": "Verilog", "code": 123, "comment": 22, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Encode_Tag_Tree.v": {"language": "Verilog", "code": 86, "comment": 2, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/lev_2_3_4.v": {"language": "Verilog", "code": 241, "comment": 29, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Encoder.v": {"language": "Verilog", "code": 397, "comment": 0, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/Encode_Controller.v": {"language": "Verilog", "code": 289, "comment": 4, "blank": 41}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcshift.v": {"language": "Verilog", "code": 32, "comment": 23, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/lev_sel.v": {"language": "Verilog", "code": 113, "comment": 19, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/fast_look_new_new.v": {"language": "Verilog", "code": 1554, "comment": 93, "blank": 61}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DCM2.vhd": {"language": "VHDL", "code": 72, "comment": 22, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/DCM2_arwz.ucf": {"language": "vivado ucf", "code": 16, "comment": 3, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/CLK_1MHz.vhd": {"language": "VHDL", "code": 56, "comment": 23, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/fifo_8width_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/mult_pepi.v": {"language": "Verilog", "code": 17, "comment": 19, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/fifo_8width_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/fifo_8width_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1.vhd": {"language": "VHDL", "code": 102, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/fifo_8width_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/fifo_8width_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/fifo_8width_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/fifo_8width_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1.vho": {"language": "VHDL", "code": 21, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/mux_apart_4level.v": {"language": "Verilog", "code": 268, "comment": 23, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DA_DRV.vhd": {"language": "VHDL", "code": 192, "comment": 31, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/pack_control.v": {"language": "Verilog", "code": 399, "comment": 33, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/P3_byteout_new.v": {"language": "Verilog", "code": 428, "comment": 23, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/ram_LL_adjust.v": {"language": "Verilog", "code": 42, "comment": 32, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DA_SCAN_OUT.vhd": {"language": "VHDL", "code": 65, "comment": 26, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/ram_col_lift.v": {"language": "Verilog", "code": 42, "comment": 30, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DA_SCAN.vhd": {"language": "VHDL", "code": 93, "comment": 28, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/PK_top.v": {"language": "Verilog", "code": 218, "comment": 24, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DA_SCAN_PROCESS.vhd": {"language": "VHDL", "code": 143, "comment": 26, "blank": 39}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DA_PROCESS.vhd": {"language": "VHDL", "code": 334, "comment": 26, "blank": 68}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/rd_MQ_dpram.v": {"language": "Verilog", "code": 331, "comment": 19, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/pass_len_code.v": {"language": "Verilog", "code": 412, "comment": 19, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/rdblk3_wrblkband.v": {"language": "Verilog", "code": 882, "comment": 38, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/summary.log": {"language": "Log", "code": 16, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo.vhd": {"language": "VHDL", "code": 239, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Parent_RAM0.v": {"language": "Verilog", "code": 32, "comment": 20, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/pack_head_INPL_top.v": {"language": "Verilog", "code": 68, "comment": 19, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/GC_DATA_PROCESS.vhd": {"language": "VHDL", "code": 101, "comment": 12, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/row_trans.v": {"language": "Verilog", "code": 235, "comment": 27, "blank": 31}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/row_lift_ctrl.v": {"language": "Verilog", "code": 298, "comment": 37, "blank": 38}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/H_EFI_DATA_PROCESS1.vhd": {"language": "VHDL", "code": 79, "comment": 5, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/H_EFI_DATA_PROCESS2.vhd": {"language": "VHDL", "code": 81, "comment": 3, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/row_lift_ctrl_lev234.v": {"language": "Verilog", "code": 544, "comment": 41, "blank": 52}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo.v": {"language": "Verilog", "code": 445, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/row_lift.v": {"language": "Verilog", "code": 68, "comment": 24, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/row_trans_lev234.v": {"language": "Verilog", "code": 316, "comment": 32, "blank": 40}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/schedule.v": {"language": "Verilog", "code": 324, "comment": 59, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/MAIN_CTRL.vhd": {"language": "VHDL", "code": 45, "comment": 48, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/H_FGM_DATA_PROCESS.vhd": {"language": "VHDL", "code": 85, "comment": 6, "blank": 35}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/D_C7_FPGA.vhd": {"language": "VHDL", "code": 700, "comment": 85, "blank": 164}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/slope_thrs_calc.v": {"language": "Verilog", "code": 68, "comment": 19, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/stack.v": {"language": "Verilog", "code": 100, "comment": 31, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo.vho": {"language": "VHDL", "code": 27, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/PACK_OUTPUT.vhd": {"language": "VHDL", "code": 169, "comment": 29, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/subband_apart.v": {"language": "Verilog", "code": 107, "comment": 25, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/RST.v": {"language": "Verilog", "code": 24, "comment": 19, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/TEST_PWR.vhd": {"language": "VHDL", "code": 46, "comment": 53, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/SEND_PACK.vhd": {"language": "VHDL", "code": 219, "comment": 33, "blank": 54}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/StateUpdate_and_ParameterPrepare.v": {"language": "Verilog", "code": 527, "comment": 0, "blank": 63}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/TIME_COUNTER.vhd": {"language": "VHDL", "code": 68, "comment": 38, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/wr_b_dpram.v": {"language": "Verilog", "code": 201, "comment": 60, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo.v": {"language": "Verilog", "code": 447, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A.vho": {"language": "VHDL", "code": 21, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/wr_blk3_dpram.v": {"language": "Verilog", "code": 177, "comment": 21, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/bmg_stim_gen.vhd": {"language": "VHDL", "code": 309, "comment": 71, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/bmg_tb_pkg.vhd": {"language": "VHDL", "code": 113, "comment": 77, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/wr_blen_bram_ctrl.v": {"language": "Verilog", "code": 44, "comment": 29, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/zerobit_out.v": {"language": "Verilog", "code": 463, "comment": 19, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/E_RAM_tb.vhd": {"language": "VHDL", "code": 56, "comment": 71, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/E_RAM_synth.vhd": {"language": "VHDL", "code": 186, "comment": 82, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/checker.vhd": {"language": "VHDL", "code": 76, "comment": 70, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/data_gen.vhd": {"language": "VHDL", "code": 61, "comment": 67, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/wr_slope_dpram.v": {"language": "Verilog", "code": 88, "comment": 19, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/addr_gen.vhd": {"language": "VHDL", "code": 44, "comment": 67, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/random.vhd": {"language": "VHDL", "code": 35, "comment": 67, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/addr_gen.vhd": {"language": "VHDL", "code": 44, "comment": 67, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 26, "comment": 0, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 5, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/write_to_sdram.v": {"language": "Verilog", "code": 1520, "comment": 56, "blank": 79}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/example_design/E_RAM_prod.vhd": {"language": "VHDL", "code": 91, "comment": 143, "blank": 42}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/example_design/E_RAM_exdes.xdc": {"language": "Xilinx Design Constraints", "code": 2, "comment": 52, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/write_to_ram.v": {"language": "Verilog", "code": 464, "comment": 41, "blank": 39}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/summary.log": {"language": "Log", "code": 16, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/TAG_TREE_top.v": {"language": "Verilog", "code": 227, "comment": 9, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/bmg_tb_pkg.vhd": {"language": "VHDL", "code": 113, "comment": 77, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/bmg_stim_gen.vhd": {"language": "VHDL", "code": 309, "comment": 71, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/checker.vhd": {"language": "VHDL", "code": 76, "comment": 70, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/CMD_RAM.vhd": {"language": "VHDL", "code": 102, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/example_design/E_RAM_exdes.vhd": {"language": "VHDL", "code": 60, "comment": 76, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/doc/blk_mem_gen_v7_3_vinfo.html": {"language": "HTML", "code": 156, "comment": 0, "blank": 69}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/uart_tx.v": {"language": "Verilog", "code": 150, "comment": 43, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/data_gen.vhd": {"language": "VHDL", "code": 61, "comment": 67, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/CMD_RAM.vho": {"language": "VHDL", "code": 21, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Tag_Tree_RAM_Ctrl.v": {"language": "Verilog", "code": 127, "comment": 199, "blank": 30}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/truncate.v": {"language": "Verilog", "code": 445, "comment": 68, "blank": 40}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/T2_top.v": {"language": "Verilog", "code": 205, "comment": 21, "blank": 27}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/T1_top.v": {"language": "Verilog", "code": 286, "comment": 0, "blank": 33}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/example_design/E_RAM_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 52, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/random.vhd": {"language": "VHDL", "code": 35, "comment": 67, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/functional/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/timing/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 22, "comment": 47, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/functional/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 18, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/timing/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 60, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/functional/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 18, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/functional/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/coregen.log": {"language": "Log", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/timing/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 22, "comment": 47, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/timing/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 60, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/RAM_A_tb.vhd": {"language": "VHDL", "code": 56, "comment": 71, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/example_design/config_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/simulation/RAM_A_synth.vhd": {"language": "VHDL", "code": 186, "comment": 82, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/syn_dpram_ip.v": {"language": "Verilog", "code": 76, "comment": 25, "blank": 18}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/SC_SEND_CTRL.vhd": {"language": "VHDL", "code": 134, "comment": 106, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/DCM0.v": {"language": "Verilog", "code": 68, "comment": 22, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/config_fifo_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/DCM1_arwz.ucf": {"language": "vivado ucf", "code": 16, "comment": 3, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/config_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/config_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/config_fifo_dverif.vhd": {"language": "VHDL", "code": 72, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/DCM2.v": {"language": "Verilog", "code": 57, "comment": 20, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/example_design/config_fifo_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/config_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/DCM0_arwz.ucf": {"language": "vivado ucf", "code": 16, "comment": 3, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/slope_top_new.v": {"language": "Verilog", "code": 179, "comment": 27, "blank": 39}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/config_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/DCM1.v": {"language": "Verilog", "code": 62, "comment": 22, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/.VSCodeCounter/2023-01-03_15-13-50/results.md": {"language": "<PERSON><PERSON>", "code": 17, "comment": 0, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/PACK_PROCESS.vhd": {"language": "VHDL", "code": 311, "comment": 32, "blank": 58}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/slope_rd_dpram_ctrl.v": {"language": "Verilog", "code": 200, "comment": 4, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/DCM2_arwz.ucf": {"language": "vivado ucf", "code": 16, "comment": 3, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/example_design/RAM_A_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 52, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/config_fifo_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A.vhd": {"language": "VHDL", "code": 102, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 26, "comment": 0, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/example_design/RAM_A_prod.vhd": {"language": "VHDL", "code": 91, "comment": 143, "blank": 42}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/example_design/RAM_A_exdes.vhd": {"language": "VHDL", "code": 60, "comment": 76, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/example_design/RAM_A_exdes.xdc": {"language": "Xilinx Design Constraints", "code": 2, "comment": 52, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 5, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/.VSCodeCounter/2023-01-03_15-13-50/results.json": {"language": "JSON", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo.v": {"language": "Verilog", "code": 447, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/TIME_manger/CLK_1s.vhd": {"language": "VHDL", "code": 107, "comment": 18, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/TIME_manger/CLK_1MHz.vhd": {"language": "VHDL", "code": 58, "comment": 23, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/TIME_manger/CLK_2MHz.vhd": {"language": "VHDL", "code": 41, "comment": 37, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/TIME_manger/time_counter.vhd": {"language": "VHDL", "code": 79, "comment": 38, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/PACK_RAM/RAM_A/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/asyn_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/asyn_fifo_pkg.vhd": {"language": "VHDL", "code": 253, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/asyn_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/asyn_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/asyn_fifo_synth.vhd": {"language": "VHDL", "code": 201, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/asyn_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/TIME_manger/DIV_TIME_EDGE.vhd": {"language": "VHDL", "code": 49, "comment": 24, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo.vho": {"language": "VHDL", "code": 29, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/asyn_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/downstream_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo.vhd": {"language": "VHDL", "code": 242, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/E_RAM.vhd": {"language": "VHDL", "code": 102, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/E_RAM.vho": {"language": "VHDL", "code": 21, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/example_design/downstream_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/example_design/downstream_fifo_exdes.vhd": {"language": "VHDL", "code": 62, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/downstream_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FGM1.vho": {"language": "VHDL", "code": 21, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/downstream_fifo_synth.vhd": {"language": "VHDL", "code": 203, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/example_design/blk_len_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FGM1.vhd": {"language": "VHDL", "code": 102, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/downstream_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/downstream_fifo_pkg.vhd": {"language": "VHDL", "code": 254, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/example_design/blk_len_fifo_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/downstream_fifo_dverif.vhd": {"language": "VHDL", "code": 72, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/downstream_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/config_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/L_SEND_PACK.vhd": {"language": "VHDL", "code": 219, "comment": 33, "blank": 54}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/blk_len_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM/doc/blk_mem_gen_v7_3_vinfo.html": {"language": "HTML", "code": 156, "comment": 0, "blank": 69}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/L_PACK_PROCESS.vhd": {"language": "VHDL", "code": 317, "comment": 35, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/.VSCodeCounter/2023-01-03_15-13-50/diff.md": {"language": "<PERSON><PERSON>", "code": 12, "comment": 0, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8.vho": {"language": "VHDL", "code": 29, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8.vhd": {"language": "VHDL", "code": 242, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/L_FGM_DATA_PROCESS.vhd": {"language": "VHDL", "code": 121, "comment": 6, "blank": 43}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/summary.log": {"language": "Log", "code": 16, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/.VSCodeCounter/2023-01-03_15-13-50/diff-details.md": {"language": "<PERSON><PERSON>", "code": 9, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/slope_rd_dpram.v": {"language": "Verilog", "code": 23, "comment": 22, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/L_EFI_DATA_PROCESS.vhd": {"language": "VHDL", "code": 80, "comment": 6, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/downstream_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/.VSCodeCounter/2023-01-03_15-13-50/details.md": {"language": "<PERSON><PERSON>", "code": 50, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8.vho": {"language": "VHDL", "code": 31, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/example_design/asyn_fifo_exdes.vhd": {"language": "VHDL", "code": 59, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8.vhd": {"language": "VHDL", "code": 245, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/example_design/asyn_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/fifo1024x16_to_2048x8_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192.vho": {"language": "VHDL", "code": 33, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192.vhd": {"language": "VHDL", "code": 248, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/fifo1024x16_to_2048x8_synth.vhd": {"language": "VHDL", "code": 203, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/fifo1024x16_to_2048x8_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/example_design/fifo1024x16_to_2048x8_exdes.vhd": {"language": "VHDL", "code": 62, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8.vho": {"language": "VHDL", "code": 31, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/example_design/fifo1024x16_to_2048x8_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8.vhd": {"language": "VHDL", "code": 245, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_8width.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/FIFO8192_2048_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/FIFO8192_2048_synth.vhd": {"language": "VHDL", "code": 205, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/FIFO8192_2048_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/FIFO8192_2048_pkg.vhd": {"language": "VHDL", "code": 255, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/example_design/FIFO8192_2048_exdes.vhd": {"language": "VHDL", "code": 65, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/FIFO8192_2048_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/example_design/FIFO8192_2048_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/FIFO8192_2048_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/FIFO8192_2048_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/FIFO8192x8_synth.vhd": {"language": "VHDL", "code": 205, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/FIFO8192x8_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/FIFO8192x8_pkg.vhd": {"language": "VHDL", "code": 255, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/FIFO8192x8_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/FIFO8192x8_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/FIFO8192x8_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/FIFO8192x8_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/example_design/fifo_16width_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/example_design/fifo_16width_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/fifo_16width_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/fifo_16width_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/fifo_16width_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/fifo_16width_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/fifo_16width_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/fifo_16width_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/example_design/fifo_26width_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/fifo_16width_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/example_design/FIFO2048_8192_exdes.vhd": {"language": "VHDL", "code": 68, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_generator_v9_3.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/FIFO2048_8192_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/example_design/fifo_26width_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/example_design/FIFO2048_8192_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/FIFO2048_8192_synth.vhd": {"language": "VHDL", "code": 207, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/FIFO2048_8192_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/fifo_26width_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/FIFO2048_8192_pkg.vhd": {"language": "VHDL", "code": 256, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048.vho": {"language": "VHDL", "code": 31, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/FIFO2048_8192_dverif.vhd": {"language": "VHDL", "code": 93, "comment": 64, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/FIFO2048_8192_pctrl.vhd": {"language": "VHDL", "code": 410, "comment": 94, "blank": 48}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/fifo_26width_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192_2048.vhd": {"language": "VHDL", "code": 245, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048_8192/simulation/FIFO2048_8192_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/fifo_26width_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/fifo_26width_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/example_design/FIFO8192x8_exdes.vhd": {"language": "VHDL", "code": 65, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/example_design/FIFO_sim_sdram_exdes.vhd": {"language": "VHDL", "code": 50, "comment": 64, "blank": 19}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/example_design/FIFO8192x8_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/fifo_26width_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/example_design/FIFO_sim_sdram_exdes.ucf": {"language": "vivado ucf", "code": 2, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO8192x8/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/fifo_26width_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/frame_len_fifo.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/fifo_26width_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/fifo1024x16_to_2048x8_pkg.vhd": {"language": "VHDL", "code": 254, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/fifo1024x16_to_2048x8_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/FIFO_sim_sdram_dverif.vhd": {"language": "VHDL", "code": 72, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/FIFO_sim_sdram_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/FIFO_sim_sdram_pkg.vhd": {"language": "VHDL", "code": 251, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/FIFO2048x8_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/FIFO_sim_sdram_pctrl.vhd": {"language": "VHDL", "code": 329, "comment": 91, "blank": 42}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/FIFO2048x8_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/FIFO2048x8_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_16width/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/FIFO_sim_sdram_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/FIFO_sim_sdram_synth.vhd": {"language": "VHDL", "code": 170, "comment": 72, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/FIFO2048x8_pkg.vhd": {"language": "VHDL", "code": 255, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/FIFO2048x8_synth.vhd": {"language": "VHDL", "code": 205, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/FIFO2048x8_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo.vho": {"language": "VHDL", "code": 25, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo.v": {"language": "Verilog", "code": 443, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo.vhd": {"language": "VHDL", "code": 236, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/FIFO_sim_sdram_tb.vhd": {"language": "VHDL", "code": 105, "comment": 67, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/FIFO2048x8_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo_26width/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/fifo1024x16_to_2048x8_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/simulation/fifo1024x16_to_2048x8_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/img_type_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/img_type_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/pktout_fifo.v": {"language": "Verilog", "code": 449, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 67, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/img_type_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/example_design/FIFO2048x8_exdes.vhd": {"language": "VHDL", "code": 65, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/img_type_fifo_pkg.vhd": {"language": "VHDL", "code": 252, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/img_type_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM_A.vho": {"language": "VHDL", "code": 21, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM_A.vhd": {"language": "VHDL", "code": 102, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/img_type_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/xaw2verilog.log": {"language": "Log", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/img_type_fifo_synth.vhd": {"language": "VHDL", "code": 199, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/example_design/RAM512X16_exdes.xdc": {"language": "Xilinx Design Constraints", "code": 2, "comment": 52, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/example_design/RAM512X16_prod.vhd": {"language": "VHDL", "code": 93, "comment": 143, "blank": 42}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/random.vhd": {"language": "VHDL", "code": 35, "comment": 67, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/RAM512X16_tb.vhd": {"language": "VHDL", "code": 56, "comment": 71, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/example_design/RAM512X16_exdes.vhd": {"language": "VHDL", "code": 63, "comment": 76, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 5, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/data_gen.vhd": {"language": "VHDL", "code": 61, "comment": 67, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/example_design/RAM512X16_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 52, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/RAM512X16_synth.vhd": {"language": "VHDL", "code": 193, "comment": 82, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 26, "comment": 0, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/checker.vhd": {"language": "VHDL", "code": 76, "comment": 70, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/addr_gen.vhd": {"language": "VHDL", "code": 44, "comment": 67, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/summary.log": {"language": "Log", "code": 16, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 67, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/bmg_tb_pkg.vhd": {"language": "VHDL", "code": 113, "comment": 77, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/bmg_stim_gen.vhd": {"language": "VHDL", "code": 311, "comment": 71, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16.vho": {"language": "VHDL", "code": 23, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16.vhd": {"language": "VHDL", "code": 105, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/example_design/FIFO2048x8_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16.v": {"language": "Verilog", "code": 142, "comment": 36, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO2048x8/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/FIFO_sim_sdram/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/example_design/img_type_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 12, "comment": 0, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/example_design/img_type_fifo_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/doc/blk_mem_gen_v7_3_vinfo.html": {"language": "HTML", "code": 156, "comment": 0, "blank": 69}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/functional/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/functional/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 18, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 60, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/timing/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/L_DATA_PROCESS.vhd": {"language": "VHDL", "code": 339, "comment": 42, "blank": 81}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/slope_control.v": {"language": "Verilog", "code": 402, "comment": 52, "blank": 36}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/timing/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 22, "comment": 47, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/LC_PROCESS.vhd": {"language": "VHDL", "code": 173, "comment": 26, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo.vho": {"language": "VHDL", "code": 29, "comment": 62, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blk_len_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/slope_calc_m.v": {"language": "Verilog", "code": 377, "comment": 34, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/H_PACK_PROCESS.vhd": {"language": "VHDL", "code": 554, "comment": 77, "blank": 50}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/img_type_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/ROM_162x8.v": {"language": "Verilog", "code": 18, "comment": 21, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/sdram2dpram.v": {"language": "Verilog", "code": 480, "comment": 27, "blank": 43}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM.vho": {"language": "VHDL", "code": 21, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DIV_TIME_EDGE.vhd": {"language": "VHDL", "code": 272, "comment": 32, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Result_Store.v": {"language": "Verilog", "code": 743, "comment": 18, "blank": 77}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/RAM512X16/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 12, "comment": 0, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/reg_top_xs_cntrl.v": {"language": "Verilog", "code": 52, "comment": 19, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/CMD/CMD_PROCESS.vhd": {"language": "VHDL", "code": 284, "comment": 38, "blank": 63}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/CMD/CMD_REV.v": {"language": "Verilog", "code": 294, "comment": 81, "blank": 39}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DA_SCH.vhd": {"language": "VHDL", "code": 71, "comment": 33, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/CMD/CMD_CHECK.v": {"language": "Verilog", "code": 296, "comment": 62, "blank": 34}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/E_RAM.vhd": {"language": "VHDL", "code": 102, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/rd_one_pack_ctrl.v": {"language": "Verilog", "code": 172, "comment": 22, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/CMD/CMD_ANALY.vhd": {"language": "VHDL", "code": 583, "comment": 79, "blank": 65}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/rd_MQ_sdram.v": {"language": "Verilog", "code": 173, "comment": 21, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/example_design/FGM1_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 52, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/pack_head_INPL_mux.v": {"language": "Verilog", "code": 153, "comment": 20, "blank": 14}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo.vhd": {"language": "VHDL", "code": 242, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/E_RAM/coregen.log": {"language": "Log", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/random.vhd": {"language": "VHDL", "code": 35, "comment": 67, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 26, "comment": 0, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/example_design/FGM1_exdes.vhd": {"language": "VHDL", "code": 60, "comment": 76, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/doc/blk_mem_gen_v7_3_vinfo.html": {"language": "HTML", "code": 156, "comment": 0, "blank": 69}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/example_design/FGM1_exdes.xdc": {"language": "Xilinx Design Constraints", "code": 2, "comment": 52, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/example_design/FGM1_prod.vhd": {"language": "VHDL", "code": 91, "comment": 143, "blank": 42}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 5, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/FGM1_tb.vhd": {"language": "VHDL", "code": 56, "comment": 71, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/asyn_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/data_gen.vhd": {"language": "VHDL", "code": 61, "comment": 67, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/checker.vhd": {"language": "VHDL", "code": 76, "comment": 70, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/bmg_tb_pkg.vhd": {"language": "VHDL", "code": 113, "comment": 77, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/FGM1_synth.vhd": {"language": "VHDL", "code": 186, "comment": 82, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/addr_gen.vhd": {"language": "VHDL", "code": 44, "comment": 67, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DA_NORMAL.vhd": {"language": "VHDL", "code": 65, "comment": 26, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/pack_head_char.v": {"language": "Verilog", "code": 135, "comment": 19, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/bmg_stim_gen.vhd": {"language": "VHDL", "code": 309, "comment": 71, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DA_CHANGE.vhd": {"language": "VHDL", "code": 165, "comment": 34, "blank": 28}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/fifo1024x16_to_2048x8/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/P2_refresh_new.v": {"language": "Verilog", "code": 190, "comment": 169, "blank": 37}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/SRC/DATA_PROCESS.vhd": {"language": "VHDL", "code": 442, "comment": 30, "blank": 81}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/functional/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/Output_BRAM.v": {"language": "Verilog", "code": 24, "comment": 1, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 60, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/mul_cut.v": {"language": "Verilog", "code": 40, "comment": 23, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/functional/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 18, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/blkout_fifo_rng.vhd": {"language": "VHDL", "code": 34, "comment": 61, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/example_design/blkout_fifo_exdes.vhd": {"language": "VHDL", "code": 62, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/example_design/blkout_fifo_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/blkout_fifo_tb.vhd": {"language": "VHDL", "code": 117, "comment": 67, "blank": 25}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/blkout_fifo_synth.vhd": {"language": "VHDL", "code": 203, "comment": 72, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/blkout_fifo_pkg.vhd": {"language": "VHDL", "code": 254, "comment": 81, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/blkout_fifo_pctrl.vhd": {"language": "VHDL", "code": 401, "comment": 94, "blank": 47}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/blkout_fifo_dverif.vhd": {"language": "VHDL", "code": 81, "comment": 64, "blank": 15}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/blkout_fifo_dgen.vhd": {"language": "VHDL", "code": 48, "comment": 64, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/timing/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM1/simulation/timing/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 22, "comment": 47, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM.vhd": {"language": "VHDL", "code": 102, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/timing/simulate_isim.sh": {"language": "<PERSON> Script", "code": 12, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM.vho": {"language": "VHDL", "code": 21, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/functional/simulate_isim.sh": {"language": "<PERSON> Script", "code": 13, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 46, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/example_design/fifo_8width_exdes.vhd": {"language": "VHDL", "code": 56, "comment": 64, "blank": 20}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 47, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/doc/fifo_generator_v9_3_vinfo.html": {"language": "HTML", "code": 170, "comment": 0, "blank": 78}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/timing/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 67, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/timing/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/example_design/fifo_8width_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 51, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/functional/simulate_ncsim.bat": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/fifo_8width/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 68, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/implement/implement_synplify.sh": {"language": "<PERSON> Script", "code": 21, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/summary.log": {"language": "Log", "code": 16, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/ipcore_dir/blkout_fifo/simulation/functional/simulate_vcs.bat": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 5, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 26, "comment": 0, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/multiply_ip.v": {"language": "Verilog", "code": 10, "comment": 19, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 50, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/mselen_input_buffer.v": {"language": "Verilog", "code": 168, "comment": 25, "blank": 24}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/implement/implement_synplify.bat": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/LL_adjust.v": {"language": "Verilog", "code": 128, "comment": 26, "blank": 21}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/MQ_coder.v": {"language": "Verilog", "code": 87, "comment": 36, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/LL_ctrl.v": {"language": "Verilog", "code": 401, "comment": 26, "blank": 37}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/src/comp/lift_pro_element.v": {"language": "Verilog", "code": 173, "comment": 28, "blank": 26}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/example_design/FGM_RAM_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 52, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/xdip/ipcore_dir/downstream_fifo/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 17}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/doc/blk_mem_gen_v7_3_vinfo.html": {"language": "HTML", "code": 156, "comment": 0, "blank": 69}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/timing/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/example_design/CMD_RAM_exdes.vhd": {"language": "VHDL", "code": 60, "comment": 76, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/checker.vhd": {"language": "VHDL", "code": 76, "comment": 70, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/bmg_tb_pkg.vhd": {"language": "VHDL", "code": 113, "comment": 77, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/timing/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 22, "comment": 47, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/addr_gen.vhd": {"language": "VHDL", "code": 44, "comment": 67, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/example_design/CMD_RAM_exdes.ucf": {"language": "vivado ucf", "code": 4, "comment": 52, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/implement/planAhead_ise.sh": {"language": "<PERSON> Script", "code": 5, "comment": 49, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/implement/implement.sh": {"language": "<PERSON> Script", "code": 22, "comment": 5, "blank": 22}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/bmg_stim_gen.vhd": {"language": "VHDL", "code": 309, "comment": 71, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/doc/blk_mem_gen_v7_3_vinfo.html": {"language": "HTML", "code": 156, "comment": 0, "blank": 69}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/implement/planAhead_ise.bat": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/example_design/CMD_RAM_exdes.xdc": {"language": "Xilinx Design Constraints", "code": 2, "comment": 52, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/data_gen.vhd": {"language": "VHDL", "code": 61, "comment": 67, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/implement/implement.bat": {"language": "<PERSON><PERSON>", "code": 26, "comment": 0, "blank": 23}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/CMD_RAM_tb.vhd": {"language": "VHDL", "code": 56, "comment": 71, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/CMD_RAM_synth.vhd": {"language": "VHDL", "code": 186, "comment": 82, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/example_design/CMD_RAM_prod.vhd": {"language": "VHDL", "code": 91, "comment": 143, "blank": 42}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/random.vhd": {"language": "VHDL", "code": 35, "comment": 67, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/timing/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/random.vhd": {"language": "VHDL", "code": 35, "comment": 67, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM.vhd": {"language": "VHDL", "code": 102, "comment": 42, "blank": 6}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/summary.log": {"language": "Log", "code": 16, "comment": 0, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/timing/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM.vho": {"language": "VHDL", "code": 21, "comment": 53, "blank": 8}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/timing/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 22, "comment": 47, "blank": 10}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/timing/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 60, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/timing/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/functional/simulate_mti.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/functional/simulate_mti.bat": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/functional/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 18, "comment": 47, "blank": 5}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/timing/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/functional/simulate_isim.bat": {"language": "<PERSON><PERSON>", "code": 60, "comment": 0, "blank": 9}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/CMD_RAM/CMD_RAM/simulation/functional/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/checker.vhd": {"language": "VHDL", "code": 76, "comment": 70, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/data_gen.vhd": {"language": "VHDL", "code": 61, "comment": 67, "blank": 13}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/FGM_RAM_tb.vhd": {"language": "VHDL", "code": 56, "comment": 71, "blank": 16}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/bmg_tb_pkg.vhd": {"language": "VHDL", "code": 113, "comment": 77, "blank": 11}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/example_design/FGM_RAM_exdes.xdc": {"language": "Xilinx Design Constraints", "code": 2, "comment": 52, "blank": 3}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/addr_gen.vhd": {"language": "VHDL", "code": 44, "comment": 67, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/FGM_RAM_synth.vhd": {"language": "VHDL", "code": 186, "comment": 82, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/functional/simulate_ncsim.sh": {"language": "<PERSON> Script", "code": 17, "comment": 47, "blank": 7}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/example_design/FGM_RAM_prod.vhd": {"language": "VHDL", "code": 91, "comment": 143, "blank": 42}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/bmg_stim_gen.vhd": {"language": "VHDL", "code": 309, "comment": 71, "blank": 55}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/example_design/FGM_RAM_exdes.vhd": {"language": "VHDL", "code": 60, "comment": 76, "blank": 44}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/functional/wave_ncsim.sv": {"language": "System Verilog", "code": 11, "comment": 0, "blank": 12}, "file:///c%3A/other/ise_14_7/workshop/ce7zlq/zx/CE7-X_DPUv20_updata_dcy%20_dm/CE7-X_DPU/dcyip/IP/FGM_RAM/FGM_RAM/simulation/functional/simulate_vcs.sh": {"language": "<PERSON> Script", "code": 18, "comment": 47, "blank": 5}}