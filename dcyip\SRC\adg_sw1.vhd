library IEEE;

use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

entity ADG_SW1 is
port (
    reset   : in std_logic;
    clk     : in std_logic;
	l_en    : in std_logic;
	
    start_edge	: in std_logic;
	time_edge	: in std_logic;    
	data_in	: in std_logic_vector(15 downto 0);  
	rd_over : in std_logic;   
    fgm_sample_state: out std_logic;	

    fgm_rd_en: out std_logic;
	gc_rd_en:  out std_logic;	
	fgm_data:out std_logic_vector(15 downto 0);
	gc_data:out std_logic_vector(15 downto 0);
	
--	fgm_chanel  : in std_logic_vector(7 downto 0);
    efi_sw_addr:out std_logic_vector(2 downto 0)
);
end ADG_SW1;	

architecture Behavioral of ADG_SW1 is

--attribute keep_hierarchy : string;
--attribute keep_hierarchy of Behavioral : architecture is "no";

signal cnt :integer range 0 to 10000;
signal wait1_cnt :integer range 0 to 2048;
signal wait2_cnt :integer range 0 to 3072;
signal wait3_cnt :integer range 0 to 5120;
signal wait4_cnt :integer range 0 to 6144;
signal wait5_cnt :integer range 0 to 8192;
signal wait6_cnt :integer range 0 to 9216;
signal wait7_cnt :integer range 0 to 385;
signal wait8_cnt :integer range 0 to 193;
signal wait9_cnt :integer range 0 to 257;
signal cnt1 :integer range 0 to 5;
--signal fgm_cnt:integer range 0 to 1600;
signal fgm_state:std_logic;
type state_t is (start_s,start_rd,check_s,fgm_read,fgm_change,wait_read,gc_read,gc_change);
signal state: state_t;
signal fgm_state_reg:std_logic;
signal gc_state:std_logic;
	
begin
---fgm_sample_state<=fgm_state;
			
process(reset,clk)	
	begin
		if reset='0' then
		     state<=start_s;cnt<=0; cnt1<=0;fgm_state<='0';efi_sw_addr<="000";
			 fgm_data<=(others=>'0');gc_data<=(others=>'0');fgm_rd_en<='0';gc_rd_en<='0';gc_state<='0';

		else
		    if clk='1' and clk'event then
			    if start_edge='1' then  	
				    cnt<=0; cnt1<=0; fgm_state<='1';state<=start_s;fgm_rd_en<='0';gc_rd_en<='0';efi_sw_addr<=(others=>'0');gc_state<='0';
				else 
				    case state is
					    when start_s =>
							efi_sw_addr<=(others=>'0');
						    if time_edge='1' then 
							    cnt<=0; cnt1<=0; fgm_state<='1'; state<=start_rd;gc_state<='0';
						    end if;
						when start_rd=>
						    if rd_over='1' then 
							    cnt<=cnt+1;state<=check_s;
                            end if; 
                        when check_s=>
						    if fgm_state='1' then state<=fgm_read;
							elsif gc_state='1' then state<=gc_read;
						    else state<=wait_read;
							end if;
						when fgm_read=>
						    if cnt>0 and cnt<=wait1_cnt then fgm_rd_en<='0'; efi_sw_addr<="000"; 
							elsif cnt>wait1_cnt and cnt<=wait2_cnt then fgm_rd_en<='1'; fgm_data<=data_in;
							elsif cnt>wait2_cnt and cnt<=wait3_cnt then fgm_rd_en<='0';efi_sw_addr<="001"; 
							elsif cnt>wait3_cnt and cnt<=wait4_cnt then fgm_rd_en<='1';fgm_data<=data_in;
							elsif cnt>wait4_cnt and cnt<=wait5_cnt then fgm_rd_en<='0';efi_sw_addr<="010";
							elsif cnt>wait5_cnt and cnt<=wait6_cnt then fgm_rd_en<='1'; fgm_data<=data_in;
							else  fgm_rd_en<='0';efi_sw_addr<="000"; end if;  
						    state<=fgm_change;
						when fgm_change=>
						    if cnt>=wait6_cnt then 
--							    cnt<=0;fgm_state<='0';	efi_sw_addr<="111";
                                cnt<=0;fgm_state<='0';	efi_sw_addr<="011";
							end if;
							fgm_rd_en<='0'; state<=start_rd;
						when wait_read=>
						    if cnt>=wait7_cnt then cnt<=0;gc_state<='1';
							else fgm_state<='0';gc_state<='0';	
                            end if;
							state<=start_rd;								
                        when gc_read=>							
						    if (cnt>0 and cnt<=wait8_cnt) then gc_rd_en<='0';
--							    if cnt1=0 then efi_sw_addr<="111";
                                if cnt1=0 then efi_sw_addr<="011";
								elsif cnt1=4 then efi_sw_addr<="011";
								else efi_sw_addr<=conv_std_logic_vector(conv_integer(cnt1+3),3); 
								end if;
							elsif (cnt>wait8_cnt and cnt<=wait9_cnt) then gc_rd_en<='1'; 
							    if cnt1=0 then gc_data<=x"0A00"; 
								else gc_data<=data_in; 
								end if;
							else gc_rd_en<='0'; end if;
							state<=gc_change;
						when gc_change=>
						    gc_rd_en<='0';
							if cnt>=wait9_cnt then 
							    cnt<=0;
							    if cnt1>=4 then 
								    cnt1<=0;cnt<=0;fgm_state<='1';state<=start_s;gc_state<='0';
								else
								    cnt1<=cnt1+1;state<=start_rd;
								end if;
                            else
							    state<=start_rd;
							end if;	
						when others=>state<=start_s;	
					end case;
				end if;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
		    fgm_state_reg<='0';
		else 
		    if clk='1' and clk'event then
		        fgm_state_reg<=fgm_state;
			end if;
		end if;
	end process;
	
    process(reset,clk)
	begin
		if reset='0' then
		    fgm_sample_state<='0';
		else 
		    if clk='1' and clk'event then
			    if fgm_state='0' and fgm_state_reg='1' then 
				    fgm_sample_state<='1';
				else 
				    fgm_sample_state<='0';
                end if;
            end if;			
		end if;
	end process;
	
    process(reset,clk)
	begin
		if reset='0' then
		    wait1_cnt<=0;wait2_cnt<=0;wait3_cnt<=0;wait4_cnt<=0;wait5_cnt<=0;wait6_cnt<=0;
			wait7_cnt<=0;wait8_cnt<=0;wait9_cnt<=0;
		else 
		    if clk='1' and clk'event then
--			    wait7_cnt<=384;wait8_cnt<=128;wait9_cnt<=192;
                wait7_cnt<=384;wait8_cnt<=192;wait9_cnt<=256;
                if l_en='0' then 
				    wait1_cnt<=2048;wait2_cnt<=3072;wait3_cnt<=5120;wait4_cnt<=6144;wait5_cnt<=8192;wait6_cnt<=9216;
				else 
				    wait1_cnt<=512;wait2_cnt<=768;wait3_cnt<=1280;wait4_cnt<=1536;wait5_cnt<=2048;wait6_cnt<=2304;
				end if;
			end if;	
		end if;
	end process;
	
							
							  
							

	
	
	
	
	
end Behavioral;




