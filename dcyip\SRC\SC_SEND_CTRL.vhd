----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    10:56:50 09/03/2021 
-- Design Name: 
-- Module Name:    UART_CTRL - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity SC_SEND_CTRL is
port(
    reset          : in std_logic;
    clk            : in std_logic;
--	test           : in std_logic;
	
	sc_edge          : in std_logic;
	send_start       : out std_logic;
	send_over        : in std_logic;
	data_in : in std_logic_vector(7 downto 0); 
	addr	: out std_logic_vector(10 downto 0);
	
	data_out: buffer std_logic_vector(7 downto 0)
	
);
end SC_SEND_CTRL;

architecture Behavioral of SC_SEND_CTRL is
type sc_state_t is (sc_start_s,send_s,send_s0,send_s1);
signal sc_state : sc_state_t;
signal packet_cnt: std_logic_vector(15 downto 0);
signal checksum: std_logic_vector(15 downto 0);
signal checksum1: std_logic_vector(7 downto 0);
signal send_cnt: integer range 0 to 1048;
signal start:std_logic;
--signal test_mode:std_logic;


begin

	process(reset,clk)
	begin
		if reset ='0' then
			packet_cnt <=(others=>'0');
		else
			if clk='1' and clk'event then
				if send_over = '1' and send_cnt =25 then
					if packet_cnt=x"3FFF" then
						packet_cnt<=(others=>'0');
					else
						packet_cnt <=packet_cnt+1;
					end if;
				end if;
			end if;
		end if;
	end process;
	
    process(reset,clk)
	begin
		if reset ='0' then
			send_start <='0';
		else
			if clk='1' and clk'event then
				send_start <=start;
			end if;
		end if;
	end process;
	
	-- process(reset,clk)
	-- begin
		-- if reset ='0' then
			-- test_mode <='0';
		-- else
			-- if clk='1' and clk'event then
				-- if sc_edge ='1' then
				    -- if test ='1' then
					   -- test_mode<='1';
					-- else
					   -- test_mode<='0';
					-- end if;
				-- end if;
			-- end if;
		-- end if;
	-- end process;



    process(reset,clk)
	variable cnt: integer range 0 to 1050;
	begin
		if reset='0' then
			cnt :=0;
			sc_state	<=sc_start_s;
			addr		<=(others=>'0');
			start	<='0';
		else
			if clk='1' and clk'event then
				case sc_state is 
					when sc_start_s =>
						if sc_edge ='1' then 
							cnt :=0;
							addr<=(others=>'0');
							sc_state<=send_s;
						end if;
					when send_s =>
						start<='1';
						sc_state<=send_s0;
					when send_s0 =>
					    start<='0';
						if send_over ='1' then
							cnt:=cnt+1;
							sc_state<= send_s1;
						end if;
					when send_s1 =>				
                        if cnt >=1048 then
							addr<=(others=>'0');
							sc_state<=sc_start_s;
							cnt:=0;
						else
						 	if cnt <1048 then
								addr<=conv_std_logic_vector(cnt,11);  --cnt:1~96
							end if;
							sc_state<=send_s;
						end if;
                    when others => sc_state<=sc_start_s;
				end case;
			end if;
		end if;
	end process;

    process(reset,clk)
	begin
		if reset='0' then
			send_cnt<=0;
			checksum<=x"0000";
			checksum1<=x"00";
		else
			if clk='1' and clk'event then
				if sc_edge ='1' then
					send_cnt<=0;
					checksum<=x"0000";
					checksum1<=x"00";
				else
					if send_over='1' then
					    if send_cnt>7 and send_cnt<1046 then							
							checksum<=conv_std_logic_vector(conv_integer(checksum)+conv_integer(data_out),16);
						end if;
						if send_cnt >1048 then
							send_cnt <=0;
						else
							send_cnt<=send_cnt+1;
						end if;
					    if send_cnt>13 and send_cnt<1045 then
						    checksum1<=conv_std_logic_vector(conv_integer(checksum1)+conv_integer(data_out),8);
						end if;
					end if;
				end if;
			end if;
		end if;
	end process;
	
	process(reset,clk)
		begin
		if reset='0' then
			data_out<=x"00";
		else
		    if clk='1' and clk'event then
--同步字：0xE225	
				-- if send_cnt = 0  then
					-- data_out<= x"E2";
				-- elsif send_cnt =1 then
					-- data_out<= x"25";
-- --包标识：0x088A
				-- elsif send_cnt =2 then
					-- data_out<= x"09";
				-- elsif send_cnt =3 then
					-- data_out<= x"49";
-- --包序 包计数
				-- elsif send_cnt =4 then
					-- data_out(7 downto 6)<= "11";
					-- data_out(5 downto 0)<= packet_cnt(13 downto 8);
				-- elsif send_cnt =5 then
					-- data_out<=packet_cnt(7 downto 0);
-- --包数据域长度-1：1040					
				-- elsif send_cnt =6 then
					-- data_out<=x"04";
				-- elsif send_cnt =7 then
					-- data_out<=x"0F";
				-- elsif send_cnt =14 then
					-- data_out<=x"EB";
				-- elsif send_cnt =15 then
					-- data_out<=x"90";
				-- elsif send_cnt =16 then
					-- data_out<=x"01";
				-- elsif send_cnt =17 then
					-- data_out<=x"49";
-- --				elsif send_cnt =18 then
-- --					data_out<=x"AA";
				-- elsif send_cnt =19 then
					-- data_out<=x"04";
				-- elsif send_cnt =20 then
					-- data_out<=x"08";					
				-- elsif send_cnt =21 then
					-- data_out(7 downto 6)<= "11";
					-- data_out(5 downto 0)<= packet_cnt(13 downto 8);
				-- elsif send_cnt =22 then
					-- data_out<=packet_cnt(7 downto 0);
-- --测试数据填充?	
								
				-- -- elsif send_cnt>=14 and send_cnt<=224 then
				    -- -- if test_mode='1'then
					    -- -- data_out<=conv_std_logic_vector(send_cnt-14,8);
					-- -- else
					    -- -- data_out<=data_in;
					-- -- end if;
				-- -- elsif send_cnt>=225 and send_cnt<=253 then
				    -- -- if (test_mode='1')then
						    -- -- data_out<=conv_std_logic_vector(send_cnt-14,8);
						-- -- end if;
					-- -- else
					    -- -- data_out<=data_in;
					-- -- end if;
-- --校验和
				-- -- elsif send_cnt>=1036 and send_cnt<=1045 then 
				    -- -- data_out<=x"AA";
				-- elsif send_cnt=1045 then
				    -- data_out<=checksum(7 downto 0);
				-- elsif send_cnt=1046 then
				    -- data_out<=checksum(15 downto 8);
				-- elsif send_cnt=1047 then
				    -- data_out<=checksum(7 downto 0);
				-- else
					data_out<=data_in;
--				end if;
			end if;
		end if;
	end process;

end Behavioral;

