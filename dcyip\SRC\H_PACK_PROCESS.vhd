----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    18:09:00 10/24/2022 
-- Design Name: 
-- Module Name:    PACK_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library ieee;
use IEEE.std_logic_1164.all;
USE IEEE.STD_LOGIC_ARITH.ALL;
USE IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity H_PACK_PROCESS is
port(
    reset          : in std_logic;
    clk            : in std_logic;
	
	ram_wr         : out std_logic;
	ram_din       : out std_logic_vector(7 downto 0);
	ram_waddr      : out std_logic_vector(10 downto 0);
	
	pack_edge      : in std_logic;

	-- e_sample_end1  : in std_logic;
	-- e_sample_end2  : in std_logic;
	-- e_sample_end3  : in std_logic;
	-- e_sample_end4  : in std_logic;
	-- e_sample_end5  : in std_logic;
	-- fgm_sample_end : in std_logic;
	-- gc_sample_end  : in std_logic;

	e_addr1        : out std_logic_vector(7 downto 0);
	e_addr2        : out std_logic_vector(7 downto 0);
	e_addr3        : out std_logic_vector(7 downto 0);
	e_addr4        : out std_logic_vector(7 downto 0);
	e_addr5        : out std_logic_vector(7 downto 0);
	fgm_addr       : out std_logic_vector(9 downto 0);
	gc_addr        : out std_logic_vector(7 downto 0);
	
	e_data_in1     : in std_logic_vector(7 downto 0);
	e_data_in2     : in std_logic_vector(7 downto 0);
	e_data_in3     : in std_logic_vector(7 downto 0);
	e_data_in4     : in std_logic_vector(7 downto 0);
    e_data_in5     : in std_logic_vector(7 downto 0);	
    fgm_data_in    : in std_logic_vector(7 downto 0);
    gc_data_in     : in std_logic_vector(7 downto 0);	
	
	cmd_count_error: in std_logic_vector(7 downto 0);
    cmd_error_type : in std_logic_vector(7 downto 0);
	cmd_sz_errcnt  :in std_logic_vector(7 downto 0);
    cmd_sz_errtype :in std_logic_vector(7 downto 0);
    cmd_count_lx   : in std_logic_vector(7 downto 0);
    cmd_count_time : in std_logic_vector(7 downto 0);
    cmd_count_sz   : in std_logic_vector(7 downto 0);
	cmd_sz_data    : in std_logic_vector(47 downto 0);
	work_state     : in std_logic_vector(7 downto 0);
    da1_data   : in std_logic_vector(15 downto 0);
    da2_data   : in std_logic_vector(15 downto 0);
    da3_data   : in std_logic_vector(15 downto 0);
    da4_data   : in std_logic_vector(15 downto 0);	

--时间码
    s_data        : in std_logic_vector(31 downto 0);
	ms_data       : in std_logic_vector(15 downto 0);		
--指令类型及计数等	

	pack_end       : out std_logic
);	
end H_PACK_PROCESS;

architecture Behavioral of H_PACK_PROCESS is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal e_rd1   :std_logic;
signal e_rd2   :std_logic;
signal e_rd3   :std_logic;
signal e_rd4   :std_logic;
signal e_rd5   :std_logic;
signal gc_rd   :std_logic;
signal fgm_rd  :std_logic;
signal time_rd :std_logic;
signal time_rd2 :std_logic;
signal rd_cmd       : std_logic;
signal blank_rd:std_logic;
signal rd_head1 :std_logic;
signal rd_head2 :std_logic;
signal rd_sum1 :std_logic;
signal rd_sum2 :std_logic;

signal reg_e_rd1   :std_logic;
signal reg_e_rd2   :std_logic;
signal reg_e_rd3   :std_logic;
signal reg_e_rd4   :std_logic;
signal reg_e_rd5   :std_logic;
signal reg_gc_rd   :std_logic;
signal reg_fgm_rd  :std_logic;
signal reg_time_rd :std_logic;
signal reg_rd_cmd   : std_logic;
signal reg_blank_rd :std_logic;

signal reg_time_rd2 :std_logic;
signal reg_rd_head1   : std_logic;
signal reg_rd_head2 :std_logic;
signal reg_rd_sum1   : std_logic;
signal reg_rd_sum2 :std_logic;

signal reg2_time_rd:std_logic;
signal reg2_rd_head1:std_logic;
signal reg2_rd_sum1:std_logic;
signal reg2_rd_sum2:std_logic;

signal reg2_e_rd1   :std_logic;
signal reg2_e_rd2   :std_logic;
signal reg2_e_rd3   :std_logic;
signal reg2_e_rd4   :std_logic;
signal reg2_e_rd5   :std_logic;
signal reg2_gc_rd   :std_logic;
signal reg2_fgm_rd  :std_logic;
--signal reg2_rd_cmd   : std_logic;
--signal reg2_blank_rd :std_logic;





signal time_reg : std_logic_vector(47 downto 0);
signal sz_data_reg:std_logic_vector(47 downto 0);

signal	wr_cnt : std_logic_vector(10 downto 0);
signal  cnt :std_logic_vector(31 downto 0);
signal pack_mode:std_logic_vector(7 downto 0);

signal checksum: std_logic_vector(15 downto 0);
signal checksum1: std_logic_vector(7 downto 0);
signal packet_cnt: std_logic_vector(15 downto 0);

signal wr        : std_logic;
signal data_out  : std_logic_vector(7 downto 0);
signal addr      : std_logic_vector(10 downto 0);

-- signal efi_cnt  :std_logic_vector(7 downto 0);
-- signal d_efi_cnt:std_logic_vector(5 downto 0);
-- signal fgm_cnt  :std_logic_vector(9 downto 0);
-- signal blank_cnt:std_logic_vector(4 downto 0);

-- signal e_addr_wr1:std_logic_vector(10 downto 0);
-- signal e_addr_wr2:std_logic_vector(10 downto 0);
-- signal e_addr_wr3:std_logic_vector(10 downto 0);
-- signal e_addr_wr4:std_logic_vector(10 downto 0);
-- signal e_addr_wr5:std_logic_vector(10 downto 0);
-- signal fgm_addr_wr:std_logic_vector(10 downto 0);
-- signal blank_addr_wr:std_logic_vector(10 downto 0);
signal e_addr_wr1:integer range 0 to 60;
signal e_addr_wr2:integer range 0 to 220;
signal e_addr_wr3:integer range 0 to 380;
signal e_addr_wr4:integer range 0 to 540;
signal e_addr_wr5:integer range 0 to 700;
signal fgm_addr_wr:integer range 0 to 800;
signal blank_addr_wr:integer range 0 to 1048;

signal efi_cnt  :integer range 0 to 160;
signal d_efi_cnt:integer range 0 to 60;
signal fgm_cnt  :integer range 0 to 700;
signal blank_cnt:integer range 0 to 16;

signal pack_ends:std_logic;
signal pack_edge_reg:std_logic;



type state_t is (start_s,check_s,rev_time,rev_time2,rev_eif1,rev_eif2,rev_eif3,rev_eif4,rev_eif5,rev_fgm,rev_gc,rev_wait1,rev_cmd,rev_blank,rev_head1,rev_head2,rev_sum1,rev_sum2,end_s);
signal state: state_t;

begin
-- process(reset,clk)
	-- begin
		-- if reset='0' then
			-- ram_wr<='0';
			-- ram_din<=(others=>'0');
			-- ram_waddr<=(others=>'0');
		-- else
            -- if clk ='1' and clk'event then
                ram_wr    <=   wr  ;
	            ram_din   <=   data_out ;
	            ram_waddr <=   addr ;
				pack_end<= pack_ends;
			-- end if;
		-- end if;
	-- end process;

process(reset,clk)
	begin
		if reset='0' then
			time_reg<=(others=>'0');
		else
            if clk ='1' and clk'event then
                if pack_edge='1' then 
				    time_reg(47 downto 16)<=s_data;
                    time_reg(15 downto 0)<=ms_data;
				end if;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
			pack_edge_reg<='0';
		else
            if clk ='1' and clk'event then
               pack_edge_reg<=pack_edge;
			end if;
		end if;
	end process;

process(reset,clk)
	begin
		if reset='0' then
			-- pack_mode<=(others=>'0');efi_cnt<=(others=>'0');d_efi_cnt<=(others=>'0');fgm_cnt<=(others=>'0');blank_cnt<=(others=>'0');
			-- e_addr_wr1<=(others=>'0');e_addr_wr2<=(others=>'0');e_addr_wr3<=(others=>'0');e_addr_wr4<=(others=>'0');e_addr_wr5<=(others=>'0');
			-- fgm_addr_wr<=(others=>'0');blank_addr_wr<=(others=>'0');
			pack_mode<=(others=>'0');
			efi_cnt<=160;d_efi_cnt<=48;fgm_cnt<=288;blank_cnt<=9;
			e_addr_wr1<=60;e_addr_wr2<=220;e_addr_wr3<=380;e_addr_wr4<=540;e_addr_wr5<=700;
			fgm_addr_wr<=748;blank_addr_wr<=1036;
		else
            if clk ='1' and clk'event then
                if pack_edge_reg='1' then 
--                if pack_ends='1' then 
				    if work_state(1)='0' then 
					    pack_mode<=x"DD"; 
						efi_cnt<=57; d_efi_cnt<=57; fgm_cnt<=684;blank_cnt<=16;
					     e_addr_wr1<=60;e_addr_wr2<=117;e_addr_wr3<=174;e_addr_wr4<=231;e_addr_wr5<=288;
			             fgm_addr_wr<=345;blank_addr_wr<=1029;
                    else pack_mode<=x"AA";		 
						 efi_cnt<=160; d_efi_cnt<=48; fgm_cnt<=288;blank_cnt<=9;
						e_addr_wr1<=60;e_addr_wr2<=220;e_addr_wr3<=380;e_addr_wr4<=540;e_addr_wr5<=700;
			            fgm_addr_wr<=748;blank_addr_wr<=1036;
					end if;	
				end if;
			end if;
		end if;
	end process;
						  
						   



process(reset,clk)
	begin
		if reset='0' then
		    state<=start_s;	cnt<= (others=>'0');pack_ends<='0';
			e_addr1<=(others=>'0');e_addr2<=(others=>'0');e_addr3<=(others=>'0');e_addr4<=(others=>'0');e_addr5<=(others=>'0');fgm_addr<=(others=>'0');gc_addr<=(others=>'0');
			e_rd1<='0';e_rd2<='0';e_rd3<='0';e_rd4<='0';e_rd5<='0';gc_rd<='0';fgm_rd<='0';time_rd<='0';blank_rd<='0';rd_cmd<='0';
			rd_head1<='0';rd_head2<='0';rd_sum1<='0';rd_sum2<='0';time_rd2<='0';sz_data_reg<=(others=>'0');
		else
            if clk ='1' and clk'event then
                case state is
                    when start_s =>
					    cnt<= (others=>'0');state<=check_s; pack_ends<='0';
					when check_s =>
					    if pack_edge_reg= '1' then
						   cnt <= (others=>'0'); state<=rev_time; 					   
						end if;
--接收时间码
					when rev_time=>
					    if(cnt<6) then 
						   time_rd<='1'; cnt <= cnt+1;
						else 
						   time_rd<='0'; cnt<=(others=>'0') ; state<=rev_time2;						   
						end if;
					when rev_time2=>
					    if(cnt<6) then 
						   time_rd2<='1'; cnt <= cnt+1;
						else 
						   time_rd2<='0'; cnt<=(others=>'0') ; state<=rev_eif1;						   
						end if;
--接收电场单探头电压数据						
					-- when rev_eif1=>
					    -- if(cnt<efi_cnt) then 
						   -- e_rd1<='1'; e_addr1<=cnt(7 downto 0); cnt <= cnt+1;
						-- else
						   -- cnt<=(others=>'0');e_rd1<='0'; e_addr1<=(others=>'0');state<=rev_eif2; 
						-- end if;		
                    -- when rev_eif2=>
					    -- if(cnt<efi_cnt) then 
						   -- e_rd2<='1'; e_addr2<=cnt(7 downto 0); cnt <= cnt+1;
						-- else
						   -- cnt<=(others=>'0');e_rd2<='0'; e_addr2<=(others=>'0');state<=rev_eif3; 
						-- end if;	
					when rev_eif1=>
					    if(cnt<efi_cnt) then 
						   e_rd1<='1'; e_addr1<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd1<='0'; e_addr1<=(others=>'0');state<=rev_eif2; 
						end if;		
					 when rev_eif2=>
					    if(cnt<efi_cnt) then 
						   e_rd2<='1'; e_addr2<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd2<='0'; e_addr2<=(others=>'0');state<=rev_eif3; 
						end if;
                    when rev_eif3=>
					    if(cnt<efi_cnt) then 
						   e_rd3<='1'; e_addr3<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd3<='0'; e_addr3<=(others=>'0');state<=rev_eif4; 
						end if;	
                    when rev_eif4=>
					    if(cnt<efi_cnt) then 
						   e_rd4<='1'; e_addr4<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd4<='0'; e_addr4<=(others=>'0');state<=rev_eif5; 
						end if;	
--接收电场差分电压						
                    when rev_eif5=>
					    if(cnt<d_efi_cnt) then 
						   e_rd5<='1'; e_addr5<=cnt(7 downto 0); cnt <= cnt+1;
						else
						   cnt<=(others=>'0');e_rd5<='0'; e_addr5<=(others=>'0');state<=rev_fgm; 
						end if;							
--接收磁场数据
					when rev_fgm=>
					    if(cnt<fgm_cnt) then 
						   fgm_rd<='1'; fgm_addr<=cnt(9 downto 0);cnt <= cnt+1;
						else 
						   fgm_rd<='0'; cnt<=(others=>'0'); fgm_addr<=(others=>'0');state<=rev_gc;
						end if;
--接收电压温度数据 
                    when rev_gc=>
					    -- if(cnt<1) then 
						   -- gc_rd<='1'; gc_addr<=cnt(7 downto 0);cnt <= cnt+1;
                        if(cnt<10) then
						   gc_rd<='1'; gc_addr<=cnt(7 downto 0);cnt <= cnt+1;
						else 
						   gc_rd<='0'; cnt<=(others=>'0'); gc_addr<=(others=>'0');state<=rev_wait1;
						end if;
--指令计数和工作状态，DA
                    when rev_wait1=>
                         state<=rev_cmd;
					when rev_cmd=>
					    if (cnt<23) then
						    rd_cmd<='1'; cnt <= cnt+1;
						else 
						    rd_cmd<='0'; cnt<=(others=>'0'); state<=rev_blank;						   
						end if;		
                        if cnt=7 then sz_data_reg<=cmd_sz_data; end if;
 						
					when rev_blank=>
					    if (cnt<blank_cnt) then
						    blank_rd<='1';cnt<=cnt+1;
						else
						    blank_rd<='0';cnt<=(others=>'0');state<=rev_head1;
						end if;	
--包头，校验和		
                    when rev_head1=>
                        if(cnt<8) then 
                            rd_head1<='1'; cnt<=cnt+1;
                        else 
                            rd_head1<='0'; cnt<=(others=>'0'); state<=rev_head2;
						end if;
                    when rev_head2=>
                        if(cnt<9) then 
                            rd_head2<='1'; cnt<=cnt+1;
                        else 
                            rd_head2<='0'; cnt<=(others=>'0'); state<=rev_sum1;
						end if;
                    when rev_sum1=>
                        if(cnt<1) then 
                            rd_sum1<='1'; cnt<=cnt+1;
                        else 
                            rd_sum1<='0'; cnt<=(others=>'0'); state<=rev_sum2;
						end if;
                    when rev_sum2=>
                        if(cnt<2) then 
                            rd_sum2<='1'; cnt<=cnt+1;
                        else 
                            rd_sum2<='0'; cnt<=(others=>'0'); state<=end_s;	
                        end if;							
					when end_s =>
					    pack_ends <='1';state<=start_s;
                     when others => state<=start_s;
				end case;
			end if;
		end if;
	end process;


	process(reset,clk)
	begin
		if reset='0' then
		    reg_e_rd1   <='0' ;
			reg_e_rd2   <='0' ;
			reg_e_rd3   <='0' ;
			reg_e_rd4   <='0' ;
			reg_e_rd5   <='0' ;
			reg_gc_rd   <='0' ;
			reg_fgm_rd  <='0' ;
			reg_time_rd <='0' ;
            reg_rd_cmd <='0';			
			reg_blank_rd<='0' ;
			reg_time_rd2 <='0' ;
			reg_rd_head1 <='0' ;
			reg_rd_head2 <='0' ;
			reg_rd_sum1 <='0' ;
			reg_rd_sum2 <='0' ;
			reg2_time_rd  <= '0';
			reg2_rd_head1 <= '0';
			reg2_rd_sum1  <= '0';
			reg2_rd_sum2  <= '0';
		    reg2_e_rd1   <='0' ;
			reg2_e_rd2   <='0' ;
			reg2_e_rd3   <='0' ;
			reg2_e_rd4   <='0' ;
			reg2_e_rd5   <='0' ;
			reg2_gc_rd   <='0' ;
			reg2_fgm_rd  <='0' ;
--			reg2_rd_cmd  <='0';
			
		else
		    if clk ='1' and clk'event then
	        reg_e_rd1   <= e_rd1   ;
			reg_e_rd2   <= e_rd2   ;
			reg_e_rd3   <= e_rd3   ;
			reg_e_rd4   <= e_rd4   ;
			reg_e_rd5   <= e_rd5   ;
			reg_gc_rd   <= gc_rd   ;
			reg_fgm_rd  <= fgm_rd  ;
			reg_time_rd <=time_rd ;
			reg_rd_cmd <= rd_cmd  ;
			reg_blank_rd <= blank_rd ;
			reg_time_rd2 <=time_rd2 ;
			reg_rd_head1 <=rd_head1 ;
			reg_rd_head2 <=rd_head2 ;
			reg_rd_sum1 <=rd_sum1 ;
			reg_rd_sum2 <=rd_sum2 ;	
			reg2_time_rd  <= reg_time_rd ;
			reg2_rd_head1 <= reg_rd_head1;
			reg2_rd_sum1  <= reg_rd_sum1 ;
			reg2_rd_sum2  <= reg_rd_sum2 ;
		    reg2_e_rd1   <=reg_e_rd1  ;
			reg2_e_rd2   <=reg_e_rd2  ;
			reg2_e_rd3   <=reg_e_rd3  ;
			reg2_e_rd4   <=reg_e_rd4  ;
			reg2_e_rd5   <=reg_e_rd5  ;
			reg2_gc_rd   <=reg_gc_rd  ;
			reg2_fgm_rd  <=reg_fgm_rd ;	
--            reg2_rd_cmd  <= reg_rd_cmd;			
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
		    wr<='0'; 
			addr<=(others=>'0'); 
			data_out<=(others=>'0'); 
			wr_cnt<=(others=>'0');
		else
		    if clk ='1' and clk'event then
--电场交流			
			    if(reg2_e_rd1='1')then
			        wr<='1'; addr<=e_addr_wr1+wr_cnt; data_out<= e_data_in1;wr_cnt<=wr_cnt+1; 
				elsif(reg2_e_rd2='1') then
				    wr<='1'; addr<=e_addr_wr2+wr_cnt; data_out<= e_data_in2;wr_cnt<=wr_cnt+1; 
				elsif(reg2_e_rd3='1') then
				    wr<='1'; addr<=e_addr_wr3+wr_cnt; data_out<= e_data_in3;wr_cnt<=wr_cnt+1; 
				elsif(reg2_e_rd4='1') then
				    wr<='1'; addr<=e_addr_wr4+wr_cnt; data_out<= e_data_in4;wr_cnt<=wr_cnt+1; 
				elsif(reg2_e_rd5='1') then
				    wr<='1'; addr<=e_addr_wr5+wr_cnt; data_out<= e_data_in5;wr_cnt<=wr_cnt+1; 
				elsif(reg2_fgm_rd='1') then
				    wr<='1'; addr<=fgm_addr_wr+wr_cnt; data_out<= fgm_data_in;wr_cnt<=wr_cnt+1; 
				elsif(reg2_gc_rd='1') then
                    if(wr_cnt=0)then
					    wr<='1';  addr<=43+wr_cnt; data_out<= gc_data_in; wr_cnt<=wr_cnt+1;
				    elsif(wr_cnt=1)then							 
					    wr<='0';   wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt>=2 and wr_cnt<=7)then							 
					    wr<='1';  addr<=52+wr_cnt; data_out<= gc_data_in; wr_cnt<=wr_cnt+1;
					elsif (wr_cnt>=8 and wr_cnt<=9)then	
					    wr<='1';  addr<=44+wr_cnt; data_out<= gc_data_in; wr_cnt<=wr_cnt+1;
					else 
                        wr<='0'; addr<=(others=>'0');  wr_cnt<=(others=>'0');  
                    end if;						 
				elsif(reg_time_rd='1')then 
				    if(wr_cnt=0)then
				       wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(47 downto 40);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=1)then
				       wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(39 downto 32);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=2) then
					   wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(31 downto 24);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=3) then
					   wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(23 downto 16);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=4) then
					   wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(15 downto 8);wr_cnt<=wr_cnt+1;    
					elsif(wr_cnt=5) then
					   wr<='1';  addr<=8+wr_cnt; data_out<= time_reg(7 downto 0);wr_cnt<=wr_cnt+1; 
					else
					    wr<='0'; addr<=(others=>'0');  wr_cnt<=(others=>'0');
					end if;
				elsif(reg_time_rd2='1')then 
				    if(wr_cnt=0) then
					   wr<='1';  addr<=23+wr_cnt; data_out<= time_reg(47 downto 40);wr_cnt<=wr_cnt+1;
					elsif(wr_cnt=1)then
				       wr<='1';  addr<=23+wr_cnt; data_out<= time_reg(39 downto 32);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=2) then
					   wr<='1';  addr<=23+wr_cnt; data_out<= time_reg(31 downto 24);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=3) then
					   wr<='1';  addr<=23+wr_cnt; data_out<= time_reg(23 downto 16);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=4) then
					   wr<='1';  addr<=23+wr_cnt; data_out<= time_reg(15 downto 8);wr_cnt<=wr_cnt+1;    
					elsif(wr_cnt=5) then
					   wr<='1';  addr<=23+wr_cnt; data_out<= time_reg(7 downto 0);wr_cnt<=wr_cnt+1;   
					else
					   wr<='0'; wr_cnt<=(others=>'0');addr<=(others=>'0'); 
					end if;
				elsif(reg_rd_cmd='1') then
				    if(wr_cnt=0)then
				       wr<='1';  addr<=29+wr_cnt; data_out<= cmd_count_lx;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=1) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= cmd_count_time;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=2) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= cmd_count_sz;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=3) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= cmd_sz_errcnt;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=4) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= cmd_sz_errtype;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=5) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= cmd_count_error;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=6) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= cmd_error_type;wr_cnt<=wr_cnt+1; 			   
					elsif(wr_cnt=7) then
					    wr<='1';  addr<=29+wr_cnt; data_out<= sz_data_reg(47 downto 40);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=8)then
				       wr<='1';  addr<=29+wr_cnt; data_out<= sz_data_reg(39 downto 32);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=9) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= sz_data_reg(31 downto 24);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=10) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= sz_data_reg(23 downto 16);wr_cnt<=wr_cnt+1;    
					elsif(wr_cnt=11) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= sz_data_reg(15 downto 8);wr_cnt<=wr_cnt+1; 
				    elsif(wr_cnt=12) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= sz_data_reg(7 downto 0);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=13) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= work_state;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=14) then
					   wr<='1';  addr<=4+wr_cnt; data_out<=pack_mode;wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=15) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da1_data(15 downto 8);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=16) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da1_data(7 downto 0);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=17) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da2_data(15 downto 8);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=18) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da2_data(7 downto 0);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=19) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da3_data(15 downto 8);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=20) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da3_data(7 downto 0);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=21) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da4_data(15 downto 8);wr_cnt<=wr_cnt+1; 
					elsif(wr_cnt=22) then
					   wr<='1';  addr<=29+wr_cnt; data_out<= da4_data(7 downto 0);wr_cnt<=wr_cnt+1; 
					else
					    wr<='0'; wr_cnt<=(others=>'0');addr<=(others=>'0'); 
					end if;				
				elsif (reg_blank_rd='1')then			    
				    wr<='1'; addr<=blank_addr_wr+wr_cnt; data_out<=x"aa";wr_cnt<=wr_cnt+1; 
				elsif (reg_rd_head1='1')then 
                    if(wr_cnt=0)then
				        wr<='1';  addr<=wr_cnt; data_out<= x"E2";wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=1)then
				        wr<='1';  addr<=wr_cnt; data_out<= x"25";wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=2)then
				        wr<='1';  addr<=wr_cnt; data_out<= x"09";wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=3)then
				        wr<='1';  addr<=wr_cnt; data_out<= x"49";wr_cnt<=wr_cnt+1;	
                    elsif(wr_cnt=4)then
				        wr<='1';  addr<=wr_cnt; data_out(7 downto 6)<= "11";data_out(5 downto 0)<= packet_cnt(13 downto 8);wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=5)then
				        wr<='1';  addr<=wr_cnt; data_out<=packet_cnt(7 downto 0);wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=6)then
				        wr<='1';  addr<=wr_cnt; data_out<= x"04";wr_cnt<=wr_cnt+1;		
                    elsif(wr_cnt=7)then
				        wr<='1';  addr<=wr_cnt; data_out<= x"0F";wr_cnt<=wr_cnt+1;	
					else
					    wr<='0'; wr_cnt<=(others=>'0');addr<=(others=>'0'); 
					end if;		
				elsif (reg_rd_head2='1')then 		
                    if(wr_cnt=0)then
				        wr<='1';  addr<=14+wr_cnt; data_out<= x"EB";wr_cnt<=wr_cnt+1;		
                    elsif(wr_cnt=1)then
				        wr<='1';  addr<=14+wr_cnt; data_out<= x"90";wr_cnt<=wr_cnt+1;		
                    elsif(wr_cnt=2)then
				        wr<='1';  addr<=14+wr_cnt; data_out<= x"01";wr_cnt<=wr_cnt+1;	
                    elsif(wr_cnt=3)then
				        wr<='1';  addr<=14+wr_cnt; data_out<= x"49";wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=4)then
				        wr<='0';  addr<=14+wr_cnt; data_out<= x"AA";wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=5)then
				        wr<='1';  addr<=14+wr_cnt; data_out<= x"04";wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=6)then
				        wr<='1';  addr<=14+wr_cnt; data_out<= x"08";wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=7)then
				        wr<='1';  addr<=14+wr_cnt; data_out(7 downto 6)<= "11";data_out(5 downto 0)<= packet_cnt(13 downto 8);wr_cnt<=wr_cnt+1;	
                    elsif(wr_cnt=8)then
				        wr<='1';  addr<=14+wr_cnt; data_out<= packet_cnt(7 downto 0);wr_cnt<=wr_cnt+1;	
					else
					    wr<='0'; wr_cnt<=(others=>'0');addr<=(others=>'0'); 
					end if;		
				elsif (reg_rd_sum1='1')then 		
                    if(wr_cnt=0)then
				        wr<='1';  addr<=1045+wr_cnt; data_out<= checksum1;wr_cnt<=wr_cnt+1;
					else
					    wr<='0'; wr_cnt<=(others=>'0');addr<=(others=>'0'); 
                    end if;						
				elsif (reg_rd_sum2='1')then 		
                    if(wr_cnt=0)then
				        wr<='1';  addr<=1046+wr_cnt; data_out<= checksum(15 downto 8);wr_cnt<=wr_cnt+1;
                    elsif(wr_cnt=1)then
				        wr<='1';  addr<=1046+wr_cnt; data_out<= checksum(7 downto 0);wr_cnt<=wr_cnt+1;
                    else
					    wr<='0'; wr_cnt<=(others=>'0');addr<=(others=>'0'); 
					end if;						
                else
                    wr<='0'; addr<=(others=>'0');  wr_cnt<=(others=>'0'); 						
	            end if;
			end if;
		end if;
	end process;
	
	
	process(reset,clk)
	begin
		if reset ='0' then
			packet_cnt <=(others=>'0');
		else
			if clk='1' and clk'event then
				if state=end_s then
					if packet_cnt=x"3FFF" then
						packet_cnt<=(others=>'0');
					else
						packet_cnt <=packet_cnt+1;
					end if;
				end if;
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset='0' then
			checksum<=x"0000";
			checksum1<=x"00";
		else
			if clk='1' and clk'event then
				if pack_edge ='1' then
					checksum<=x"0000";
					checksum1<=x"00";
				else
					if wr='1' then
					    if reg2_rd_head1='1'or reg2_rd_sum2='1' then 
						    checksum<=checksum;
						else
						    checksum<=conv_std_logic_vector(conv_integer(checksum)+conv_integer(data_out),16);
						end if;
						if reg2_rd_head1='1'or reg2_rd_sum2='1' or reg2_rd_sum1='1' or reg2_time_rd='1' then
						    checksum1<=checksum1;
						else
						    checksum1<=conv_std_logic_vector(conv_integer(checksum1)+conv_integer(data_out),8);
						end if;
					end if;
				end if;
			end if;
		end if;
	end process;
	
end Behavioral;	