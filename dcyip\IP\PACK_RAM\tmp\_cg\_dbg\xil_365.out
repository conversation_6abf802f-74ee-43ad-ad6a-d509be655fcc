SET_PARAMETER use_rstb_pin false
SET_PARAMETER pipeline_stages 0
SET_PARAMETER assume_synchronous_clk true
SET_PARAMETER use_regcea_pin false
SET_PARAMETER axi_id_width 4
SET_PARAMETER softecc false
SET_PARAMETER load_init_file false
SET_PARAMETER port_a_write_rate 50
SET_PARAMETER disable_collision_warnings false
SET_PARAMETER use_byte_write_enable false
SET_PARAMETER ecc false
SET_PARAMETER primitive 8kx2
SET_PARAMETER port_b_clock 100
SET_PARAMETER remaining_memory_locations 0
SET_PARAMETER memory_type Simple_Dual_Port_RAM
SET_PARAMETER register_porta_input_of_softecc false
SET_PARAMETER port_a_clock 100
SET_PARAMETER read_width_a 8
SET_PARAMETER disable_out_of_range_warnings false
SET_PARAMETER read_width_b 8
SET_PARAMETER register_portb_output_of_softecc false
SET_PARAMETER byte_size 9
SET_PARAMETER register_portb_output_of_memory_core false
SET_PARAMETER use_regceb_pin false
SET_PARAMETER register_porta_output_of_memory_core false
SET_PARAMETER reset_memory_latch_a false
SET_PARAMETER reset_memory_latch_b false
SET_PARAMETER register_porta_output_of_memory_primitives false
SET_PARAMETER use_error_injection_pins false
SET_PARAMETER enable_a Always_Enabled
SET_PARAMETER enable_b Always_Enabled
SET_PARAMETER port_a_enable_rate 100
SET_PARAMETER use_axi_id false
SET_PARAMETER write_depth_a 2048
SET_PARAMETER algorithm Minimum_Area
SET_PARAMETER use_bram_block Stand_Alone
SET_PARAMETER output_reset_value_a 0
SET_PARAMETER output_reset_value_b 0
SET_PARAMETER error_injection_type Single_Bit_Error_Injection
SET_PARAMETER port_b_write_rate 0
SET_PARAMETER ecctype No_ECC
SET_PARAMETER write_width_a 8
SET_PARAMETER write_width_b 8
SET_PARAMETER component_name RAM_A
SET_PARAMETER reset_priority_a CE
SET_PARAMETER reset_priority_b CE
SET_PARAMETER operating_mode_a WRITE_FIRST
SET_PARAMETER additional_inputs_for_power_estimation false
SET_PARAMETER operating_mode_b WRITE_FIRST
SET_PARAMETER interface_type Native
SET_PARAMETER mem_file no_Mem_file_loaded
SET_PARAMETER reset_type SYNC
SET_PARAMETER register_portb_output_of_memory_primitives false
SET_PARAMETER use_rsta_pin false
SET_PARAMETER port_b_enable_rate 100
SET_PARAMETER coe_file no_coe_file_loaded
SET_PARAMETER fill_remaining_memory_locations false
SET_PARAMETER axi_slave_type Memory_Slave
SET_PARAMETER axi_type AXI4_Full
SET_PARAMETER enable_32bit_address false
SET_PARAMETER collision_warnings ALL
SET_ERROR_CODE 2
SET_ERROR_MSG  CANCEL: Customization cancelled.
SET_ERROR_TEXT Finished initializing IP model.	
