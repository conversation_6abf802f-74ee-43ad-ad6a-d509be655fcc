library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity MAIN_CTRL is
    Port (  
			clk : in  STD_LOGIC;
			reset : in std_logic;
			mp_enable: in std_logic;
		   
			sample_edge: out std_logic; 		   
			pack_edge : out  STD_LOGIC;--200ms
			time_edge1: out std_logic;  --2.5ms
			time_edge2: out std_logic;  --12.5ms
			time_edge3: out std_logic;  --25ms
           
			mode_change : in std_logic;
            cmd_mode    : in std_logic_vector(7 downto 0);	        
	        fgm_state:   in std_logic_vector(3 downto 0);
            fgm_state_change:	in  std_logic;
	        
	        work_mode  : out std_logic_vector(7 downto 0);
	        work_mode_pack  : out std_logic_vector(7 downto 0)
		   );

end MAIN_CTRL;

architecture Behavioral of MAIN_CTRL is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal cnt: integer range 0 to 200;
signal clk_cnt: integer range 0 to 80000;
signal time_cnt1: integer range 0 to 40;
signal time_cnt2: integer range 0 to 40;
signal time_cnt3: integer range 0 to 40;
signal time_cnt4: integer range 0 to 800;
signal time_edge:std_logic;
signal pack_edge1:std_logic;
signal cmd_state:std_logic_vector(7 downto 0);
signal work_mode_up:std_logic_vector(7 downto 0);

begin

work_mode<=work_mode_up;
pack_edge<=pack_edge1;

--sample_edge
    process(reset,clk)
	begin
		if reset = '0' then
            cnt<= 0;
        else
            if clk'event and clk ='1' then
                if mp_enable='0' then 	
				    cnt<= 0;
				else
				    if cnt >=195 then
				    	cnt<=1;	
				    else
				    	cnt<=cnt+1;
				    end if;
				end if;
			end if;
		end if;
	end process;
	
	process(clk,reset)
    begin
        if reset ='0' then
            sample_edge<='0';
        else
            if clk='1' and clk'event then
				if cnt=1 then
                   sample_edge <='1';
                else
                   sample_edge <='0';
                end if;
            end if;
        end if;
    end process;

process(reset,clk)
	begin
		if reset = '0' then
            clk_cnt<=0;
		else 
		    if clk'event and clk ='1' then	
			    if mp_enable='0' then 
				    clk_cnt<=0;
				else 
			        if clk_cnt>=79999 then 
				        clk_cnt<=0;
				    else 
			            clk_cnt<=clk_cnt+1;
				    end if;
				end if;
			end if;
		end if;
	end process;
	
--time_edge：2.5ms,以2.5ms为基准，触发其他采样信号
process(reset,clk)
	begin
		if reset = '0' then
			time_edge<='0';
		else 
		    if clk'event and clk ='1' then	
			    if mp_enable='0' then 
				    time_edge<='0';
				else
				    if clk_cnt>=79999 then 
			            time_edge<='1';
					else 
					    time_edge<='0';
					end if;
				end if;
			end if;
		end if;
	end process;


	
--time_edge1，高速包2.5ms，低速包100ms,电场采样信号	
process(reset,clk)
	begin
		if reset = '0' then
            time_cnt1<=0;
		else 
		    if clk'event and clk ='1' then	
			    if mp_enable='0' then 
				    time_cnt1<=0;
				else
				    if time_edge='1' then 
			            if work_mode_up(1)='1' then 
					        time_cnt1<=0;
						else 
						    if time_cnt1>=39 then 
					    	    time_cnt1<=0;
					    	else 
					            time_cnt1<=time_cnt1+1;
							end if;
						end if;
					end if;
				end if;
			end if;
		end if;
	end process;	
--time_edge2，高速包12.5ms，低速包100ms,差分采样信号
	process(reset,clk)
	begin
		if reset = '0' then
            time_cnt2<=0;
		else 
		    if clk'event and clk ='1' then	
			    if mp_enable='0' then 
				    time_cnt2<=0;
				else
				    if time_edge='1' then 
			            if  work_mode_up(1)='1' then 
					        if time_cnt2>=4 then 
					    	    time_cnt2<=0;
					    	else 
					            time_cnt2<=time_cnt2+1;
							end if;
						else 
						    if time_cnt2>=39 then 
					    	    time_cnt2<=0;
					    	else 
					            time_cnt2<=time_cnt2+1;
							end if;
						end if;
					end if;
				end if;
			end if;
		end if;
	end process;
--time_edge3，高速包25ms，低速包100ms, 磁场数据采样信号			
	process(reset,clk)
	begin
		if reset = '0' then
            time_cnt3<=0;
		else 
		    if clk'event and clk ='1' then	
			    if mp_enable='0' then 
				    time_cnt3<=0;
				else
				    if time_edge='1' then 
			            if  work_mode_up(1)='1' then 
					        if time_cnt3>=9 then 
					    	    time_cnt3<=0;
					    	else 
					            time_cnt3<=time_cnt3+1;
							end if;
						else 
						    if time_cnt3>=39 then 
					    	    time_cnt3<=0;
					    	else 
					            time_cnt3<=time_cnt3+1;
							end if;
						end if;
					end if;
				end if;
			end if;
		end if;
	end process;
	
--pack_edge，高速包200ms，低速包1.9s, 数据包组包信号
process(reset,clk)
	begin
		if reset = '0' then
            time_cnt4<=0;
			pack_edge1<='0';
		else 
		    if clk'event and clk ='1' then	
			    if mp_enable='0' then 
				    time_cnt4<=0;pack_edge1<='0';
				else
				    if time_edge='1' then 
			            if  work_mode_up(1)='1' then 
					        if time_cnt4>=79 then 
					    	    time_cnt4<=0;pack_edge1<='1';
					    	else 
					            time_cnt4<=time_cnt4+1;pack_edge1<='0';
							end if;
						else 
						    if time_cnt4>=759 then 
					    	    time_cnt4<=0;pack_edge1<='1';
					    	else 
					            time_cnt4<=time_cnt4+1;pack_edge1<='0';
							end if;
						end if;
					else 
					    pack_edge1<='0';
					end if;
				end if;
			end if;
		end if;
	end process;    
	
process(reset,clk)
	begin
		if reset = '0' then
            time_edge1<='0';
			time_edge2<='0';
			time_edge3<='0';
		else 
		    if clk'event and clk ='1' then	
			    if mp_enable='0' then 
				    time_edge1<='0';
			        time_edge2<='0';
			        time_edge3<='0';
				else 
				    if time_cnt1=0 and clk_cnt=10 then  time_edge1<='1'; else  time_edge1<='0'; end if;
					if time_cnt2=0 and clk_cnt=10 then  time_edge2<='1'; else  time_edge2<='0'; end if;
					if time_cnt3=0 and clk_cnt=10 then  time_edge3<='1'; else  time_edge3<='0'; end if;
				end if;
			end if;
		end if;
	end process;
	
	-- process(reset,clk)
	-- begin
		-- if reset = '0' then
            -- time_edge1<='0';
			-- time_edge2<='0';
			-- time_edge3<='0';
		-- else 
		    -- if clk'event and clk ='1' then	
			    -- if mp_enable='0' then 
				    -- time_edge1<='0';
			        -- time_edge2<='0';
			        -- time_edge3<='0';
				-- else 
				    -- if time_cnt1=0 and clk_cnt=10 then  time_edge1<='1'; else  time_edge1<='0'; end if;
					-- if time_cnt2=0 and clk_cnt=10 then  time_edge2<='1'; else  time_edge2<='0'; end if;
					-- if time_cnt3=0 and clk_cnt=10 then  time_edge3<='1'; else  time_edge3<='0'; end if;
				-- end if;
			-- end if;
		-- end if;
	-- end process;

--更新工作状态标识	
	process(reset,clk)
	begin
		if reset='0' then
            cmd_state<=x"F2";
		else
		    if clk='1' and clk'event then
                if mode_change='1' then 
					if cmd_mode=x"11" then 	cmd_state(1)<='0';
					elsif cmd_mode=x"22" then 	cmd_state(1)<='1';
					elsif cmd_mode=x"44" then 	cmd_state(2)<='1';
					elsif cmd_mode=x"CC" then 	cmd_state(2)<='0';
					elsif cmd_mode=x"AA" then 	cmd_state(0)<='1';
					elsif cmd_mode=x"BB" then 	cmd_state(0)<='0';
					else cmd_state<=cmd_state;
					end if;
				end if;
			    if  fgm_state_change='1' then  
                    cmd_state(7 downto 4)<=fgm_state;
			    end if;				
			end if;
		end if;
	end process;
	
	process(reset,clk)
	begin
		if reset = '0' then
            work_mode_up<=x"F2";
			work_mode_pack<=x"F2";
		else 
		    if clk'event and clk ='1' then	
				if pack_edge1='1' then  work_mode_pack<=work_mode_up; end if;
				if time_cnt4=0 and clk_cnt=5 then work_mode_up<=cmd_state; end if;
			end if;
		end if;
	end process;
					
end Behavioral	;			