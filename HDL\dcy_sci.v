`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    19:17:47 11/29/2022 
// Design Name: 
// Module Name:    dcy_sci 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module dcy_sci(
input clk,//32M
input reset,//n active
//pmu rv
output data_out_en,
output [7:0] data_out,
//dcy
input RX_en,
input pack_end,
output [10:0] addr_rd,
input [7:0] ram_dout
    );

reg [10:0] cnt;
reg [1:0] state;
reg [1:0] cnttemp;
assign data_out_en=((state==2'd2)&(cnttemp==2'd0))?1'b1:1'b0;
assign data_out=ram_dout;
assign addr_rd=cnt;
always@(posedge clk) begin
    if(~reset) begin
        state<=2'd0;
    end
    else begin
        case(state)
            2'd0:begin
                cnt<=11'd0;
                state<=2'd1;
					 cnttemp<=2'd0;
            end
            2'd1:begin
                state<=(pack_end&RX_en)?2'd2:state;
                cnt<=(pack_end&RX_en)?11'd1:cnt;
            end
            2'd2:begin
                state<=((cnt==11'd1048)&(cnttemp==2'd3))?2'd3:state;
                cnt<=(cnttemp==2'd3)?cnt+11'd1:cnt;
					 cnttemp<=cnttemp+2'd1;
            end
            2'd3:begin
                cnt<=11'd0;
                state<=2'd0;
            end
				default:begin
					state<=2'd0;
				end
        endcase
    end
end
endmodule
