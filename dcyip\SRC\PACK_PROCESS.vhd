----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    18:09:00 10/24/2022 
-- Design Name: 
-- Module Name:    PACK_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library ieee;
use IEEE.std_logic_1164.all;
USE IEEE.STD_LOGIC_ARITH.ALL;
USE IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity PACK_PROCESS is
port(
    reset          : in std_logic;
    clk            : in std_logic;
	
	wr         : out std_logic;
	data_out       : out std_logic_vector(7 downto 0);
	addr      : out std_logic_vector(10 downto 0);
	
	pack_edge      : in std_logic;

	e_sample_end1  : in std_logic;
	e_sample_end2  : in std_logic;
	e_sample_end3  : in std_logic;
	e_sample_end4  : in std_logic;
	e_sample_end5  : in std_logic;
	fgm_sample_end : in std_logic;
	gc_sample_end  : in std_logic;

	e_addr1        : out std_logic_vector(7 downto 0);
	e_addr2        : out std_logic_vector(7 downto 0);
	e_addr3        : out std_logic_vector(7 downto 0);
	e_addr4        : out std_logic_vector(7 downto 0);
	e_addr5        : out std_logic_vector(7 downto 0);
	fgm_addr       : out std_logic_vector(9 downto 0);
	gc_addr        : out std_logic_vector(7 downto 0);
	
	e_data_in1     : in std_logic_vector(7 downto 0);
	e_data_in2     : in std_logic_vector(7 downto 0);
	e_data_in3     : in std_logic_vector(7 downto 0);
	e_data_in4     : in std_logic_vector(7 downto 0);
    e_data_in5     : in std_logic_vector(7 downto 0);	
    fgm_data_in    : in std_logic_vector(7 downto 0);
    gc_data_in     : in std_logic_vector(7 downto 0);	
	
	cmd_count_error: in std_logic_vector(7 downto 0);
    cmd_error_type : in std_logic_vector(7 downto 0);
    cmd_count_lx   : in std_logic_vector(7 downto 0);
    cmd_count_time : in std_logic_vector(7 downto 0);
    cmd_count_sz   : in std_logic_vector(7 downto 0);
	cmd_sz_data    : in std_logic_vector(47 downto 0);
	work_state     : in std_logic_vector(7 downto 0);
    da1_data   : in std_logic_vector(15 downto 0);
    da2_data   : in std_logic_vector(15 downto 0);
    da3_data   : in std_logic_vector(15 downto 0);
    da4_data   : in std_logic_vector(15 downto 0);	

--时间码
    s_data        : in std_logic_vector(31 downto 0);
	ms_data       : in std_logic_vector(15 downto 0);		
--指令类型及计数等	

	pack_end       : out std_logic
);	
end PACK_PROCESS;

architecture Behavioral of PACK_PROCESS is
component H_PACK_PROCESS 
port(
    reset          : in std_logic;
    clk            : in std_logic;
	
	ram_wr         : out std_logic;
	ram_din       : out std_logic_vector(7 downto 0);
	ram_waddr      : out std_logic_vector(10 downto 0);
	
	pack_edge      : in std_logic;

	e_sample_end1  : in std_logic;
	e_sample_end2  : in std_logic;
	e_sample_end3  : in std_logic;
	e_sample_end4  : in std_logic;
	e_sample_end5  : in std_logic;
	fgm_sample_end : in std_logic;
	gc_sample_end  : in std_logic;

	e_addr1        : out std_logic_vector(7 downto 0);
	e_addr2        : out std_logic_vector(7 downto 0);
	e_addr3        : out std_logic_vector(7 downto 0);
	e_addr4        : out std_logic_vector(7 downto 0);
	e_addr5        : out std_logic_vector(7 downto 0);
	fgm_addr       : out std_logic_vector(9 downto 0);
	gc_addr        : out std_logic_vector(7 downto 0);
	
	e_data_in1     : in std_logic_vector(7 downto 0);
	e_data_in2     : in std_logic_vector(7 downto 0);
	e_data_in3     : in std_logic_vector(7 downto 0);
	e_data_in4     : in std_logic_vector(7 downto 0);
    e_data_in5     : in std_logic_vector(7 downto 0);	
    fgm_data_in    : in std_logic_vector(7 downto 0);
    gc_data_in     : in std_logic_vector(7 downto 0);	

--时间码
    s_data        : in std_logic_vector(31 downto 0);
	ms_data       : in std_logic_vector(15 downto 0);		
--指令类型及计数等	
    cmd_count_error: in std_logic_vector(7 downto 0);
    cmd_error_type : in std_logic_vector(7 downto 0);
    cmd_count_lx   : in std_logic_vector(7 downto 0);
    cmd_count_time : in std_logic_vector(7 downto 0);
    cmd_count_sz   : in std_logic_vector(7 downto 0);
	cmd_sz_data    : in std_logic_vector(47 downto 0);
	work_state     : in std_logic_vector(7 downto 0);
	
	da1_data   : in std_logic_vector(15 downto 0);
    da2_data   : in std_logic_vector(15 downto 0);
    da3_data   : in std_logic_vector(15 downto 0);
    da4_data   : in std_logic_vector(15 downto 0);	

	pack_end       : out std_logic
);	
end component;

component L_PACK_PROCESS 
port(
    reset          : in std_logic;
    clk            : in std_logic;
	
	wr             : out std_logic;
	data_out       : out std_logic_vector(7 downto 0);
	addr           : out std_logic_vector(10 downto 0);
	
	pack_edge      : in std_logic;

	e_sample_end1  : in std_logic;
	e_sample_end2  : in std_logic;
	e_sample_end3  : in std_logic;
	e_sample_end4  : in std_logic;
	e_sample_end5  : in std_logic;
	fgm_sample_end : in std_logic;
	gc_sample_end  : in std_logic;

	e_addr1        : out std_logic_vector(7 downto 0);
	e_addr2        : out std_logic_vector(7 downto 0);
	e_addr3        : out std_logic_vector(7 downto 0);
	e_addr4        : out std_logic_vector(7 downto 0);
	e_addr5        : out std_logic_vector(7 downto 0);
	fgm_addr       : out std_logic_vector(9 downto 0);
	gc_addr        : out std_logic_vector(7 downto 0);
	
	e_data_in1     : in std_logic_vector(7 downto 0);
	e_data_in2     : in std_logic_vector(7 downto 0);
	e_data_in3     : in std_logic_vector(7 downto 0);
	e_data_in4     : in std_logic_vector(7 downto 0);
    e_data_in5     : in std_logic_vector(7 downto 0);	
    fgm_data_in    : in std_logic_vector(7 downto 0);
    gc_data_in     : in std_logic_vector(7 downto 0);	

--时间码
    s_data        : in std_logic_vector(31 downto 0);
	ms_data       : in std_logic_vector(15 downto 0);		
--指令类型及计数等	
    cmd_count_error: in std_logic_vector(7 downto 0);
    cmd_error_type : in std_logic_vector(7 downto 0);
    cmd_count_lx   : in std_logic_vector(7 downto 0);
    cmd_count_time : in std_logic_vector(7 downto 0);
    cmd_count_sz   : in std_logic_vector(7 downto 0);
	cmd_sz_data    : in std_logic_vector(47 downto 0);
	work_state     : in std_logic_vector(7 downto 0);
	
	da1_data   : in std_logic_vector(15 downto 0);
    da2_data   : in std_logic_vector(15 downto 0);
    da3_data   : in std_logic_vector(15 downto 0);
    da4_data   : in std_logic_vector(15 downto 0);	

	pack_end       : out std_logic
);	
end component;
signal	wr1        : std_logic;
signal	data_out1   : std_logic_vector(7 downto 0);
signal	addr_wr1   : std_logic_vector(10 downto 0);
signal	wr2        : std_logic;
signal	data_out2   : std_logic_vector(7 downto 0);
signal	addr_wr2   : std_logic_vector(10 downto 0);
signal h_e_addr1        : std_logic_vector(7 downto 0);
signal h_e_addr2        : std_logic_vector(7 downto 0);
signal h_e_addr3        : std_logic_vector(7 downto 0);
signal h_e_addr4        : std_logic_vector(7 downto 0);
signal h_e_addr5        : std_logic_vector(7 downto 0);
signal h_fgm_addr       : std_logic_vector(9 downto 0);
signal h_gc_addr        : std_logic_vector(7 downto 0);
signal l_e_addr1        : std_logic_vector(7 downto 0);
signal l_e_addr2        : std_logic_vector(7 downto 0);
signal l_e_addr3        : std_logic_vector(7 downto 0);
signal l_e_addr4        : std_logic_vector(7 downto 0);
signal l_e_addr5        : std_logic_vector(7 downto 0);
signal l_fgm_addr       : std_logic_vector(9 downto 0);
signal l_gc_addr        : std_logic_vector(7 downto 0);
signal	h_pack_end        : std_logic;
signal	l_pack_end        : std_logic;
signal  h_en              : std_logic;

begin
U1_PACK_PROCESS:H_PACK_PROCESS
port map (
    reset         =>   reset           ,
    clk           =>   clk             ,       
    
    ram_wr         =>   wr1              ,
    ram_din        =>   data_out1        ,
    ram_waddr      =>   addr_wr1         ,
				
    pack_edge     =>   pack_edge       ,
				
    e_sample_end1 =>   e_sample_end1   ,
    e_sample_end2 =>   e_sample_end2   ,
    e_sample_end3 =>   e_sample_end3   ,
    e_sample_end4 =>   e_sample_end4   ,
    e_sample_end5 =>   e_sample_end5   ,
    fgm_sample_end=>   fgm_sample_end   ,
    gc_sample_end =>   gc_sample_end   ,
				
    e_addr1       =>   h_e_addr1         ,
    e_addr2       =>   h_e_addr2         ,
    e_addr3       =>   h_e_addr3         ,
    e_addr4       =>   h_e_addr4         ,
    e_addr5       =>   h_e_addr5         ,
    fgm_addr      =>   h_fgm_addr        ,
    gc_addr       =>   h_gc_addr         ,
				  
    e_data_in1    =>   e_data_in1      ,
    e_data_in2    =>   e_data_in2      ,
    e_data_in3    =>   e_data_in3      ,
    e_data_in4    =>   e_data_in4      ,
    e_data_in5    =>   e_data_in5      ,
    fgm_data_in   =>   fgm_data_in     ,
    gc_data_in    =>   gc_data_in      ,
	
	cmd_count_error => cmd_count_error ,
    cmd_error_type  => cmd_error_type  ,
    cmd_count_lx    => cmd_count_lx    ,
	cmd_count_time  => cmd_count_time   ,
    cmd_count_sz    => cmd_count_sz     ,
	cmd_sz_data     => cmd_sz_data      ,
    work_state      => work_state       ,
	
	da1_data     =>  da1_data ,
	da2_data     =>  da2_data ,
	da3_data     =>  da3_data ,
	da4_data     =>  da4_data ,

    s_data        =>   s_data          ,
	ms_data       =>   ms_data         ,
    pack_end      =>   h_pack_end        
);

U2_PACK_PROCESS:L_PACK_PROCESS
port map (
    reset         =>   reset           ,
    clk           =>   clk             ,       
    
    wr            =>   wr2             ,
    data_out      =>   data_out2        ,
    addr          =>   addr_wr2         ,
				
    pack_edge     =>   pack_edge       ,
				
    e_sample_end1 =>   e_sample_end1   ,
    e_sample_end2 =>   e_sample_end2   ,
    e_sample_end3 =>   e_sample_end3   ,
    e_sample_end4 =>   e_sample_end4   ,
    e_sample_end5 =>   e_sample_end5   ,
    fgm_sample_end=>   fgm_sample_end   ,
    gc_sample_end =>   gc_sample_end   ,
				
    e_addr1       =>   l_e_addr1         ,
    e_addr2       =>   l_e_addr2         ,
    e_addr3       =>   l_e_addr3         ,
    e_addr4       =>   l_e_addr4         ,
    e_addr5       =>   l_e_addr5         ,
    fgm_addr      =>   l_fgm_addr        ,
    gc_addr       =>   l_gc_addr         ,
				  
    e_data_in1    =>   e_data_in1      ,
    e_data_in2    =>   e_data_in2      ,
    e_data_in3    =>   e_data_in3      ,
    e_data_in4    =>   e_data_in4      ,
    e_data_in5    =>   e_data_in5      ,
    fgm_data_in   =>   fgm_data_in     ,
    gc_data_in    =>   gc_data_in      ,
	
	cmd_count_error => cmd_count_error ,
    cmd_error_type  => cmd_error_type  ,
    cmd_count_lx    => cmd_count_lx    ,
	cmd_count_time  => cmd_count_time   ,
    cmd_count_sz    => cmd_count_sz     ,
	cmd_sz_data     => cmd_sz_data      ,
    work_state      => work_state       ,
	
	da1_data     =>  da1_data ,
	da2_data     =>  da2_data ,
	da3_data     =>  da3_data ,
	da4_data     =>  da4_data ,

    s_data        =>   s_data          ,
	ms_data       =>   ms_data         ,
    pack_end      =>   l_pack_end        
);

	process(reset,clk)
	begin
		if reset='0' then
		    h_en<='0'; 
		else 
		    if clk ='1' and clk'event then
                if pack_edge='1' and work_state(2 downto 0)=x"010" then 
                    h_en<='0';
				else
				    h_en<='1';
				end if;
			end if;
		end if;
	end process;


	process(reset,clk)
	begin
		if reset='0' then	
		    e_addr1<=(others=>'0');
			e_addr2<=(others=>'0');
			e_addr3<=(others=>'0');
			e_addr4<=(others=>'0');
			e_addr5<=(others=>'0');
			fgm_addr<=(others=>'0');
			gc_addr<=(others=>'0');
			pack_end<='0';
			wr<='0'; 
			addr<=(others=>'0'); 
			data_out<=(others=>'0'); 
		else 
		    if clk ='1' and clk'event then
                if h_en='1' then 
				    e_addr1 <= h_e_addr1  ;
			        e_addr2 <= h_e_addr2  ;
			        e_addr3 <= h_e_addr3  ;
			        e_addr4 <= h_e_addr4  ;
			        e_addr5 <= h_e_addr5  ;
			        fgm_addr<= h_fgm_addr ;
			        gc_addr <= h_gc_addr  ;
			        pack_end<= h_pack_end;
			        wr      <= wr1; 
			        addr    <= addr_wr1; 
			        data_out<= data_out1; 
				else
				    e_addr1 <= l_e_addr1  ;
			        e_addr2 <= l_e_addr2  ;
			        e_addr3 <= l_e_addr3  ;
			        e_addr4 <= l_e_addr4  ;
			        e_addr5 <= l_e_addr5  ;
			        fgm_addr<= l_fgm_addr ;
			        gc_addr <= l_gc_addr  ;
			        pack_end<= l_pack_end;
			        wr      <= wr2; 
			        addr    <= addr_wr2; 
			        data_out<= data_out2;
                end if;
            end if; 
        end if;
    end process;		
				    
		
		
		
		
		
		
		
end Behavioral;	