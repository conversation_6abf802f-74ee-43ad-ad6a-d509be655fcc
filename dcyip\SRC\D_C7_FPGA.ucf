

NET "DATA_IN[0]" LOC = L1;
NET "DATA_IN[1]" LOC = M1;
NET "DATA_IN[2]" LOC = N5;
NET "DATA_IN[3]" LOC = P5;
NET "DATA_IN[4]" LOC = P7;
NET "DATA_IN[5]" LOC = P6;
NET "DATA_IN[6]" LOC = T10;
NET "DATA_IN[7]" LOC = R9;
NET "EFI_SW_ADDR[2]" LOC = J4;
NET "EFI_SW_ADDR[1]" LOC = K4;
NET "EFI_SW_ADDR[0]" LOC = H3;
NET "LC_RST1" LOC = J6;
NET "LC_EN1" LOC = J5;
#NET "LC_RST1" LOC = J5;
#NET "LC_EN1" LOC = J6;
NET "BY" LOC = H5;
NET "RC" LOC = H4;
NET "CS1" LOC = P12;
NET "CS2" LOC = P11;
NET "CS3" LOC = K6;
NET "CS4" LOC = L6;
#NET "CS5" LOC = F1;
#NET "CS6" LOC = G1;
#差分探头
NET "CS5" LOC = G1;
#工参采样
NET "CS6" LOC = F1;
NET "CLK_10K" LOC = P10;
NET "CLK_20K" LOC = H2;
NET "clk" LOC = AD16;
NET "RXD" LOC = AK3;
NET "TXD" LOC = AD32;
NET "DA_CS1" LOC = D4;
NET "DA_CS2" LOC = E4;
NET "DA_CS3" LOC = M10;
NET "DA_CS4" LOC = N13;
NET "DA_DATA" LOC = F5;
NET "DA_CLK" LOC = G5;
NET "FGM_4" LOC = F3;
NET "FGM_2" LOC = C2;
NET "FGM_1" LOC = N12;
NET "FGM_3" LOC = F4;
NET "SC_TXD" LOC = AE32;
NET "TX_ON" LOC = AG32;
NET "EN1" LOC = M32;
NET "EN2" LOC = M33;


NET "DATA_IN[7]" IOSTANDARD = LVTTL;
NET "DATA_IN[6]" IOSTANDARD = LVTTL;
NET "DATA_IN[5]" IOSTANDARD = LVTTL;
NET "DATA_IN[4]" IOSTANDARD = LVTTL;
NET "DATA_IN[3]" IOSTANDARD = LVTTL;
NET "DATA_IN[2]" IOSTANDARD = LVTTL;
NET "DATA_IN[1]" IOSTANDARD = LVTTL;
NET "DATA_IN[0]" IOSTANDARD = LVTTL;
NET "EFI_SW_ADDR[2]" IOSTANDARD = LVTTL;
NET "EFI_SW_ADDR[1]" IOSTANDARD = LVTTL;
NET "EFI_SW_ADDR[0]" IOSTANDARD = LVTTL;
NET "LC_RST1" IOSTANDARD = LVTTL;
NET "LC_EN1" IOSTANDARD = LVTTL;
NET "BY" IOSTANDARD = LVTTL;
NET "clk" IOSTANDARD = LVTTL;
NET "CLK_10K" IOSTANDARD = LVTTL;
NET "CLK_20K" IOSTANDARD = LVTTL;
NET "CS1" IOSTANDARD = LVTTL;
NET "CS2" IOSTANDARD = LVTTL;
NET "CS3" IOSTANDARD = LVTTL;
NET "CS4" IOSTANDARD = LVTTL;
NET "CS5" IOSTANDARD = LVTTL;
NET "CS6" IOSTANDARD = LVTTL;
NET "RC" IOSTANDARD = LVTTL;
NET "RXD" IOSTANDARD = LVTTL;
NET "TXD" IOSTANDARD = LVCMOS33;
NET "DA_CS1" IOSTANDARD = LVTTL;
NET "DA_CS2" IOSTANDARD = LVTTL;
NET "DA_CS3" IOSTANDARD = LVTTL;
NET "DA_CS4" IOSTANDARD = LVTTL;
NET "DA_DATA" IOSTANDARD = LVTTL;
NET "DA_CLK" IOSTANDARD = LVTTL;
NET "FGM_1" IOSTANDARD = LVTTL;
NET "FGM_2" IOSTANDARD = LVTTL;
NET "FGM_3" IOSTANDARD = LVTTL;
NET "FGM_4" IOSTANDARD = LVTTL;
NET "SC_TXD" IOSTANDARD = LVTTL;
NET "EN1" IOSTANDARD = LVTTL;
NET "EN2" IOSTANDARD = LVTTL;
NET "TX_ON" IOSTANDARD = LVTTL;

# PlanAhead Generated miscellaneous constraints 

