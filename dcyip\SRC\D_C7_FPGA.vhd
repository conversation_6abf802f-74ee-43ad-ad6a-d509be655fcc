----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    16:10:28 10/21/2022 
-- Design Name: 
-- Module Name:    D_C7_FPGA - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;


-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity D_C7_FPGA is
    Port ( 
	clk: in  STD_LOGIC;
	PW_EN:in std_logic;
	
--	reset : in std_logic;
	
	CLK_20K : out   std_logic;
    CLK_10K : out   std_logic;
--AD976A	
    CS1		:	out std_logic; 
    CS2		:	out std_logic;
    CS3		:	out std_logic;
    CS4		:	out std_logic;
    CS5		:	out std_logic; 
	CS6		:	out std_logic; 	
	RC		:	out std_logic;                    
	BY		:	out std_logic;  
    DATA_IN :   in std_logic_vector(7 downto 0);	
--ADG508	
--	FGM_SW_ADDR :out std_logic_vector(1 downto 0);
	EFI_SW_ADDR :out std_logic_vector(2 downto 0);

--    SC_TXD        : out std_logic;
    RXD           :  in std_logic;  
    TXD           :  out std_logic;
--    TX_ON         : out std_logic;
--    TEST_TXD      : out std_logic;
--    TEST_RXD      : out std_logic;
	
	DA_CS1		: out std_logic;   
	DA_CS2		: out std_logic; 
	DA_CS3		: out std_logic; 
	DA_CS4		: out std_logic;	
	DA_DATA		: out std_logic;                    
	DA_CLK 		: out std_logic;

--sc_ram	
	addr_rd	   : in std_logic_vector(10 downto 0);
	ram_dout   : out std_logic_vector(7 downto 0);
	pack_end   :out std_logic;
	
	LC_RST1    : out std_logic;
	LC_EN1     : out std_logic;
	
	FGM_1      :out std_logic;
	FGM_2      :out std_logic;
	FGM_3      :out std_logic;
	FGM_4      :out std_logic

    -- EN1:out std_logic;
    -- EN2:out std_logic
	
);
end D_C7_FPGA;



architecture Behavioral of D_C7_FPGA is

--signal reset: STD_LOGIC;
signal clk_us_edge:std_logic;

signal sample_edge  :std_logic;
		   
signal packedge   :std_logic;
signal time_edge1 :std_logic;
signal time_edge2 :std_logic;
signal time_edge3	:std_logic;	
--signal send_edge:std_logic;

signal s_data       :std_logic_vector(31 downto 0);
signal ms_data      :std_logic_vector(15 downto 0);
signal time_set     :std_logic;
signal s_data_in    :std_logic_vector(31 downto 0);
signal ms_data_in   :std_logic_vector(15 downto 0);
signal clk_s_edge   :std_logic;
signal clk_ms_edge  :std_logic;

signal cmd_count_lx   :std_logic_vector(7 downto 0);
signal cmd_count_sz   :std_logic_vector(7 downto 0);
signal cmd_count_time :std_logic_vector(7 downto 0);
signal cmd_count_error:std_logic_vector(7 downto 0);
signal cmd_error_type :std_logic_vector(7 downto 0);
signal cmd_sz_data    :std_logic_vector(47 downto 0);
signal cmd_sz_errtype:std_logic_vector(7 downto 0);
signal cmd_sz_errcnt:std_logic_vector(7 downto 0);

--signal 	pram_data_in: std_logic_vector(7 downto 0);
--signal	pram_addr   : std_logic_vector(9 downto 0);

signal data_out_lx1  :std_logic_vector(7 downto 0);
signal data_out_lx2  :std_logic_vector(7 downto 0);
signal data_out_lx3  :std_logic_vector(7 downto 0);
signal data_out_lx4  :std_logic_vector(7 downto 0);
signal data_out_lx5  :std_logic_vector(7 downto 0);
signal tmp1          :std_logic_vector(7 downto 0);
signal tmp2          :std_logic_vector(7 downto 0);
signal tmp3          :std_logic_vector(7 downto 0);
signal tmp4          :std_logic_vector(7 downto 0);
signal dy            :std_logic_vector(7 downto 0);
signal cmd_addr_rd   :std_logic_vector (9 downto 0);
signal cmd_fgm       :std_logic_vector(7 downto 0);

--signal cmd_tx        :std_logic;
signal clk1MHz       :std_logic;--DA时钟

signal da_change   : std_logic;
signal da_change_data     : std_logic_vector(47 downto 0);
signal da1_data   : std_logic_vector(15 downto 0);
signal da2_data   : std_logic_vector(15 downto 0);
signal da3_data   : std_logic_vector(15 downto 0);
signal da4_data   : std_logic_vector(15 downto 0);

signal mode_change :std_logic;
signal cmd_mode    :std_logic_vector(7 downto 0);
signal work_state  :std_logic_vector(7 downto 0);
--signal h_mode      :std_logic;
--signal l_mode      :std_logic;
--signal scan_mode   :std_logic;
--signal lc_run      :std_logic;
--signal lc_ground   :std_logic;

signal rd_over:std_logic;
signal fgm_rd_over:std_logic;
signal gc_rd_over :std_logic;	

signal data_in1 :std_logic_vector(15 downto 0);
signal data_in2 :std_logic_vector(15 downto 0);
signal data_in3 :std_logic_vector(15 downto 0);
signal data_in4 :std_logic_vector(15 downto 0);
signal data_in5 :std_logic_vector(15 downto 0);
signal data_in6 :std_logic_vector(15 downto 0);
signal fgm_data :std_logic_vector(15 downto 0);
signal gc_data  :std_logic_vector(15 downto 0);

signal e_sample_end1  : std_logic;
signal e_sample_end2  : std_logic;
signal e_sample_end3  : std_logic;
signal e_sample_end4  : std_logic;
signal e_sample_end5  : std_logic;
signal fgm_sample_end : std_logic;
--signal gc_sample_end  : std_logic;
signal e_addr1        : std_logic_vector(7 downto 0);
signal e_addr2        : std_logic_vector(7 downto 0);
signal e_addr3        : std_logic_vector(7 downto 0);
signal e_addr4        : std_logic_vector(7 downto 0);
signal e_addr5        : std_logic_vector(7 downto 0);
signal fgm_addr       : std_logic_vector(9 downto 0);
signal gc_addr        : std_logic_vector(7 downto 0);
signal e_data_in1     : std_logic_vector(7 downto 0);
signal e_data_in2     : std_logic_vector(7 downto 0);
signal e_data_in3     : std_logic_vector(7 downto 0);
signal e_data_in4     : std_logic_vector(7 downto 0);
signal e_data_in5     : std_logic_vector(7 downto 0);	
signal fgm_data_in    : std_logic_vector(7 downto 0);
signal gc_data_in     : std_logic_vector(7 downto 0);

--signal PW_EN:std_logic;
signal reset_n:std_logic;

signal	wr        : std_logic;
signal	ram_din   : std_logic_vector(7 downto 0);
signal	addr_wr   : std_logic_vector(10 downto 0);
--signal  send_over  :std_logic;
--signal  send_start :std_logic;
--signal  sc_data    : std_logic_vector(7 downto 0);    

signal wr_ram:std_logic_vector(0 downto 0);


signal off_cmd:std_logic;
--signal reset_1:std_logic;
--signal start_off:std_logic;
signal cnt:integer range 0 to 5;
--signal lc_change:std_logic;
--signal clk_test:std_logic;
--signal pw_en_reg:std_logic;
--signal pw_en_edge:Std_logic;
--signal pw_en_reg1:std_logic;

signal	fgm_chanel: std_logic_vector(7 downto 0);
signal	fgm_chanel_change: std_logic;
signal  fgm_chanel_data  :  std_logic_vector(47 downto 0);
signal  fgm_change: std_logic;
signal fgm_state:    std_logic_vector(3 downto 0);
signal fgm_state_change:	 std_logic;
signal fgm_sample_state:std_logic;
signal fgm_addr_out:std_logic_vector(7 downto 0);
signal pack_state: std_logic_vector(7 downto 0);
signal pack_end_f:std_logic;

signal init_da: std_logic;
signal init_lc: std_logic;
signal init_fgm: std_logic;
signal mp_enable:std_logic;
signal driv_enable:std_logic;

signal clk_10k_reg:std_logic;
signal clk_20k_reg:std_logic;

component RST_CTRL is
    Port (  
			clk       : in  STD_LOGIC;
		    PW_EN     : in  STD_LOGIC;
			off_cmd   : in std_logic;
			clk_s_edge: in std_logic;
			
			reset_n   : out STD_LOGIC
		   );

end component;

component Init_sch is
    Port ( 	clk		: in  STD_LOGIC;
			reset 	: in 	std_logic;
			clk_1ms_edge	:	in	std_logic;
	
            init_lc: out std_logic;
			init_da: out std_logic;
			init_fgm: out std_logic;
            mp_enable: out std_logic ;
            driv_enable:out std_logic	 
          );
end component;

component MAIN_CTRL is
Port ( 
	clk : in  STD_LOGIC;
	reset : in std_logic;
	mp_enable: in std_logic;
	
	sample_edge: out std_logic; 		   
	pack_edge : out  STD_LOGIC;--200ms
	time_edge1: out std_logic;  --2.5ms
	time_edge2: out std_logic;  --12.5ms
	time_edge3: out std_logic;  --25ms
    
	mode_change : in std_logic;
    cmd_mode    : in std_logic_vector(7 downto 0);	        
	fgm_state:   in std_logic_vector(3 downto 0);
    fgm_state_change:	in  std_logic;
	
	work_mode  : out std_logic_vector(7 downto 0);
	work_mode_pack  : out std_logic_vector(7 downto 0)
);
end component;

component TIME_Manger is
    Port ( clk : in  STD_LOGIC;
           reset : in  STD_LOGIC;	
--		    work_mode:in std_logic;
		   
		   clk_20k : out   std_logic;
           clk_10k : out   std_logic;
           clk_us_edge:out std_logic;
           clk1MHz:  out  STD_LOGIC;
           clk_ms_edge:out std_logic;
            clk_s_edge:  out std_logic;			   

		   -- sample_edge  :out std_logic;
           -- packedge   :out std_logic;
--		   send_edge    :out std_logic;
		   -- time_edge1   :out std_logic;
		   -- time_edge2   :out std_logic;
		   -- time_edge3   :out std_logic;		   
		   
		   s_data  : out std_logic_vector(31 downto 0);
           ms_data : out std_logic_vector(15 downto 0);
		   
		   --校时
           time_set: in std_logic;
		   s_data_in  : in std_logic_vector(31 downto 0);
           ms_data_in : in std_logic_vector(15 downto 0)
);
end component;

component Read_AD976 
port (
    clk : in std_logic;
    reset : in std_logic;
    
	read_start: in std_logic;
    
    CS1		:	out std_logic; 
    CS2		:	out std_logic;
    CS3		:	out std_logic;
    CS4		:	out std_logic;
    CS5		:	out std_logic; 
	CS6		:	out std_logic; 
	
	RC		:	out std_logic;                    
	BY		:	out std_logic;      

	data_in :  in std_logic_vector(7 downto 0);
	read_over: out std_logic;
--单探头	
	data_out1:  out std_logic_vector(15 downto 0);
	data_out2:  out std_logic_vector(15 downto 0);
	data_out3:  out std_logic_vector(15 downto 0);
	data_out4:  out std_logic_vector(15 downto 0);
--差分探头
	data_out5:  out std_logic_vector(15 downto 0);
--磁场数据及工参
	data_out6:  out std_logic_vector(15 downto 0)
);
end component;

component ADG_SW1 
port (
    reset   : in std_logic;
    clk     : in std_logic;
	l_en    : in std_logic;
	
    start_edge	: in std_logic;
	time_edge	: in std_logic;    
	data_in	: in std_logic_vector(15 downto 0);  
	rd_over : in std_logic;     

    fgm_rd_en: out std_logic;
	gc_rd_en:  out std_logic;	
	fgm_data:out std_logic_vector(15 downto 0);
	gc_data:out std_logic_vector(15 downto 0);
	fgm_sample_state: out std_logic;	
--	fgm_chanel  : in std_logic_vector(7 downto 0);
	
--	fgm_sw_addr:out std_logic_vector(1 downto 0);
    efi_sw_addr:out std_logic_vector(2 downto 0)
);
end component;

component DATA_PROCESS is
port (
    clk : in std_logic;
    reset : in std_logic;
	l_en  : in std_logic;
	l_en_1: in std_logic;
   
	read_start: in std_logic;
	fgm_chanel  : in std_logic_vector(7 downto 0);
	
	start_edge:in std_logic;
	time_edge1 :in std_logic;
	time_edge2 :in std_logic;
	time_edge3 :in std_logic;
	
	read_over: in std_logic;
	fgm_rd_over: in std_logic;
	gc_rd_over:  in std_logic;	

	data_in1:  in std_logic_vector(15 downto 0);
	data_in2:  in std_logic_vector(15 downto 0);
	data_in3:  in std_logic_vector(15 downto 0);
	data_in4:  in std_logic_vector(15 downto 0);
	data_in5:  in std_logic_vector(15 downto 0);
	fgm_data:  in std_logic_vector(15 downto 0);
	gc_data:  in std_logic_vector(15 downto 0);
	
	sample_end1 : out std_logic;  	
	ram_dout1   : out std_logic_vector (7 downto 0);
	addr_rd1    : in  std_logic_vector(7 downto 0);
	
--	sample_end2 : out std_logic;
	ram_dout2   : out std_logic_vector (7 downto 0);
	addr_rd2    : in  std_logic_vector(7 downto 0);
	
--	sample_end3 : out std_logic;
	ram_dout3   : out std_logic_vector (7 downto 0);
	addr_rd3    : in  std_logic_vector(7 downto 0);
	
--	sample_end4 : out std_logic;
	ram_dout4   : out std_logic_vector (7 downto 0);
	addr_rd4    : in  std_logic_vector(7 downto 0);
	
--	sample_end5 : out std_logic;
	ram_dout5   : out std_logic_vector (7 downto 0);
	addr_rd5    : in  std_logic_vector(7 downto 0);
	
--	sample_end6 : out std_logic;
	ram_dout6   : out std_logic_vector (7 downto 0);
	addr_rd6    : in  std_logic_vector(9 downto 0);
	
--	sample_end7 : out std_logic;
	ram_dout7   : out std_logic_vector (7 downto 0);
	addr_rd7    : in  std_logic_vector(7 downto 0);

--CMD
    data_out_lx1  :out std_logic_vector(7 downto 0);
	data_out_lx2  :out std_logic_vector(7 downto 0);
	data_out_lx3  :out std_logic_vector(7 downto 0);
	data_out_lx4  :out std_logic_vector(7 downto 0);
	data_out_lx5  :out std_logic_vector(7 downto 0);
    tmp1          :out std_logic_vector(7 downto 0);
	tmp2          :out std_logic_vector(7 downto 0);
	tmp3          :out std_logic_vector(7 downto 0);
	tmp4          :out std_logic_vector(7 downto 0);
	dy            :out std_logic_vector(7 downto 0);
	cmd_addr_rd   :in  std_logic_vector (9 downto 0);
	cmd_fgm       :out std_logic_vector(7 downto 0)
		
);
end component;

component H_PACK_PROCESS 
port(
    reset          : in std_logic;
    clk            : in std_logic;
	
	ram_wr             : out std_logic;
	ram_din       : out std_logic_vector(7 downto 0);
	ram_waddr           : out std_logic_vector(10 downto 0);
	
	pack_edge      : in std_logic;

	-- e_sample_end1  : in std_logic;
	-- e_sample_end2  : in std_logic;
	-- e_sample_end3  : in std_logic;
	-- e_sample_end4  : in std_logic;
	-- e_sample_end5  : in std_logic;
	-- fgm_sample_end : in std_logic;
	-- gc_sample_end  : in std_logic;

	e_addr1        : out std_logic_vector(7 downto 0);
	e_addr2        : out std_logic_vector(7 downto 0);
	e_addr3        : out std_logic_vector(7 downto 0);
	e_addr4        : out std_logic_vector(7 downto 0);
	e_addr5        : out std_logic_vector(7 downto 0);
	fgm_addr       : out std_logic_vector(9 downto 0);
	gc_addr        : out std_logic_vector(7 downto 0);
	
	e_data_in1     : in std_logic_vector(7 downto 0);
	e_data_in2     : in std_logic_vector(7 downto 0);
	e_data_in3     : in std_logic_vector(7 downto 0);
	e_data_in4     : in std_logic_vector(7 downto 0);
    e_data_in5     : in std_logic_vector(7 downto 0);	
    fgm_data_in    : in std_logic_vector(7 downto 0);
    gc_data_in     : in std_logic_vector(7 downto 0);	

--时间码
    s_data        : in std_logic_vector(31 downto 0);
	ms_data       : in std_logic_vector(15 downto 0);		
--指令类型及计数等	
    cmd_count_error: in std_logic_vector(7 downto 0);
    cmd_error_type : in std_logic_vector(7 downto 0);
	cmd_sz_errcnt  :in std_logic_vector(7 downto 0);
    cmd_sz_errtype :in std_logic_vector(7 downto 0);
    cmd_count_lx   : in std_logic_vector(7 downto 0);
    cmd_count_time : in std_logic_vector(7 downto 0);
    cmd_count_sz   : in std_logic_vector(7 downto 0);
	cmd_sz_data    : in std_logic_vector(47 downto 0);
	work_state     : in std_logic_vector(7 downto 0);
	
	da1_data   : in std_logic_vector(15 downto 0);
    da2_data   : in std_logic_vector(15 downto 0);
    da3_data   : in std_logic_vector(15 downto 0);
    da4_data   : in std_logic_vector(15 downto 0);	

	pack_end       : out std_logic
);	
end component;

component CMD_PROCESS is
port(
    reset      :  in std_logic;
    clk        :  in std_logic; 
	clk_us_edge:  in std_logic;
	
    RXD        :  in std_logic;    
    -- rxd_flag   : in std_logic;
	-- rxd_data    : in std_logic_vector(7 downto 0);  
	
	TXD        :  out std_logic	;
--	TX_ON      :  out std_logic;
--	RX_ON      :  out std_logic;
	
--科学数据包
    s_data_in    :in std_logic_vector(31 downto 0);
    ms_data_in   :in std_logic_vector(15 downto 0);	
	data_out_lx1  :in std_logic_vector(7 downto 0);
	data_out_lx2  :in std_logic_vector(7 downto 0);
	data_out_lx3  :in std_logic_vector(7 downto 0);
	data_out_lx4  :in std_logic_vector(7 downto 0);
	data_out_lx5  :in std_logic_vector(7 downto 0);
	tmp1          :in std_logic_vector(7 downto 0);
	tmp2          :in std_logic_vector(7 downto 0);
	tmp3          :in std_logic_vector(7 downto 0);
	tmp4          :in std_logic_vector(7 downto 0);
	dy            :in std_logic_vector(7 downto 0);
	cmd_addr_rd   :out  std_logic_vector (9 downto 0);
	cmd_fgm       :in std_logic_vector(7 downto 0);
	
	da1_data   :in std_logic_vector(15 downto 0);
    da2_data   :in std_logic_vector(15 downto 0);
    da3_data   :in std_logic_vector(15 downto 0);
    da4_data   :in std_logic_vector(15 downto 0);
--    work_state     : in std_logic_vector(7 downto 0);
		
--工况	
	power_state : in std_logic_vector(7 downto 0);
	off_cmd     : out std_logic;
--	off_state      : out std_logic;
--校时	
	s_data      : out std_logic_vector(31 downto 0);
    ms_data     : out std_logic_vector(15 downto 0);
	time_set    : out std_logic;
	
   mode_change : out std_logic;
   cmd_mode    : out std_logic_vector(7 downto 0);
   da_change   : out std_logic;
   da_data     : out std_logic_vector(47 downto 0);
   fgm_chanel_data  : out std_logic_vector(47 downto 0);
   fgm_change  : out std_logic;
--   lc_change   : out std_logic;

--指令计数
--  cmd_count_right:out std_logic_vector(7 downto 0);
  cmd_count_error:out std_logic_vector(7 downto 0);
  cmd_sz_errcnt  :out std_logic_vector(7 downto 0);
  cmd_sz_errtype :out std_logic_vector(7 downto 0);
  
  cmd_error_type :out std_logic_vector(7 downto 0);
  cmd_count_lx   :out std_logic_vector(7 downto 0);
  cmd_count_time :out std_logic_vector(7 downto 0);
  cmd_count_sz   :out std_logic_vector(7 downto 0);
--  cmd_last       :out std_logic_vector(7 downto 0);
--  cmd_sz_code    :out std_logic_vector(7 downto 0);
  cmd_sz_data    :out std_logic_vector(47 downto 0)
		
);
end component;

component DA_PROCESS is
Port ( 	
	clk			: in std_logic;
	reset 		: in std_logic;
	da_clk      : in std_logic;
	da_data_in  : in std_logic_vector(47 downto 0);
	da_change   : in std_logic;
	s_edge      : in std_logic;
	mode        : in std_logic;
	init_da     : in std_logic;
	
	da1_data   : out std_logic_vector(15 downto 0);
    da2_data   : out std_logic_vector(15 downto 0);
    da3_data   : out std_logic_vector(15 downto 0);
    da4_data   : out std_logic_vector(15 downto 0);	
	
	SC_CS1		: out std_logic; 
    SC_CS2		: out std_logic; 
    SC_CS3		: out std_logic; 
    SC_CS4		: out std_logic; 			
	SC_DATA		: out std_logic;                  
	SC_CLK 		: out std_logic       
	);
end component;

component LC_PROCESS is
Port (
	clk			: in std_logic;
	reset 		: in std_logic;	
	init_lc     : in std_logic;
	off_cmd     : in std_logic;

    lc_state   : in std_logic;
	ms_edge     : in std_logic;
	
	LC_RST1    : out std_logic;
	LC_EN1     : out std_logic

);	
end component;

component FGM_PROCESS is
Port (
	clk			: in std_logic;
	reset 		: in std_logic;
	init_fgm    : in std_logic;

    fgm_data_in	: in std_logic_vector(47 downto 0);
	fgm_change  : in std_logic;
	s_edge      : in std_logic;
	ms_edge     : in std_logic;	
	fgm_sample_state  : in std_logic;
	
	fgm_chanel_out: out std_logic_vector(7 downto 0);
	fgm_change_out:out std_logic;
	
    fgm_state:   out std_logic_vector(3 downto 0);
    fgm_state_change:	out std_logic;
	
	FGM_1      :out std_logic;
	FGM_2      :out std_logic;
	FGM_3      :out std_logic;
	FGM_4      :out std_logic;
	fgm_addr_out   :out std_logic_vector(7 downto 0)
);

end component;

component RAM_A 
	port (
	addra: IN std_logic_VECTOR(10 downto 0);
	addrb: IN std_logic_VECTOR(10 downto 0);
	clka: IN std_logic;
	clkb: IN std_logic;
	dina: IN std_logic_VECTOR(7 downto 0);
	doutb: OUT std_logic_VECTOR(7 downto 0);
	wea: IN std_logic_VECTOR(0 downto 0)
	);
end component;




begin

--生成软件全局复位：低有效；
--在未收到软件启动信号，及收到预关机5s后复位
--reset_n<=((PW_EN) and (reset_1));
--reset_n<=PW_EN;
pack_end<=pack_end_f;

process(clk,reset_n)
begin
    if reset_n='0' then
        CLK_10K<='0';
		CLK_20K<='0';
    else
        if clk'event and clk='1' then
           if driv_enable ='1' then 
		        CLK_10K<=clk_10k_reg;
		        CLK_20K<=clk_20k_reg;
			else
			    CLK_10K<='0';
		        CLK_20K<='0';
			end if;
        end if;
    end if;
end process;


	U_RST_CTRL:RST_CTRL
port map(
    clk       =>  clk      ,
	PW_EN     => PW_EN     ,
	off_cmd   => off_cmd   ,
	clk_s_edge=> clk_s_edge,
	
	reset_n   =>  reset_n
);	

	U_Init_sch:Init_sch
port map(
    clk     =>  clk  ,
    reset   =>  reset_n,
    clk_1ms_edge => clk_ms_edge,	
	
	init_lc  => init_lc  ,
    init_da => init_da ,
    init_fgm => init_fgm ,
    mp_enable => mp_enable,
    driv_enable=>driv_enable
);




	
	U_MAIN_CTRL:MAIN_CTRL
port map(
    clk     =>  clk  ,
    reset   =>  reset_n,
    mp_enable => mp_enable,	
	
	sample_edge  => sample_edge  ,
    time_edge1 => time_edge1 ,
    time_edge2 => time_edge2 ,
    time_edge3 => time_edge3 ,
    pack_edge  =>  packedge,
	
	
	mode_change => mode_change ,
	cmd_mode    => cmd_mode    ,	
	fgm_state           =>   fgm_state           ,
    fgm_state_change    =>   fgm_state_change    ,
	
	
	work_mode  => work_state,
    work_mode_pack	=> pack_state

);	
	
U_TIME_Manger: TIME_Manger
port map(
    clk     =>  clk  ,
    reset   =>  reset_n, 
--	work_mode=> work_state(1),
	
	clk_20k => clk_20k_reg,
    clk_10k => clk_10k_reg,
	clk_us_edge=>clk_us_edge,
	clk1MHz    =>clk1MHz,
	clk_ms_edge=>clk_ms_edge,
	clk_s_edge=>clk_s_edge,
    
	-- sample_edge  => sample_edge  ,
    -- time_edge1 => time_edge1 ,
    -- time_edge2 => time_edge2 ,
    -- time_edge3 => time_edge3 ,

    -- packedge  =>packedge,
--	send_edge => send_edge,

    s_data      =>  s_data       ,
	ms_data     =>  ms_data      ,
    time_set    =>  time_set     ,
    s_data_in   =>  s_data_in    ,
    ms_data_in  =>  ms_data_in   
);
	
U_READ_AD976:Read_AD976 
port map(
    clk     =>  clk  ,
    reset   =>  reset_n,    
    read_start =>sample_edge,
    
    CS1	    => CS1	,
    CS2	    => CS2	,
    CS3	    => CS3	,
    CS4	    => CS4	,
    CS5	    => CS5	,
	CS6	    => CS6	,
	RC	    => RC	,                  
	BY	    => BY	,    
	data_in => DATA_IN,
	
	read_over=> rd_over,
--单探头	
	data_out1 => data_in1  ,
	data_out2 => data_in2  ,
	data_out3 => data_in3  ,
	data_out4 => data_in4  ,
--差分探头           
	data_out5 => data_in5  ,
--磁场数据和工参
	data_out6 =>data_in6
);

U_ADG_SW1:ADG_SW1
port map(
    clk         =>  clk         ,
    reset       =>  reset_n       ,  
    l_en	    => work_state(1),
    start_edge  =>  packedge  ,
    time_edge   =>  time_edge3   ,
    data_in     =>  data_in6    ,
--    data_in     =>  x"0000"    ,
    rd_over     =>  rd_over     ,  

    fgm_rd_en   =>  fgm_rd_over ,
	gc_rd_en    =>  gc_rd_over  ,
	fgm_data    =>  fgm_data   ,
	gc_data     =>  gc_data   ,
--xinzeng	
--    fgm_chanel   =>  fgm_chanel     ,
	
--	fgm_sw_addr =>  FGM_SW_ADDR ,
    fgm_sample_state=> fgm_sample_state,
    efi_sw_addr =>  EFI_SW_ADDR
);


   
	
U_DATA_PROCESS:DATA_PROCESS
port map(
    clk     =>  clk  ,
    reset   =>  reset_n, 
--    h_en	    =>  '1',
	 l_en	    => pack_state(1),
	 l_en_1	    => work_state(1),
	read_start  => sample_edge  ,
    fgm_chanel  =>	fgm_addr_out,
	
	start_edge  => packedge   ,
	time_edge1  => time_edge1   , 
	time_edge2  => time_edge2   ,
	time_edge3  => time_edge3   ,
	
	read_over    =>  rd_over    ,
	fgm_rd_over  =>  fgm_rd_over  ,
	gc_rd_over   =>  gc_rd_over   ,
	
	data_in1     =>  data_in1     ,
	data_in2     =>  data_in2     ,
	data_in3     =>  data_in3     ,
	data_in4     =>  data_in4     ,
	data_in5     =>  data_in5     ,
	fgm_data     =>  fgm_data     ,
	gc_data     =>  gc_data     ,
	
	-- fgm_data     =>  x"1111"     ,
	-- gc_data     =>  x"2222"     ,
		
	sample_end1 => e_sample_end1 ,
	ram_dout1   => e_data_in1   ,
	addr_rd1    => e_addr1    ,

--	sample_end2 => e_sample_end2 ,
	ram_dout2   => e_data_in2   ,
	addr_rd2    => e_addr2    ,
		
--	sample_end3 => e_sample_end3 ,
	ram_dout3   => e_data_in3   ,
	addr_rd3    => e_addr3    ,
		
--	sample_end4 => e_sample_end4 ,
	ram_dout4   => e_data_in4   ,
	addr_rd4    => e_addr4    ,
			
--	sample_end5 => e_sample_end5 ,
	ram_dout5   => e_data_in5   ,
	addr_rd5    => e_addr5    ,
			
--	sample_end6 => fgm_sample_end ,
	ram_dout6   => fgm_data_in   ,
	addr_rd6    => fgm_addr    ,
				
--	sample_end7 => gc_sample_end ,
	ram_dout7   => gc_data_in   ,
	addr_rd7    => gc_addr      ,

   data_out_lx1 => data_out_lx1 ,
   data_out_lx2 => data_out_lx2 ,
   data_out_lx3 => data_out_lx3 ,
   data_out_lx4 => data_out_lx4 ,
   data_out_lx5 => data_out_lx5 ,
   tmp1         => tmp1         ,
   tmp2         => tmp2         ,
   tmp3         => tmp3         ,
   tmp4         => tmp4         ,
   dy           => dy           ,
   cmd_addr_rd  => cmd_addr_rd  ,
   cmd_fgm      => cmd_fgm      

);

U_H_PACK_PROCESS:H_PACK_PROCESS
port map (
    reset         =>   reset_n           ,
    clk           =>   clk             ,       
    
    ram_wr       =>   wr              ,
    ram_din      =>   ram_din    ,
    ram_waddr     =>   addr_wr         ,
				
    pack_edge     =>   packedge       ,
				
    -- e_sample_end1 =>   e_sample_end1   ,
    -- e_sample_end2 =>   e_sample_end2   ,
    -- e_sample_end3 =>   e_sample_end3   ,
    -- e_sample_end4 =>   e_sample_end4   ,
    -- e_sample_end5 =>   e_sample_end5   ,
    -- fgm_sample_end=>   fgm_sample_end   ,
    -- gc_sample_end =>   gc_sample_end   ,
				
    e_addr1       =>   e_addr1         ,
    e_addr2       =>   e_addr2         ,
    e_addr3       =>   e_addr3         ,
    e_addr4       =>   e_addr4         ,
    e_addr5       =>   e_addr5         ,
    fgm_addr      =>   fgm_addr        ,
    gc_addr       =>   gc_addr         ,
				  
    e_data_in1    =>   e_data_in1      ,
    e_data_in2    =>   e_data_in2      ,
    e_data_in3    =>   e_data_in3      ,
    e_data_in4    =>   e_data_in4      ,
    e_data_in5    =>   e_data_in5      ,
    fgm_data_in   =>   fgm_data_in     ,
    gc_data_in    =>   gc_data_in      ,
	
	cmd_count_error => cmd_count_error ,
    cmd_error_type  => cmd_error_type  ,
	cmd_sz_errcnt   => cmd_sz_errcnt  ,
    cmd_sz_errtype  => cmd_sz_errtype ,
    cmd_count_lx    => cmd_count_lx    ,
	cmd_count_time  => cmd_count_time   ,
    cmd_count_sz    => cmd_count_sz     ,
	cmd_sz_data     => cmd_sz_data      ,
    work_state      => pack_state       ,
	
	da1_data     =>  da1_data ,
	da2_data     =>  da2_data ,
	da3_data     =>  da3_data ,
	da4_data     =>  da4_data ,

    s_data        =>   s_data          ,
	ms_data       =>   ms_data         ,
    pack_end      =>   pack_end_f        
);

wr_ram(0)<=wr ;
U_PACK_RAM:RAM_A
	port map (
		addra => addr_wr,
		addrb => addr_rd,
		clka  => clk    ,
		clkb  => clk    ,
		dina  => ram_din,
		doutb => ram_dout,
		wea   => wr_ram
	);	



 U_CMD_PROCESS: CMD_PROCESS 
PORT MAP (
    reset => reset_n,
    clk   => clk,
    clk_us_edge => clk_us_edge,
	
    RXD          => RXD,
    TXD          => TXD,
--    TXD          => cmd_tx,
	s_data_in    => s_data    ,
    ms_data_in   => ms_data   ,
   data_out_lx1 => data_out_lx1 ,
   data_out_lx2 => data_out_lx2 ,
   data_out_lx3 => data_out_lx3 ,
   data_out_lx4 => data_out_lx4 ,
   data_out_lx5 => data_out_lx5 ,
   tmp1         => tmp1         ,
   tmp2         => tmp2         ,
   tmp3         => tmp3         ,
   tmp4         => tmp4         ,
   dy           => dy           ,
   cmd_addr_rd  => cmd_addr_rd  ,
   cmd_fgm      => cmd_fgm      ,
   	da1_data     =>  da1_data ,
	da2_data     =>  da2_data ,
	da3_data     =>  da3_data ,
	da4_data     =>  da4_data ,
	
--    power_state  =>	work_state,
   power_state =>	pack_state,
	off_cmd   => off_cmd        ,
	
    s_data       => s_data_in,
    ms_data      => ms_data_in,
    time_set     => time_set,
    mode_change  => mode_change,
    cmd_mode     => cmd_mode,
    da_change    => da_change,
	da_data      => da_change_data  ,
	fgm_chanel_data   =>  fgm_chanel_data     ,
    fgm_change   =>  fgm_change     ,
--   lc_change    => lc_change,
	
--    cmd_count_right => cmd_count_right,
    cmd_count_error => cmd_count_error,
    cmd_error_type  => cmd_error_type,
    cmd_count_lx    => cmd_count_lx,
	cmd_count_time  => cmd_count_time   ,
    cmd_count_sz    => cmd_count_sz   ,
	cmd_sz_errcnt   =>  cmd_sz_errcnt  ,
    cmd_sz_errtype  =>  cmd_sz_errtype ,
	
--    cmd_last        => cmd_last,
--    cmd_sz_code     => cmd_sz_code,
    cmd_sz_data     => cmd_sz_data
    );

 u_DA_PROCESS: DA_PROCESS 
 PORT MAP (
          clk => clk,
          reset => reset_n,
          da_clk => clk1MHz,
		  da_change=>da_change,
		  da_data_in=>da_change_data,
		  s_edge =>clk_s_edge,
		  
--need change		  
		  mode   =>work_state(2),
		  init_da=>init_da,
		  
		   da1_data  =>  da1_data ,
	       da2_data  =>  da2_data ,
	       da3_data  =>  da3_data ,
	       da4_data  =>  da4_data ,

          SC_CS1   => DA_CS1,
		  SC_CS2   => DA_CS2,
		  SC_CS3   => DA_CS3,
		  SC_CS4   => DA_CS4,
          SC_DATA  => DA_DATA,
          SC_CLK   => DA_CLK
        );
		
	U_LC_PROCESS:LC_PROCESS
    PORT MAP (
          clk => clk,
          reset => reset_n,	
		  init_lc=>init_lc,
		  
		  lc_state  => work_state(0)  ,
		  off_cmd   => off_cmd        ,
		  ms_edge   => clk_ms_edge    ,

		  LC_RST1   => LC_RST1,
		  LC_EN1    => LC_EN1 

		);
		
U_FGM_PROCESS:FGM_PROCESS
    PORT MAP (
	clk			 =>  clk		  ,
	reset 		 =>  reset_n 		  ,
	init_fgm     =>  init_fgm     ,
				
    fgm_data_in	 =>  fgm_chanel_data  ,
	fgm_change   =>  fgm_change   ,
	s_edge       =>  clk_s_edge       ,
	ms_edge      =>  clk_ms_edge      ,
	fgm_sample_state =>  fgm_sample_state       ,
	

	fgm_chanel_out=>fgm_chanel    ,
	fgm_change_out=>fgm_chanel_change,
	
    fgm_state        =>  fgm_state        ,
    fgm_state_change =>  fgm_state_change ,
	
	fgm_addr_out     =>   fgm_addr_out    ,	
	FGM_1        =>  FGM_1        ,  
	FGM_2        =>  FGM_2        ,  
	FGM_3        =>  FGM_3        ,  
	FGM_4        =>  FGM_4       
);


	
		
		

end Behavioral;