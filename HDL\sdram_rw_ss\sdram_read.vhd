----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    17:58:54 13/02/2017 
-- Design Name: 
-- Module Name:    sdram_read - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;
use work.pkg_ce5.all;

--Uncomment the following library declaration if instantiating
--any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity sdram_read is
  port(
    rst : in std_logic;
    clk : in std_logic; 
    read_en : in std_logic;
    start_addr_rd : in std_logic_vector(24 downto 0);
    read_length_rd : in std_logic_vector(10 downto 0);
    read_sdram_end : out std_logic;
    sd_addr : out std_logic_vector(24 downto 0);
    sd_command : out std_logic_vector(2 downto 0);
    sd_read_length : out std_logic_vector(10 downto 0)
  );
end sdram_read;

architecture Behavioral of sdram_read is

constant start_end_add_time : integer := 12;
--*********************************************************************************************
--SIGNAL DECLARATION
--*********************************************************************************************
  signal read_length : std_logic_vector(10 downto 0);
  signal start_addr : std_logic_vector(24 downto 0);
  signal counter_read_cntrl : std_logic_vector(10 downto 0);
  signal read_cycle_length : std_logic_vector(10 downto 0);
  signal addr_rd : std_logic_vector(9 downto 0);
--*********************************************************************************************
--LOGIC BODY
--*********************************************************************************************
begin
  
  sd_read_length <= read_length;
  
  process(clk,rst)
  begin
    if rst = '0' then
      read_length <= (others => '0');
      start_addr <= (others => '0');
    elsif rising_edge(clk) then
      if read_en = '1' then
        read_length <= read_length_rd;
        start_addr <= start_addr_rd;
      else 
        read_length <= read_length;
        start_addr <= start_addr;
      end if;
    end if;
  end process;
  
  process(clk,rst)
  begin
    if rst = '0' then
      read_cycle_length <= (others => '0');
    elsif rising_edge(clk) then
      read_cycle_length <= read_length + start_end_add_time;----3 + 9 + read_length_rd - 1 + 12 = read_length_rd - 23
    end if;
  end process;
   
  process(clk,rst)
  begin
    if rst = '0' then
      counter_read_cntrl <= (others => '0');
    elsif rising_edge(clk) then
      if read_en = '1' then
        counter_read_cntrl <= counter_read_cntrl + '1';
      elsif counter_read_cntrl >= 1 and counter_read_cntrl < read_cycle_length then
        counter_read_cntrl <= counter_read_cntrl + '1';
      else
        counter_read_cntrl <= (others => '0');
      end if;
    end if;
  end process;
  
  process(clk,rst)
  begin 
    if rst = '0' then
      sd_addr <= (others => '0');
      sd_command <= NOP_COMMAND;
      addr_rd <= (others => '0');
    elsif rising_edge(clk) then
      if counter_read_cntrl < 4 then
        sd_addr<= (others => '0');
        sd_command <= NOP_COMMAND;
        addr_rd <= (others => '0');
      elsif counter_read_cntrl >= 4 and counter_read_cntrl < (read_length + 3) then
        addr_rd <= addr_rd + '1';
        sd_command <= READ_COMMAND;
--        if addr_rd(1 downto 0) = "00" then
          sd_addr <= start_addr + ("000" & X"000" &  addr_rd);
--        end if;
      elsif counter_read_cntrl = (read_length + 3) then
        sd_command <= READ_COMMAND;
        addr_rd <= (others => '0');
		  sd_addr <= start_addr + ("000" & X"000" &  addr_rd);
      elsif counter_read_cntrl >= (read_length + 4) and counter_read_cntrl <= (read_length + start_end_add_time) then
        sd_command <= READ_COMMAND;
        addr_rd <= (others => '0');
--      elsif counter_read_cntrl <= (read_length + start_end_add_time) then
--        sd_command <= REFRESH_COMMAND;
--        addr_rd <= (others => '0');     
      else
        sd_addr <= (others => '0');
        sd_command <= NOP_COMMAND;
        addr_rd <= (others => '0');
      end if;
    end if;
  end process;
  
  process(clk,rst)
  begin
    if rst = '0' then
      read_sdram_end <= '0';
    elsif rising_edge(clk) then
      if counter_read_cntrl >= (read_length + start_end_add_time) then
        read_sdram_end <= '1';
      else
        read_sdram_end <= '0';
      end if;
    end if;
  end process;

end Behavioral;

