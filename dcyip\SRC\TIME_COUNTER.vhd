----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    14:16:03 09/15/2021 
-- Design Name: 
-- Module Name:    time_counter - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

---- Uncomment the following library declaration if instantiating
---- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity TIME_COUNTER is
Port ( 
    clk : in  STD_LOGIC;
    reset : in  STD_LOGIC;
    
	ms_edge    :in std_logic;
--	start_edge :in std_logic;
	s_data_out :out std_logic_vector(31 downto 0);
	ms_data_out:out std_logic_vector(15 downto 0);
--	clk_s_edge :out std_logic;
	
	time_set   : in std_logic;
	s_data_in  : in std_logic_vector(31 downto 0);
    ms_data_in : in std_logic_vector(15 downto 0)

);
end TIME_COUNTER;

architecture Behavioral of TIME_COUNTER is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal s_cnt: std_logic_vector(31 downto 0);
signal ms_cnt : std_logic_vector(15 downto 0);
--signal s_edge: std_logic;
--signal s_edge_reg: std_logic;

begin
--clk_s_edge<=s_edge;
 -- process(clk,reset)
    -- begin
        -- if reset='0' then
            -- ms_data_out<=(others=>'0');
			-- s_data_out <=(others=>'0');
        -- else
            -- if clk'event and clk='1' then
			    -- if start_edge ='1' then
				    -- ms_data_out<=ms_cnt;
			        -- s_data_out <=s_cnt;
				-- end if;
			-- end if;
		-- end if;
	-- end process;
	
    ms_data_out<=ms_cnt;
    s_data_out <=s_cnt;
	
    -- process(clk,reset)
    -- begin
        -- if reset='0' then
            -- s_edge<='0';
        -- else
            -- if clk'event and clk='1' then
			    -- if ms_cnt >=999 and ms_edge ='1' then 
				    -- s_edge<='1';
				-- else
				    -- s_edge<='0';
				-- end if;
			-- end if;
		-- end if;
	-- end process;
	
	
	process(clk,reset)
    begin
        if reset='0' then
            ms_cnt<=(others=>'0');
			s_cnt  <=(others=>'0');
        else
            if clk'event and clk='1' then
			    if time_set ='1'and  ms_data_in<=999 then
					ms_cnt <=ms_data_in;
					s_cnt  <=s_data_in;
			    else
                    if ms_edge ='1' then
                        if ms_cnt >=999 then
                            ms_cnt <=(others=>'0');
							if s_cnt =x"FFFFFFFF" then 
							    s_cnt <=(others=>'0');
							else
				    		    s_cnt  <=s_cnt+1;
							end if;
                        else
                            ms_cnt <= ms_cnt+1;
                        end if;
					end if;
                end if;
            end if;
        end if;
    end process;
	

end Behavioral;

