----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    14:07:07 08/29/2023 
-- Design Name: 
-- Module Name:    FGM_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity FGM_PROCESS is
Port (
	clk			: in std_logic;
	reset 		: in std_logic;
    init_fgm: in std_logic;

    fgm_data_in	: in std_logic_vector(47 downto 0);
	fgm_change  : in std_logic;
	s_edge      : in std_logic;
	ms_edge     : in std_logic;	
	fgm_sample_state  :in std_logic;
	
	fgm_chanel_out: out std_logic_vector(7 downto 0);
	fgm_change_out:out std_logic;
	
	fgm_state:   out std_logic_vector(3 downto 0);
    fgm_state_change:	out std_logic;
	
	FGM_1      :out std_logic;
	FGM_2      :out std_logic;
	FGM_3      :out std_logic;
	FGM_4      :out std_logic;
	fgm_addr_out   :out std_logic_vector(7 downto 0)
);

end FGM_PROCESS;

architecture Behavioral of FGM_PROCESS is

attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

signal fgm_chanel  :  std_logic_vector(7 downto 0);
signal fgm_chanel_change  :  std_logic;	




component FMG_MODE is
port (
    reset   : in std_logic;
    clk     : in std_logic;
	init_fgm: in std_logic;
	
    fgm_data_in	: in std_logic_vector(47 downto 0);
	fgm_change  : in std_logic;
	s_edge      : in std_logic;
	
	fgm_chanel  : out std_logic_vector(7 downto 0); --探头0，1，2，3
	fgm_chanel_change:out std_logic	;
	
    fgm_state:   out std_logic_vector(3 downto 0);
    fgm_state_change:	out std_logic	
);

end component;

component FGM_ADDR is
Port (
	clk			: in std_logic;
	reset 		: in std_logic;

	ms_edge     : in std_logic;	
	fgm_sample_state  : in std_logic;	
	fgm_chanel  : in std_logic_vector(7 downto 0);
    fgm_chanel_change  : in std_logic;	

    FGM_1      :out std_logic;
	FGM_2      :out std_logic;
	FGM_3      :out std_logic;
	FGM_4      :out std_logic;
	fgm_addr_out   :out std_logic_vector(7 downto 0)
);
end component;


begin

fgm_chanel_out<=fgm_chanel;
fgm_change_out<=fgm_chanel_change;

U_FMG_MODE:FMG_MODE
port map(
    reset            =>  reset             ,
    clk              =>  clk               ,
	
	init_fgm	     =>  init_fgm	       ,					
    fgm_data_in      =>  fgm_data_in       ,
	fgm_change       =>  fgm_change        ,
	s_edge           =>  s_edge            ,
	
	fgm_state           =>   fgm_state           ,
    fgm_state_change    =>   fgm_state_change    ,
										 
	fgm_chanel       =>  fgm_chanel        ,
	fgm_chanel_change=>fgm_chanel_change  
);

u_FGM_ADDR :FGM_ADDR
Port map(
	clk			        =>   clk			     ,  
	reset 		        =>   reset 		         ,
		
	ms_edge             =>   ms_edge             ,
	fgm_sample_state    =>   fgm_sample_state    ,
	fgm_chanel          =>   fgm_chanel          ,
    fgm_chanel_change   =>   fgm_chanel_change   ,
	

	fgm_addr_out            =>   fgm_addr_out            ,				
    FGM_1               =>   FGM_1               ,
	FGM_2               =>   FGM_2               ,
	FGM_3               =>   FGM_3               ,
	FGM_4               =>   FGM_4               
);



end Behavioral;

