----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    11:24:37 10/24/2022 
-- Design Name: 
-- Module Name:    DATA_PROCESS - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity DATA_PROCESS is
port (
    clk : in std_logic;
    reset : in std_logic;
	l_en  : in std_logic;
	l_en_1: in std_logic;
   
	read_start: in std_logic;
	
	start_edge:in std_logic;
	time_edge1 :in std_logic;
	time_edge2 :in std_logic;
	time_edge3 :in std_logic;
	fgm_chanel  : in std_logic_vector(7 downto 0); 
	
	read_over: in std_logic;
	fgm_rd_over: in std_logic;
	gc_rd_over:  in std_logic;	

	data_in1:  in std_logic_vector(15 downto 0);
	data_in2:  in std_logic_vector(15 downto 0);
	data_in3:  in std_logic_vector(15 downto 0);
	data_in4:  in std_logic_vector(15 downto 0);
	data_in5:  in std_logic_vector(15 downto 0);
	fgm_data:  in std_logic_vector(15 downto 0);
	gc_data:  in std_logic_vector(15 downto 0);
	
	sample_end1 : out std_logic;  	
	ram_dout1   : out std_logic_vector (7 downto 0);
	addr_rd1    : in  std_logic_vector(7 downto 0);
	
--	sample_end2 : out std_logic;
	ram_dout2   : out std_logic_vector (7 downto 0);
	addr_rd2    : in  std_logic_vector(7 downto 0);
	
--	sample_end3 : out std_logic;
	ram_dout3   : out std_logic_vector (7 downto 0);
	addr_rd3    : in  std_logic_vector(7 downto 0);
	
--	sample_end4 : out std_logic;
	ram_dout4   : out std_logic_vector (7 downto 0);
	addr_rd4    : in  std_logic_vector(7 downto 0);
	
--	sample_end5 : out std_logic;
	ram_dout5   : out std_logic_vector (7 downto 0);
	addr_rd5    : in  std_logic_vector(7 downto 0);
	
--	sample_end6 : out std_logic;
	ram_dout6   : out std_logic_vector (7 downto 0);
	addr_rd6    : in  std_logic_vector(9 downto 0);
	
--	sample_end7 : out std_logic;
	ram_dout7   : out std_logic_vector (7 downto 0);
	addr_rd7    : in  std_logic_vector(7 downto 0);
--ADG508	
	-- FGM_SW_ADDR :out std_logic_vector(1 downto 0);
	-- EFI_SW_ADDR :out std_logic_vector(2 downto 0);
--CMD
    data_out_lx1  :out std_logic_vector(7 downto 0);
	data_out_lx2  :out std_logic_vector(7 downto 0);
	data_out_lx3  :out std_logic_vector(7 downto 0);
	data_out_lx4  :out std_logic_vector(7 downto 0);
	data_out_lx5  :out std_logic_vector(7 downto 0);
    tmp1          :out std_logic_vector(7 downto 0);
	tmp2          :out std_logic_vector(7 downto 0);
	tmp3          :out std_logic_vector(7 downto 0);
	tmp4          :out std_logic_vector(7 downto 0);
	dy            :out std_logic_vector(7 downto 0);
	cmd_addr_rd   :in  std_logic_vector (9 downto 0);
	cmd_fgm       :out std_logic_vector(7 downto 0)
		
);
end DATA_PROCESS;

architecture Behavioral of DATA_PROCESS is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";

-- signal data_out1:std_logic_vector(7 downto 0);
-- signal addr_wr1 :std_logic_VECTOR(7 downto 0);
-- signal wr1      :std_logic; 
-- signal data_out2:std_logic_vector(7 downto 0);
-- signal addr_wr2 :std_logic_VECTOR(7 downto 0);
-- signal wr2      :std_logic; 
-- signal data_out3:std_logic_vector(7 downto 0);
-- signal addr_wr3 :std_logic_VECTOR(7 downto 0);
-- signal wr3      :std_logic; 
-- signal data_out4:std_logic_vector(7 downto 0);
-- signal addr_wr4 :std_logic_VECTOR(7 downto 0);
-- signal wr4      :std_logic; 
-- signal data_out5:std_logic_vector(7 downto 0);
-- signal addr_wr5 :std_logic_VECTOR(7 downto 0);
-- signal wr5      :std_logic; 
-- signal data_out6:std_logic_vector(7 downto 0);
-- signal addr_wr6 :std_logic_VECTOR(9 downto 0);
-- signal wr6      :std_logic; 
-- signal data_out7:std_logic_vector(7 downto 0);
-- signal addr_wr7 :std_logic_VECTOR(7 downto 0);
-- signal wr7      :std_logic; 

-- signal wr1_ram:std_logic_VECTOR(0 downto 0);
-- signal wr2_ram:std_logic_VECTOR(0 downto 0);
-- signal wr3_ram:std_logic_VECTOR(0 downto 0);
-- signal wr4_ram:std_logic_VECTOR(0 downto 0);
-- signal wr5_ram:std_logic_VECTOR(0 downto 0);
-- signal wr6_ram:std_logic_VECTOR(0 downto 0);
-- signal wr7_ram:std_logic_VECTOR(0 downto 0);

component H_DATA_PROCESS is
port (
    clk : in std_logic;
    reset : in std_logic;
   
	read_start: in std_logic;
	
	start_edge:in std_logic;
	time_edge1 :in std_logic;
	time_edge2 :in std_logic;
	time_edge3 :in std_logic;
	fgm_chanel  : in std_logic_vector(7 downto 0); 	
	
	read_over: in std_logic;
	fgm_rd_over: in std_logic;
	gc_rd_over:  in std_logic;	

	data_in1:  in std_logic_vector(15 downto 0);
	data_in2:  in std_logic_vector(15 downto 0);
	data_in3:  in std_logic_vector(15 downto 0);
	data_in4:  in std_logic_vector(15 downto 0);
	data_in5:  in std_logic_vector(15 downto 0);
	fgm_data:  in std_logic_vector(15 downto 0);
	gc_data:  in std_logic_vector(15 downto 0);
	
	sample_end1 : out std_logic;  	
	ram_dout1   : out std_logic_vector (7 downto 0);
	addr_rd1    : in  std_logic_vector(7 downto 0);
	
--	sample_end2 : out std_logic;
	ram_dout2   : out std_logic_vector (7 downto 0);
	addr_rd2    : in  std_logic_vector(7 downto 0);
	
--	sample_end3 : out std_logic;
	ram_dout3   : out std_logic_vector (7 downto 0);
	addr_rd3    : in  std_logic_vector(7 downto 0);
	
--	sample_end4 : out std_logic;
	ram_dout4   : out std_logic_vector (7 downto 0);
	addr_rd4    : in  std_logic_vector(7 downto 0);
	
--	sample_end5 : out std_logic;
	ram_dout5   : out std_logic_vector (7 downto 0);
	addr_rd5    : in  std_logic_vector(7 downto 0);
	
--	sample_end6 : out std_logic;
	ram_dout6   : out std_logic_vector (7 downto 0);
	addr_rd6    : in  std_logic_vector(9 downto 0);
	
--	sample_end7 : out std_logic;
	ram_dout7   : out std_logic_vector (7 downto 0);
	addr_rd7    : in  std_logic_vector(7 downto 0);

--CMD
    data_out_lx1  :out std_logic_vector(7 downto 0);
	data_out_lx2  :out std_logic_vector(7 downto 0);
	data_out_lx3  :out std_logic_vector(7 downto 0);
	data_out_lx4  :out std_logic_vector(7 downto 0);
	data_out_lx5  :out std_logic_vector(7 downto 0);
    tmp1          :out std_logic_vector(7 downto 0);
	tmp2          :out std_logic_vector(7 downto 0);
	tmp3          :out std_logic_vector(7 downto 0);
	tmp4          :out std_logic_vector(7 downto 0);
	dy            :out std_logic_vector(7 downto 0);
	cmd_addr_rd   :in  std_logic_vector (9 downto 0);
	cmd_fgm       :out std_logic_vector(7 downto 0)
		
);
end component;

component L_DATA_PROCESS is
port (
    clk : in std_logic;
    reset : in std_logic;
   
	read_start: in std_logic;
	
	start_edge:in std_logic;
	time_edge1 :in std_logic;
	time_edge2 :in std_logic;
	time_edge3 :in std_logic;
	fgm_chanel  : in std_logic_vector(7 downto 0); 	
	
	read_over: in std_logic;
	fgm_rd_over: in std_logic;
	gc_rd_over:  in std_logic;	

	data_in1:  in std_logic_vector(15 downto 0);
	data_in2:  in std_logic_vector(15 downto 0);
	data_in3:  in std_logic_vector(15 downto 0);
	data_in4:  in std_logic_vector(15 downto 0);
	data_in5:  in std_logic_vector(15 downto 0);
	fgm_data:  in std_logic_vector(15 downto 0);
	gc_data:  in std_logic_vector(15 downto 0);
	
	sample_end1 : out std_logic;  	
	ram_dout1   : out std_logic_vector (7 downto 0);
	addr_rd1    : in  std_logic_vector(7 downto 0);
	
--	sample_end2 : out std_logic;
	ram_dout2   : out std_logic_vector (7 downto 0);
	addr_rd2    : in  std_logic_vector(7 downto 0);
	
--	sample_end3 : out std_logic;
	ram_dout3   : out std_logic_vector (7 downto 0);
	addr_rd3    : in  std_logic_vector(7 downto 0);
	
--	sample_end4 : out std_logic;
	ram_dout4   : out std_logic_vector (7 downto 0);
	addr_rd4    : in  std_logic_vector(7 downto 0);
	
--	sample_end5 : out std_logic;
	ram_dout5   : out std_logic_vector (7 downto 0);
	addr_rd5    : in  std_logic_vector(7 downto 0);
	
--	sample_end6 : out std_logic;
	ram_dout6   : out std_logic_vector (7 downto 0);
	addr_rd6    : in  std_logic_vector(9 downto 0);
	
--	sample_end7 : out std_logic;
	ram_dout7   : out std_logic_vector (7 downto 0);
	addr_rd7    : in  std_logic_vector(7 downto 0);

--CMD
    data_out_lx1  :out std_logic_vector(7 downto 0);
	data_out_lx2  :out std_logic_vector(7 downto 0);
	data_out_lx3  :out std_logic_vector(7 downto 0);
	data_out_lx4  :out std_logic_vector(7 downto 0);
	data_out_lx5  :out std_logic_vector(7 downto 0);
    tmp1          :out std_logic_vector(7 downto 0);
	tmp2          :out std_logic_vector(7 downto 0);
	tmp3          :out std_logic_vector(7 downto 0);
	tmp4          :out std_logic_vector(7 downto 0);
	dy            :out std_logic_vector(7 downto 0);
	cmd_addr_rd   :in  std_logic_vector (9 downto 0);
	cmd_fgm       :out std_logic_vector(7 downto 0)
		
);
end component;

signal h_data_out_lx1  :std_logic_vector(7 downto 0);
signal h_data_out_lx2  :std_logic_vector(7 downto 0);
signal h_data_out_lx3  :std_logic_vector(7 downto 0);
signal h_data_out_lx4  :std_logic_vector(7 downto 0);
signal h_data_out_lx5  :std_logic_vector(7 downto 0);
signal h_tmp1          :std_logic_vector(7 downto 0);
signal h_tmp2          :std_logic_vector(7 downto 0);
signal h_tmp3          :std_logic_vector(7 downto 0);
signal h_tmp4          :std_logic_vector(7 downto 0);
signal h_dy            :std_logic_vector(7 downto 0);
signal h_cmd_fgm       :std_logic_vector(7 downto 0);
signal h_sample_end1   :std_logic;  	
signal h_ram_dout1     :std_logic_vector (7 downto 0);
signal h_sample_end2   :std_logic;
signal h_ram_dout2     :std_logic_vector (7 downto 0);
signal h_sample_end3   :std_logic;
signal h_ram_dout3     :std_logic_vector (7 downto 0);
signal h_sample_end4   :std_logic;
signal h_ram_dout4     :std_logic_vector (7 downto 0);
signal h_sample_end5   :std_logic;
signal h_ram_dout5     :std_logic_vector (7 downto 0);
signal h_sample_end6   :std_logic;
signal h_ram_dout6     :std_logic_vector (7 downto 0);
--signal h_sample_end7   :std_logic;
signal h_ram_dout7     : std_logic_vector (7 downto 0);
signal l_data_out_lx1  :std_logic_vector(7 downto 0);
signal l_data_out_lx2  :std_logic_vector(7 downto 0);
signal l_data_out_lx3  :std_logic_vector(7 downto 0);
signal l_data_out_lx4  :std_logic_vector(7 downto 0);
signal l_data_out_lx5  :std_logic_vector(7 downto 0);
signal l_tmp1          :std_logic_vector(7 downto 0);
signal l_tmp2          :std_logic_vector(7 downto 0);
signal l_tmp3          :std_logic_vector(7 downto 0);
signal l_tmp4          :std_logic_vector(7 downto 0);
signal l_dy            :std_logic_vector(7 downto 0);
signal l_cmd_fgm       :std_logic_vector(7 downto 0);
signal l_sample_end1   :std_logic;  	
signal l_ram_dout1     :std_logic_vector (7 downto 0);
signal l_sample_end2   :std_logic;
signal l_ram_dout2     :std_logic_vector (7 downto 0);
signal l_sample_end3   :std_logic;
signal l_ram_dout3     :std_logic_vector (7 downto 0);
signal l_sample_end4   :std_logic;
signal l_ram_dout4     :std_logic_vector (7 downto 0);
signal l_sample_end5   :std_logic;
signal l_ram_dout5     :std_logic_vector (7 downto 0);
signal l_sample_end6   :std_logic;
signal l_ram_dout6     :std_logic_vector (7 downto 0);
--signal l_sample_end7   :std_logic;
signal l_ram_dout7     : std_logic_vector (7 downto 0);
	
begin


U_H_DATA_PROCESS:H_DATA_PROCESS
port map(
    clk     =>  clk  ,
    reset   =>  reset,   
	
	read_start  => read_start  ,	
		
	start_edge  => start_edge   ,
	time_edge1  => time_edge1   , 
	time_edge2  => time_edge2   ,
	time_edge3  => time_edge3   ,
	fgm_chanel  => fgm_chanel   ,
	
	read_over    =>  read_over    ,
	fgm_rd_over  =>  fgm_rd_over  ,
	gc_rd_over   =>  gc_rd_over   ,
	
	data_in1     =>  data_in1     ,
	data_in2     =>  data_in2     ,
	data_in3     =>  data_in3     ,
	data_in4     =>  data_in4     ,
	data_in5     =>  data_in5     ,
	fgm_data     =>  fgm_data     ,
	gc_data     =>  gc_data     ,
		
	sample_end1 => h_sample_end1 ,
	ram_dout1   => h_ram_dout1   ,
	addr_rd1    => addr_rd1    ,
                   
--	sample_end2 => h_sample_end2 ,
	ram_dout2   => h_ram_dout2   ,
	addr_rd2    => addr_rd2    ,
		           
--	sample_end3 => h_sample_end3 ,
	ram_dout3   => h_ram_dout3   ,
	addr_rd3    => addr_rd3    ,
		           
--	sample_end4 => h_sample_end4 ,
	ram_dout4   => h_ram_dout4   ,
	addr_rd4    => addr_rd4    ,
			       
--	sample_end5 => h_sample_end5 ,
	ram_dout5   => h_ram_dout5   ,
	addr_rd5    => addr_rd5    ,
			       
--	sample_end6 => h_sample_end6 ,
	ram_dout6   => h_ram_dout6   ,
	addr_rd6    => addr_rd6    ,
				   
--	sample_end7 => h_sample_end7 ,
	ram_dout7   => h_ram_dout7   ,
	addr_rd7    => addr_rd7      ,

   data_out_lx1 => h_data_out_lx1 ,
   data_out_lx2 => h_data_out_lx2 ,
   data_out_lx3 => h_data_out_lx3 ,
   data_out_lx4 => h_data_out_lx4 ,
   data_out_lx5 => h_data_out_lx5 ,
   tmp1         => h_tmp1         ,
   tmp2         => h_tmp2         ,
   tmp3         => h_tmp3         ,
   tmp4         => h_tmp4         ,
   dy           => h_dy           ,
   cmd_addr_rd  => cmd_addr_rd  ,
   cmd_fgm      => h_cmd_fgm      

);

U_L_DATA_PROCESS:L_DATA_PROCESS
port map(
    clk     =>  clk  ,
    reset   =>  reset,   
	
	read_start  => read_start  ,	
		
	start_edge  => start_edge   ,
	time_edge1  => time_edge1   , 
	time_edge2  => time_edge2   ,
	time_edge3  => time_edge3   ,
	fgm_chanel  => fgm_chanel   ,
	
	read_over    =>  read_over    ,
	fgm_rd_over  =>  fgm_rd_over  ,
	gc_rd_over   =>  gc_rd_over   ,
	
	data_in1     =>  data_in1     ,
	data_in2     =>  data_in2     ,
	data_in3     =>  data_in3     ,
	data_in4     =>  data_in4     ,
	data_in5     =>  data_in5     ,
	fgm_data     =>  fgm_data     ,
	gc_data     =>  gc_data     , 
	
 	-- data_in1     =>  x"1111"     ,
	-- data_in2     =>  x"2222"     ,
	-- data_in3     =>  x"3333"     ,
	-- data_in4     =>  x"4444"     ,
	-- data_in5     =>  x"5555"     ,
	-- fgm_data     =>  x"6666"     ,
	-- gc_data     =>  x"7777"     ,  
		
	sample_end1 => l_sample_end1 ,
	ram_dout1   => l_ram_dout1   ,
	addr_rd1    => addr_rd1    ,

--	sample_end2 => l_sample_end2 ,
	ram_dout2   => l_ram_dout2   ,
	addr_rd2    => addr_rd2    ,
		
--	sample_end3 => l_sample_end3 ,
	ram_dout3   => l_ram_dout3   ,
	addr_rd3    => addr_rd3    ,
		
--	sample_end4 => l_sample_end4 ,
	ram_dout4   => l_ram_dout4   ,
	addr_rd4    => addr_rd4    ,
			
--	sample_end5 => l_sample_end5 ,
	ram_dout5   => l_ram_dout5   ,
	addr_rd5    => addr_rd5    ,
			
--	sample_end6 => l_sample_end6 ,
	ram_dout6   => l_ram_dout6   ,
	addr_rd6    => addr_rd6    ,
				
--	sample_end7 => l_sample_end7 ,
	ram_dout7   => l_ram_dout7   ,
	addr_rd7    => addr_rd7      ,

   data_out_lx1 => l_data_out_lx1 ,
   data_out_lx2 => l_data_out_lx2 ,
   data_out_lx3 => l_data_out_lx3 ,
   data_out_lx4 => l_data_out_lx4 ,
   data_out_lx5 => l_data_out_lx5 ,
   tmp1         => l_tmp1         ,
   tmp2         => l_tmp2         ,
   tmp3         => l_tmp3         ,
   tmp4         => l_tmp4         ,
   dy           => l_dy           ,
   cmd_addr_rd  => cmd_addr_rd    ,
   cmd_fgm      => l_cmd_fgm      

);
process(reset,clk)
	begin
		if reset='0' then	
		    sample_end1<='0';
			-- sample_end2<='0';
			-- sample_end3<='0';
			-- sample_end4<='0';
			-- sample_end5<='0';
--			 sample_end6<='0';
			-- sample_end7<='0';
			ram_dout1<=(others=>'0');
			ram_dout2<=(others=>'0');
			ram_dout3<=(others=>'0');
			ram_dout4<=(others=>'0');
			ram_dout5<=(others=>'0');
			ram_dout6<=(others=>'0');
			ram_dout7<=(others=>'0');
			data_out_lx1<=(others=>'0');  
			data_out_lx2<=(others=>'0');  
			data_out_lx3<=(others=>'0');  
			data_out_lx4<=(others=>'0');  
			data_out_lx5<=(others=>'0');  
			tmp1        <=(others=>'0');  
			tmp2        <=(others=>'0');  
			tmp3        <=(others=>'0');  
			tmp4        <=(others=>'0');  
			dy          <=(others=>'0');   
--			cmd_fgm     <=(others=>'0');  
		else 
		    if clk ='1' and clk'event then
                if l_en='0' then 
				    sample_end1 <=  l_sample_end1  ;
			        -- sample_end2 <=  l_sample_end2  ;
			        -- sample_end3 <=  l_sample_end3  ;
			        -- sample_end4 <=  l_sample_end4  ;
			        -- sample_end5 <=  l_sample_end5  ;
--			         sample_end6 <=  l_sample_end6  ;
			        -- sample_end7 <=  l_sample_end7  ;
			        ram_dout1   <=  l_ram_dout1    ;
			        ram_dout2   <=  l_ram_dout2    ;
			        ram_dout3   <=  l_ram_dout3    ;
			        ram_dout4   <=  l_ram_dout4    ;
			        ram_dout5   <=  l_ram_dout5    ;
			        ram_dout6   <=  l_ram_dout6    ;
			        ram_dout7   <=  l_ram_dout7    ;
			        data_out_lx1<=  l_data_out_lx1  ;
			        data_out_lx2<=  l_data_out_lx2  ;
			        data_out_lx3<=  l_data_out_lx3  ;
			        data_out_lx4<=  l_data_out_lx4  ;
			        data_out_lx5<=  l_data_out_lx5  ;
			        tmp1        <=  l_tmp1         ;
			        tmp2        <=  l_tmp2         ;
			        tmp3        <=  l_tmp3         ;
			        tmp4        <=  l_tmp4         ;
			        dy          <=  l_dy           ;
--			        cmd_fgm     <=  l_cmd_fgm      ;

				else
				   	sample_end1 <=  h_sample_end1  ;
			        -- sample_end2 <=  h_sample_end2  ;
			        -- sample_end3 <=  h_sample_end3  ;
			        -- sample_end4 <=  h_sample_end4  ;
			        -- sample_end5 <=  h_sample_end5  ;
--			         sample_end6 <=  h_sample_end6  ;
			        -- sample_end7 <=  h_sample_end7  ;
			        ram_dout1   <=  h_ram_dout1    ;
			        ram_dout2   <=  h_ram_dout2    ;
			        ram_dout3   <=  h_ram_dout3    ;
			        ram_dout4   <=  h_ram_dout4    ;
			        ram_dout5   <=  h_ram_dout5    ;
			        ram_dout6   <=  h_ram_dout6    ;
			        ram_dout7   <=  h_ram_dout7    ;
			        data_out_lx1<=  h_data_out_lx1  ;
			        data_out_lx2<=  h_data_out_lx2  ;
			        data_out_lx3<=  h_data_out_lx3  ;
			        data_out_lx4<=  h_data_out_lx4  ;
			        data_out_lx5<=  h_data_out_lx5  ;
			        tmp1        <=  h_tmp1         ;
			        tmp2        <=  h_tmp2         ;
			        tmp3        <=  h_tmp3         ;
			        tmp4        <=  h_tmp4         ;
			        dy          <=  h_dy           ;
--			        cmd_fgm     <=  h_cmd_fgm      ;
                end if;
            end if; 
        end if;
    end process;	



process(reset,clk)
	begin
		if reset='0' then	  
			cmd_fgm     <=(others=>'0');  
		else 
		    if clk ='1' and clk'event then
                if l_en_1='0' then 			
			        cmd_fgm     <=  l_cmd_fgm      ;
				else				   
			        cmd_fgm     <=  h_cmd_fgm      ;
                end if;
            end if; 
        end if;
    end process;		

end Behavioral;

