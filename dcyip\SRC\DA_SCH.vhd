----------------------------------------------------------------------------------
-- Company: 
-- Engineer: 
-- 
-- Create Date:    10:29:50 11/14/2022 
-- Design Name: 
-- Module Name:    DA_SCH - Behavioral 
-- Project Name: 
-- Target Devices: 
-- Tool versions: 
-- Description: 
--
-- Dependencies: 
--
-- Revision: 
-- Revision 0.01 - File Created
-- Additional Comments: 
--
----------------------------------------------------------------------------------
library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.STD_LOGIC_ARITH.ALL;
use IEEE.STD_LOGIC_UNSIGNED.ALL;

-- Uncomment the following library declaration if using
-- arithmetic functions with Signed or Unsigned values
--use IEEE.NUMERIC_STD.ALL;

-- Uncomment the following library declaration if instantiating
-- any Xilinx primitives in this code.
--library UNISIM;
--use UNISIM.VComponents.all;

entity DA_SCH is
Port ( 	
		clk			: in std_logic;
		reset 		: in std_logic;
		mode        : in std_logic;
		
		scan_send_mark1  : in std_logic;
	    scan_send_mark2  : in std_logic;
	    scan_send_mark3  : in std_logic;
	    scan_send_mark4  : in std_logic;	
        scan_da_out      : in std_logic_vector(15 downto 0);
        scan_da_out1      :in std_logic_vector(15 downto 0);
        scan_da_out2      :in std_logic_vector(15 downto 0);
        scan_da_out3      :in std_logic_vector(15 downto 0);
        scan_da_out4      :in std_logic_vector(15 downto 0);
		
		normal_send_mark1  : in std_logic;
	    normal_send_mark2  : in std_logic;
	    normal_send_mark3  : in std_logic;
	    normal_send_mark4  : in std_logic;	
        normal_da_out      : in std_logic_vector(15 downto 0);
		normal_da_out1      :in std_logic_vector(15 downto 0);
        normal_da_out2      :in std_logic_vector(15 downto 0);
        normal_da_out3      :in std_logic_vector(15 downto 0);
        normal_da_out4      :in std_logic_vector(15 downto 0);
        		
		send_mark1  : out std_logic;
	    send_mark2  : out std_logic;
	    send_mark3  : out std_logic;
	    send_mark4  : out std_logic;	
        da_out      : out std_logic_vector(15 downto 0);
		da_out1     : out std_logic_vector(15 downto 0);
        da_out2     : out std_logic_vector(15 downto 0);
        da_out3     : out std_logic_vector(15 downto 0);
        da_out4     : out std_logic_vector(15 downto 0)
);		
	
end DA_SCH;

architecture Behavioral of DA_SCH is
attribute keep_hierarchy : string;
attribute keep_hierarchy of Behavioral : architecture is "no";
begin
	process(reset,clk)
	begin
		if reset ='0' then
			send_mark1<='0';send_mark2<='0';send_mark3<='0';send_mark4<='0';da_out<=(others=>'0');	
			da_out1<=(others=>'0');da_out2<=(others=>'0');da_out3<=(others=>'0');da_out4<=(others=>'0');
		else
            if clk='1' and clk'event then
			    if mode='1'  then 
				    send_mark1<=scan_send_mark1; 
                    send_mark2<=scan_send_mark2;
                    send_mark3<=scan_send_mark3;
                    send_mark4<=scan_send_mark4;
					da_out    <=scan_da_out;
					da_out1(15 downto 12)<=x"0";  da_out1(11 downto 0)<= scan_da_out1(11 downto 0);
					da_out2(15 downto 12)<=x"0";  da_out2(11 downto 0)<= scan_da_out2(11 downto 0);
					da_out3(15 downto 12)<=x"0";  da_out3(11 downto 0)<= scan_da_out3(11 downto 0);
					da_out4(15 downto 12)<=x"0";  da_out4(11 downto 0)<= scan_da_out4(11 downto 0);
                    -- da_out2  <= scan_da_out2;
                    -- da_out3  <= scan_da_out3;
                    -- da_out4  <= scan_da_out4;					
				else
				    send_mark1<=normal_send_mark1;
				    send_mark2<=normal_send_mark2;
				    send_mark3<=normal_send_mark3;
				    send_mark4<=normal_send_mark4;
					da_out    <=normal_da_out;
					da_out1(15 downto 12)<=x"0";  da_out1(11 downto 0)<= normal_da_out1(11 downto 0);
					da_out2(15 downto 12)<=x"0";  da_out2(11 downto 0)<= normal_da_out2(11 downto 0);
					da_out3(15 downto 12)<=x"0";  da_out3(11 downto 0)<= normal_da_out3(11 downto 0);
					da_out4(15 downto 12)<=x"0";  da_out4(11 downto 0)<= normal_da_out4(11 downto 0);
					-- da_out1  <=normal_da_out1;
                    -- da_out2  <=normal_da_out2;
                    -- da_out3  <=normal_da_out3;
                    -- da_out4  <=normal_da_out4;
				end if;
			end if;
		end if;
	end process;
				   
end Behavioral;				   